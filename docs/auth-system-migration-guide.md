# 用户状态管理和权限检查系统迁移指南

本文档提供了从旧的状态机管理系统迁移到新的简化认证系统的详细步骤。

## 迁移步骤

### 1. 准备工作

在开始迁移之前，请确保：

1. 备份当前代码
2. 创建一个新的分支进行迁移工作
3. 确保所有测试都通过

```bash
# 创建迁移分支
git checkout -b auth-system-refactoring
```

### 2. 替换文件

#### 2.1 复制新文件

将以下新文件复制到项目中：

- `frontend/src/auth/contexts/simplified-auth-context.tsx`
- `frontend/src/auth/hooks/simplified-permissions.ts`
- `frontend/src/auth/components/simplified-permission-gate.tsx`
- `frontend/src/components/auth/simplified-route-guard.tsx`
- `frontend/src/auth/core/simplified-token-manager.ts`
- `frontend/src/auth/services/simplified-auth-api.ts`
- `frontend/src/config/simplified-routes.ts`
- `frontend/src/middleware-simplified.ts`

#### 2.2 重命名文件

将新文件重命名为原始文件名：

```bash
# 重命名上下文
mv frontend/src/auth/contexts/simplified-auth-context.tsx frontend/src/auth/contexts/auth-context.tsx

# 重命名权限Hook
mv frontend/src/auth/hooks/simplified-permissions.ts frontend/src/auth/hooks/use-permissions.ts

# 重命名权限组件
mv frontend/src/auth/components/simplified-permission-gate.tsx frontend/src/auth/components/permission-gate.tsx

# 重命名路由守卫
mv frontend/src/components/auth/simplified-route-guard.tsx frontend/src/components/auth/route-guard.tsx

# 重命名Token管理器
mv frontend/src/auth/core/simplified-token-manager.ts frontend/src/auth/core/token-manager.ts

# 重命名API服务
mv frontend/src/auth/services/simplified-auth-api.ts frontend/src/auth/services/auth-api.ts

# 重命名路由配置
mv frontend/src/config/simplified-routes.ts frontend/src/config/routes.ts

# 重命名中间件
mv frontend/src/middleware-simplified.ts frontend/src/middleware.ts
```

### 3. 更新导入路径

由于我们重命名了文件，需要更新所有导入路径。

#### 3.1 更新Auth Context导入

```tsx
// 旧版本
import { useAuth } from '@/auth/hooks/use-auth'

// 新版本
import { useAuth } from '@/auth/contexts/auth-context'
```

#### 3.2 更新useUserRole导入

```tsx
// 旧版本
import { useUserRole } from '@/auth/hooks/use-auth'

// 新版本
import { useUserRole } from '@/auth/contexts/auth-context'
```

### 4. 删除不再需要的文件

以下文件在新系统中不再需要，可以删除：

```bash
# 删除旧的状态机
rm frontend/src/auth/core/auth-state-machine.ts

# 删除旧的认证规则配置
rm frontend/src/auth/config/auth-rules.ts

# 删除旧的认证类型定义
rm frontend/src/types/auth-rules.ts

# 删除旧的Auth Hook
rm frontend/src/auth/hooks/use-auth.ts
```

### 5. 更新应用入口

更新应用入口文件，使用新的Auth Provider：

```tsx
// frontend/src/app/providers.tsx
import { AuthProvider } from '@/auth/contexts/auth-context'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      {/* 其他Provider */}
      {children}
    </AuthProvider>
  )
}
```

## 测试迁移

### 1. 基本功能测试

迁移完成后，测试以下基本功能：

- 用户登录
- 用户注册
- 用户登出
- 获取当前用户信息
- 管理员登录

### 2. 权限测试

测试以下权限相关功能：

- 公开路由访问
- 需要认证的路由访问
- 管理员路由访问
- 权限组件显示/隐藏

### 3. 路由守卫测试

测试以下路由守卫功能：

- 未登录访问需要认证的路由
- 已登录访问登录页
- 普通用户访问管理员路由

## 回滚计划

如果迁移过程中遇到问题，可以使用以下步骤回滚：

```bash
# 放弃当前更改
git reset --hard

# 切回主分支
git checkout main
```

## 常见问题

### 1. 登录后无法获取用户信息

检查 `getCurrentUser` API 是否正常工作，以及 token 是否正确存储。

### 2. 权限检查不正确

检查 `usePermissions` hook 中的权限配置是否正确。

### 3. 路由守卫不工作

检查 `RouteGuard` 组件是否正确集成到应用中。

## 后续优化

完成基本迁移后，可以考虑以下优化：

1. 使用 React Query 进一步简化 API 调用
2. 实现更细粒度的权限控制
3. 添加更多单元测试
4. 优化权限检查性能
