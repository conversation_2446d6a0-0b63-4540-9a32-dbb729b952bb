# 企业AI交互式学习课堂

## 项目简介
本项目是一个现代化的企业AI交互式学习平台，旨在为企业提供智能化的员工培训解决方案。系统集成了课程管理、学习追踪、智能辅导等功能，为企业培训提供全方位的支持。

## 主要功能
- 智能课程内容管理
- 个性化学习路径
- 学习进度追踪
- 互动式学习功能
- AI 图像生成练习
- AI 对话练习
- 员工能力评估
- 培训效果分析

## 技术架构

### 前端技术栈
- React 18
- TypeScript
- Next.js 13 (App Router)
- Tailwind CSS
- Shadcn/ui

### 后端技术栈
- Node.js
- NestJS
- TypeScript
- PostgreSQL
- Redis
- Docker

### AI 服务集成
- 阿里云 DashScope
  - 文本生成
  - 图像生成
- 未来计划
  - 语音识别
  - 视频分析

## 系统架构

### 核心模块
1. 用户管理
   - 用户认证
   - 权限控制
   - 角色管理

2. 课程管理
   - 课程创建和编辑
   - 课程内容管理
   - 学习进度追踪

3. AI 互动
   - AI 对话练习
   - AI 图像生成
   - 实时反馈

4. 数据存储
   - PostgreSQL: 结构化数据
   - Redis: 缓存和会话
   - 阿里云 OSS: 媒体文件存储

## 部署指南

### 环境要求
- Node.js >= 18.0.0
- PNPM >= 8.0.0
- Docker & Docker Compose
- PostgreSQL
- Redis

### Docker 部署步骤

1. 克隆项目
```bash
git clone [项目地址]
cd enterprise-ai-classroom
```

2. 配置环境变量
```bash
# 后端配置
cp backend/.env.example backend/.env.production
# 编辑 backend/.env.production 设置必要的环境变量

# 前端配置
cp frontend/.env.example frontend/.env.production
# 编辑 frontend/.env.production 设置必要的环境变量
```

3. 构建和启动服务
```bash
# 构建 Docker 镜像
docker compose build

# 启动所有服务
docker compose up -d
```

4. 验证部署
```bash
# 检查服务状态
docker compose ps

# 查看日志
docker compose logs -f
```

### 手动部署步骤

1. 安装依赖
```bash
pnpm install
```

2. 构建项目
```bash
# 前端构建
cd frontend
pnpm build

# 后端构建
cd backend
pnpm build
```

3. 启动服务
```bash
# 前端
cd frontend
pnpm start

# 后端
cd backend
pnpm start:prod
```

## 项目结构
```
.
├── frontend/          # 前端项目
│   ├── src/          # 源代码
│   ├── public/       # 静态资源
│   └── Dockerfile    # 前端 Docker 配置
│
├── backend/          # 后端项目
│   ├── src/         # 源代码
│   ├── uploads/     # 上传文件目录
│   └── Dockerfile   # 后端 Docker 配置
│
├── nginx/           # Nginx 配置
└── docker-compose.yml  # Docker 编排配置
```

## 开发指南

### 本地开发
```bash
# 启动所有服务
pnpm dev

# 或分别启动前后端
cd frontend && pnpm dev
cd backend && pnpm dev
```

### 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式

## 维护指南

### 数据备份
- 定期备份 PostgreSQL 数据库
- 备份上传的媒体文件
- 保存环境配置文件

### 监控和日志
- 使用 Docker 日志管理
- 监控服务器资源使用
- 跟踪 API 调用情况

### 更新部署
```bash
# 拉取最新代码
git pull

# 重新构建和启动
docker compose up -d --build
```

## 贡献指南
欢迎提交 Issue 和 Pull Request

## 许可证
MIT License 
