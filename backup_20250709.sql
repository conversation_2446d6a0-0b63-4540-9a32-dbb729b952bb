--
-- PostgreSQL database dump
--

-- Dumped from database version 14.13 (Homebrew)
-- Dumped by pg_dump version 14.13 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: ai_providers_defaulttype_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.ai_providers_defaulttype_enum AS ENUM (
    'text',
    'image'
);


ALTER TYPE public.ai_providers_defaulttype_enum OWNER TO soulflare_clare;

--
-- Name: ai_providers_status_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.ai_providers_status_enum AS ENUM (
    'active',
    'inactive',
    'testing'
);


ALTER TYPE public.ai_providers_status_enum OWNER TO soulflare_clare;

--
-- Name: ai_providers_type_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.ai_providers_type_enum AS ENUM (
    'openai',
    'azure_openai',
    'anthropic',
    'moonshot',
    'gemini',
    'volcengine',
    'mistral',
    'deepseek',
    'flux',
    'siliconflow',
    'custom'
);


ALTER TYPE public.ai_providers_type_enum OWNER TO soulflare_clare;

--
-- Name: courses_level_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.courses_level_enum AS ENUM (
    '情绪疏导',
    '关系咨询',
    '个人成长'
);


ALTER TYPE public.courses_level_enum OWNER TO soulflare_clare;

--
-- Name: orders_order_type_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.orders_order_type_enum AS ENUM (
    'course'
);


ALTER TYPE public.orders_order_type_enum OWNER TO soulflare_clare;

--
-- Name: orders_status_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.orders_status_enum AS ENUM (
    'pending',
    'paid',
    'cancelled',
    'refunded',
    'expired'
);


ALTER TYPE public.orders_status_enum OWNER TO soulflare_clare;

--
-- Name: payments_status_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.payments_status_enum AS ENUM (
    'pending',
    'success',
    'failed',
    'refunded'
);


ALTER TYPE public.payments_status_enum OWNER TO soulflare_clare;

--
-- Name: refunds_status_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.refunds_status_enum AS ENUM (
    'pending',
    'success',
    'failed'
);


ALTER TYPE public.refunds_status_enum OWNER TO soulflare_clare;

--
-- Name: tool_category_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.tool_category_enum AS ENUM (
    '页面制作',
    'SEO优化',
    '设计优化',
    '数据分析',
    '开发工具'
);


ALTER TYPE public.tool_category_enum OWNER TO soulflare_clare;

--
-- Name: tool_difficulty_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.tool_difficulty_enum AS ENUM (
    '初级',
    '中级',
    '高级'
);


ALTER TYPE public.tool_difficulty_enum OWNER TO soulflare_clare;

--
-- Name: users_role_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.users_role_enum AS ENUM (
    'user',
    'admin'
);


ALTER TYPE public.users_role_enum OWNER TO soulflare_clare;

--
-- Name: users_status_enum; Type: TYPE; Schema: public; Owner: soulflare_clare
--

CREATE TYPE public.users_status_enum AS ENUM (
    'active',
    'inactive'
);


ALTER TYPE public.users_status_enum OWNER TO soulflare_clare;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ai_providers; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.ai_providers (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying NOT NULL,
    type public.ai_providers_type_enum NOT NULL,
    status public.ai_providers_status_enum DEFAULT 'active'::public.ai_providers_status_enum NOT NULL,
    "apiKey" character varying NOT NULL,
    "secretKey" character varying,
    "apiEndpoint" character varying,
    "requestCount" integer DEFAULT 0 NOT NULL,
    config jsonb,
    "costAmount" numeric(10,2) DEFAULT '0'::numeric NOT NULL,
    "defaultType" public.ai_providers_defaulttype_enum,
    "lastTestAt" timestamp without time zone,
    "lastTestResult" jsonb,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.ai_providers OWNER TO soulflare_clare;

--
-- Name: building_tools; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.building_tools (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying NOT NULL,
    title character varying NOT NULL,
    description text NOT NULL,
    icon character varying NOT NULL,
    url character varying NOT NULL,
    category public.tool_category_enum NOT NULL,
    difficulty public.tool_difficulty_enum NOT NULL,
    "estimatedTime" character varying NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "sortOrder" integer DEFAULT 0 NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.building_tools OWNER TO soulflare_clare;

--
-- Name: catalogue; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.catalogue (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.catalogue OWNER TO soulflare_clare;

--
-- Name: course_building_tools; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.course_building_tools (
    tool_id uuid NOT NULL,
    course_id uuid NOT NULL
);


ALTER TABLE public.course_building_tools OWNER TO soulflare_clare;

--
-- Name: course_lessons; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.course_lessons (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    course_id uuid NOT NULL,
    title character varying NOT NULL,
    "order" integer NOT NULL,
    duration character varying NOT NULL,
    description text NOT NULL,
    script_path character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.course_lessons OWNER TO soulflare_clare;

--
-- Name: courses; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.courses (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying NOT NULL,
    title character varying NOT NULL,
    description text NOT NULL,
    duration character varying NOT NULL,
    lessons_count integer NOT NULL,
    price numeric(10,2) NOT NULL,
    is_published boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    level public.courses_level_enum DEFAULT '情绪疏导'::public.courses_level_enum NOT NULL,
    category_id uuid,
    catalogue_id uuid
);


ALTER TABLE public.courses OWNER TO soulflare_clare;

--
-- Name: gift_codes; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.gift_codes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    used_by uuid,
    course_id uuid NOT NULL,
    valid_days integer NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    "isUsed" boolean DEFAULT false NOT NULL,
    "usedAt" timestamp without time zone,
    "expiresAt" timestamp without time zone NOT NULL,
    code character varying NOT NULL,
    type character varying(10) DEFAULT 'gift'::character varying NOT NULL,
    creator_id uuid,
    friend_name character varying(100),
    invite_reason text,
    user_name character varying(100),
    profession character varying(100),
    ai_experience character varying(50),
    interests text,
    expectation text
);


ALTER TABLE public.gift_codes OWNER TO soulflare_clare;

--
-- Name: migrations; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    "timestamp" bigint NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.migrations OWNER TO soulflare_clare;

--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: soulflare_clare
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.migrations_id_seq OWNER TO soulflare_clare;

--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: soulflare_clare
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: orders; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.orders (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    order_no character varying(32) NOT NULL,
    user_id uuid NOT NULL,
    order_type public.orders_order_type_enum NOT NULL,
    product_name character varying NOT NULL,
    amount numeric(10,2) NOT NULL,
    payment_method character varying,
    transaction_id character varying,
    status public.orders_status_enum DEFAULT 'pending'::public.orders_status_enum NOT NULL,
    metadata jsonb,
    paid_at timestamp without time zone,
    expires_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    course_id uuid
);


ALTER TABLE public.orders OWNER TO soulflare_clare;

--
-- Name: payments; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.payments (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    order_id uuid NOT NULL,
    payment_method character varying NOT NULL,
    transaction_id character varying,
    amount numeric(10,2) NOT NULL,
    status public.payments_status_enum DEFAULT 'pending'::public.payments_status_enum NOT NULL,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.payments OWNER TO soulflare_clare;

--
-- Name: refunds; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.refunds (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    order_id uuid NOT NULL,
    refund_no character varying(32) NOT NULL,
    amount numeric(10,2) NOT NULL,
    status public.refunds_status_enum DEFAULT 'pending'::public.refunds_status_enum NOT NULL,
    reason text,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.refunds OWNER TO soulflare_clare;

--
-- Name: subscriptions; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.subscriptions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    userid uuid NOT NULL,
    planname character varying NOT NULL,
    price numeric(10,2) NOT NULL,
    startdate timestamp without time zone NOT NULL,
    enddate timestamp without time zone NOT NULL,
    aicalllimit integer NOT NULL,
    aicallused integer DEFAULT 0 NOT NULL,
    isactive boolean DEFAULT true NOT NULL,
    createdat timestamp without time zone DEFAULT now() NOT NULL,
    updatedat timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.subscriptions OWNER TO soulflare_clare;

--
-- Name: user_courses; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.user_courses (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    course_id uuid NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    completed_lessons jsonb,
    purchased_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    last_visited_lesson_id character varying
);


ALTER TABLE public.user_courses OWNER TO soulflare_clare;

--
-- Name: users; Type: TABLE; Schema: public; Owner: soulflare_clare
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying NOT NULL,
    password character varying NOT NULL,
    name character varying,
    avatar character varying,
    role public.users_role_enum DEFAULT 'user'::public.users_role_enum NOT NULL,
    status public.users_status_enum DEFAULT 'active'::public.users_status_enum NOT NULL,
    "loginCount" integer DEFAULT 0 NOT NULL,
    "lastLoginAt" timestamp without time zone,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.users OWNER TO soulflare_clare;

--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Data for Name: ai_providers; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.ai_providers (id, name, type, status, "apiKey", "secretKey", "apiEndpoint", "requestCount", config, "costAmount", "defaultType", "lastTestAt", "lastTestResult", "createdAt", "updatedAt") FROM stdin;
7cfb7859-a7f3-448a-81b3-cd95cd0614e6	flx	flux	active	sk-e995b02b7dac420d8051982399bb535e	\N	https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis	0	\N	0.00	image	\N	\N	2025-04-28 16:05:51.000453	2025-04-28 16:07:28.067626
f7c16bd7-ed14-4e1f-a040-6552637775c9	deepseek	deepseek	active	***********************************	\N	https://api.deepseek.com/v1	0	\N	0.00	text	\N	\N	2025-04-28 16:06:52.483477	2025-04-28 16:07:33.86002
\.


--
-- Data for Name: building_tools; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.building_tools (id, code, title, description, icon, url, category, difficulty, "estimatedTime", "isActive", "sortOrder", created_at, updated_at) FROM stdin;
c361e18b-64c7-4a7c-97c6-464d15bf3ca1	seo-optimizer	SEO优化工具	智能分析网站SEO表现，提供优化建议和关键词策略	Wrench	#	SEO优化	中级	10分钟	t	2	2025-07-04 16:37:52.081873	2025-07-04 16:37:52.081873
c54e0c48-e226-4dab-ab4e-d8bc232bd11a	responsive-checker	响应式设计检查	一键检查网站在不同设备上的显示效果，确保完美适配	Wrench	#	设计优化	初级	3分钟	t	3	2025-07-04 16:37:52.081873	2025-07-04 16:37:52.081873
\.


--
-- Data for Name: catalogue; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.catalogue (id, name, description, created_at, updated_at) FROM stdin;
eef97720-d76c-4504-a89b-099cd976654c	情感疗愈	情感问题	2025-07-04 16:42:21.228129	2025-07-04 16:42:21.228129
\.


--
-- Data for Name: course_building_tools; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.course_building_tools (tool_id, course_id) FROM stdin;
\.


--
-- Data for Name: course_lessons; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.course_lessons (id, course_id, title, "order", duration, description, script_path, created_at, updated_at) FROM stdin;
d49c46d2-7426-4944-ac2d-bf565b7d7ced	c1aaf1b9-7893-482c-92cc-40e60d074538	先聊聊自己	1	15分钟	先来聊聊自己	love_1/lesson_1_script.md	2025-04-28 16:04:38.768865	2025-04-28 16:04:38.768865
49a5ea52-fe8a-4244-9fbc-c3953d03ce65	c1aaf1b9-7893-482c-92cc-40e60d074538	关于过去	2	30分钟	讨论过去	love_1/lesson_2_script.md	2025-07-02 16:16:03.061424	2025-07-02 16:16:03.061424
\.


--
-- Data for Name: courses; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.courses (id, code, title, description, duration, lessons_count, price, is_published, created_at, updated_at, level, category_id, catalogue_id) FROM stdin;
c1aaf1b9-7893-482c-92cc-40e60d074538	love_1	我花了很多时间想你，直到我想明白我自己	关于爱和成长的对话	30	2	0.00	t	2025-04-28 16:03:01.587928	2025-07-04 17:31:10.231181	情绪疏导	\N	eef97720-d76c-4504-a89b-099cd976654c
\.


--
-- Data for Name: gift_codes; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.gift_codes (id, created_at, used_by, course_id, valid_days, updated_at, "isUsed", "usedAt", "expiresAt", code, type, creator_id, friend_name, invite_reason, user_name, profession, ai_experience, interests, expectation) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.migrations (id, "timestamp", name) FROM stdin;
1	1739005993344	InitialSchema1739005993344
2	1739008538701	CreateCoursesTables1739008538701
3	1739008694895	InsertCourses1739008694895
4	1739014492987	InsertAdminUser1739014492987
5	1739017038931	UpdateGiftCodeTable1739017038931
6	1739017530741	AddGiftCodeFields1739017530741
7	1739017531000	AddUniqueCourseLesson1739017531000
8	1740208777508	UpdateCourseLevelEnum1740208777508
9	1740208777509	AddInviteCodeFields1740208777509
10	1745737562150	AddCourseIdToOrder1745737562150
11	1745737562151	CreatePaymentAndRefundTables1745737562151
12	1740208777509	UpdateCourseLevelToVarchar1740208777509
13	1740208777510	UpdateCourseLevelEnumToHealingTypes1740208777510
20	1746000000000	CreateCatalogueTable1746000000000
21	1746000000001	AddCategoryToCourse1746000000001
22	1750143262273	CreateBuildingToolsTables1750143262273
23	1751619435112	AddCatalogueIdToCourses1751619435112
24	1751619435113	InsertInitialCatalogues1751619435113
\.


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.orders (id, order_no, user_id, order_type, product_name, amount, payment_method, transaction_id, status, metadata, paid_at, expires_at, created_at, updated_at, course_id) FROM stdin;
de344dce-5c4a-4c0a-95d3-231be66f2e64	087fde7f5829539644861	087fde7f-8587-4a16-97f6-f15872cbf5fd	course	我花了很多时间想你，直到我想明白我自己	19.00	\N	\N	pending	{"courseId": "c1aaf1b9-7893-482c-92cc-40e60d074538"}	\N	2025-04-28 17:08:59.644	2025-04-28 16:38:59.649585	2025-04-28 16:38:59.649585	c1aaf1b9-7893-482c-92cc-40e60d074538
0cec85ed-af18-4886-9a71-c74c2084baaa	087fde7f5829650134888	087fde7f-8587-4a16-97f6-f15872cbf5fd	course	我花了很多时间想你，直到我想明白我自己	19.00	\N	\N	pending	{"courseId": "c1aaf1b9-7893-482c-92cc-40e60d074538"}	\N	2025-04-28 17:10:50.134	2025-04-28 16:40:50.134828	2025-04-28 16:40:50.134828	c1aaf1b9-7893-482c-92cc-40e60d074538
f119c91b-dec3-4658-8211-0e951e5b032e	087fde7f5907897691990	087fde7f-8587-4a16-97f6-f15872cbf5fd	course	我花了很多时间想你，直到我想明白我自己	19.00	\N	\N	pending	{"courseId": "c1aaf1b9-7893-482c-92cc-40e60d074538"}	\N	2025-04-29 14:54:57.692	2025-04-29 14:24:57.693894	2025-04-29 14:24:57.693894	c1aaf1b9-7893-482c-92cc-40e60d074538
\.


--
-- Data for Name: payments; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.payments (id, order_id, payment_method, transaction_id, amount, status, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: refunds; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.refunds (id, order_id, refund_no, amount, status, reason, metadata, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: subscriptions; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.subscriptions (id, userid, planname, price, startdate, enddate, aicalllimit, aicallused, isactive, createdat, updatedat) FROM stdin;
\.


--
-- Data for Name: user_courses; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.user_courses (id, user_id, course_id, progress, completed_lessons, purchased_at, updated_at, last_visited_lesson_id) FROM stdin;
bd6c5a62-ea08-42cc-8671-4d54aeb4b3ee	087fde7f-8587-4a16-97f6-f15872cbf5fd	c1aaf1b9-7893-482c-92cc-40e60d074538	50	["d49c46d2-7426-4944-ac2d-bf565b7d7ced"]	2025-07-02 16:13:27.095586	2025-07-03 13:54:08.316968	d49c46d2-7426-4944-ac2d-bf565b7d7ced
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: soulflare_clare
--

COPY public.users (id, email, password, name, avatar, role, status, "loginCount", "lastLoginAt", "createdAt", "updatedAt") FROM stdin;
087fde7f-8587-4a16-97f6-f15872cbf5fd	<EMAIL>	$2b$10$bA08G7QlR17VTQwNLNPghu6zH5qpL.yHa7ygQmzcR4SsseSCd.V3.	admin	\N	admin	active	0	\N	2025-04-28 14:57:00.107061	2025-04-28 14:57:00.107061
\.


--
-- Name: migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: soulflare_clare
--

SELECT pg_catalog.setval('public.migrations_id_seq', 24, true);


--
-- Name: orders PK_710e2d4957aa5878dfe94e4ac2f; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY (id);


--
-- Name: migrations PK_8c82d7f526340ab734260ea46be; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY (id);


--
-- Name: gift_codes PK_a18bf56cdcac2e27f2d22bc2f69; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.gift_codes
    ADD CONSTRAINT "PK_a18bf56cdcac2e27f2d22bc2f69" PRIMARY KEY (id);


--
-- Name: users PK_a3ffb1c0c8416b9fc6f907b7433; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY (id);


--
-- Name: subscriptions PK_a87248d73155605cf782be9ee5e; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT "PK_a87248d73155605cf782be9ee5e" PRIMARY KEY (id);


--
-- Name: building_tools PK_building_tools_id; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.building_tools
    ADD CONSTRAINT "PK_building_tools_id" PRIMARY KEY (id);


--
-- Name: catalogue PK_catalogue_id; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.catalogue
    ADD CONSTRAINT "PK_catalogue_id" PRIMARY KEY (id);


--
-- Name: course_building_tools PK_course_building_tools; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_building_tools
    ADD CONSTRAINT "PK_course_building_tools" PRIMARY KEY (tool_id, course_id);


--
-- Name: ai_providers PK_de28ebefc0fb425c37b27a4c0a7; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.ai_providers
    ADD CONSTRAINT "PK_de28ebefc0fb425c37b27a4c0a7" PRIMARY KEY (id);


--
-- Name: payments PK_payments_id; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "PK_payments_id" PRIMARY KEY (id);


--
-- Name: refunds PK_refunds_id; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.refunds
    ADD CONSTRAINT "PK_refunds_id" PRIMARY KEY (id);


--
-- Name: orders UQ_035026a83bef9740d7ad05df383; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT "UQ_035026a83bef9740d7ad05df383" UNIQUE (order_no);


--
-- Name: gift_codes UQ_2ab475f14dfa3594af8a80c2e9b; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.gift_codes
    ADD CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b" UNIQUE (code);


--
-- Name: users UQ_97672ac88f789774dd47f7c8be3; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE (email);


--
-- Name: building_tools UQ_building_tools_code; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.building_tools
    ADD CONSTRAINT "UQ_building_tools_code" UNIQUE (code);


--
-- Name: course_lessons UQ_course_lesson_order; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_lessons
    ADD CONSTRAINT "UQ_course_lesson_order" UNIQUE (course_id, "order");


--
-- Name: refunds UQ_refunds_refund_no; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.refunds
    ADD CONSTRAINT "UQ_refunds_refund_no" UNIQUE (refund_no);


--
-- Name: course_lessons course_lessons_pkey; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_lessons
    ADD CONSTRAINT course_lessons_pkey PRIMARY KEY (id);


--
-- Name: courses courses_code_key; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.courses
    ADD CONSTRAINT courses_code_key UNIQUE (code);


--
-- Name: courses courses_pkey; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.courses
    ADD CONSTRAINT courses_pkey PRIMARY KEY (id);


--
-- Name: user_courses user_courses_pkey; Type: CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.user_courses
    ADD CONSTRAINT user_courses_pkey PRIMARY KEY (id);


--
-- Name: IDX_course_building_tools_course_id; Type: INDEX; Schema: public; Owner: soulflare_clare
--

CREATE INDEX "IDX_course_building_tools_course_id" ON public.course_building_tools USING btree (course_id);


--
-- Name: IDX_course_building_tools_tool_id; Type: INDEX; Schema: public; Owner: soulflare_clare
--

CREATE INDEX "IDX_course_building_tools_tool_id" ON public.course_building_tools USING btree (tool_id);


--
-- Name: IDX_courses_category_id; Type: INDEX; Schema: public; Owner: soulflare_clare
--

CREATE INDEX "IDX_courses_category_id" ON public.courses USING btree (category_id);


--
-- Name: idx_user_course_unique; Type: INDEX; Schema: public; Owner: soulflare_clare
--

CREATE UNIQUE INDEX idx_user_course_unique ON public.user_courses USING btree (user_id, course_id);


--
-- Name: course_lessons FK_1bb754da7dd104c4a3beb9677c8; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_lessons
    ADD CONSTRAINT "FK_1bb754da7dd104c4a3beb9677c8" FOREIGN KEY (course_id) REFERENCES public.courses(id);


--
-- Name: gift_codes FK_4bd0232a6e77d9cbc5ca2859a12; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.gift_codes
    ADD CONSTRAINT "FK_4bd0232a6e77d9cbc5ca2859a12" FOREIGN KEY (course_id) REFERENCES public.courses(id);


--
-- Name: user_courses FK_7ecb10d15b858768c36d37727f9; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.user_courses
    ADD CONSTRAINT "FK_7ecb10d15b858768c36d37727f9" FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: course_building_tools FK_course_building_tools_course_id; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_building_tools
    ADD CONSTRAINT "FK_course_building_tools_course_id" FOREIGN KEY (course_id) REFERENCES public.courses(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: course_building_tools FK_course_building_tools_tool_id; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.course_building_tools
    ADD CONSTRAINT "FK_course_building_tools_tool_id" FOREIGN KEY (tool_id) REFERENCES public.building_tools(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: courses FK_courses_catalogue_id; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.courses
    ADD CONSTRAINT "FK_courses_catalogue_id" FOREIGN KEY (catalogue_id) REFERENCES public.catalogue(id) ON DELETE SET NULL;


--
-- Name: courses FK_courses_category_id; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.courses
    ADD CONSTRAINT "FK_courses_category_id" FOREIGN KEY (category_id) REFERENCES public.catalogue(id) ON DELETE SET NULL;


--
-- Name: user_courses FK_d65a2771413a10753d76937b3d6; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.user_courses
    ADD CONSTRAINT "FK_d65a2771413a10753d76937b3d6" FOREIGN KEY (course_id) REFERENCES public.courses(id);


--
-- Name: gift_codes FK_gift_codes_user; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.gift_codes
    ADD CONSTRAINT "FK_gift_codes_user" FOREIGN KEY (used_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: orders FK_orders_course_id; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT "FK_orders_course_id" FOREIGN KEY (course_id) REFERENCES public.courses(id);


--
-- Name: payments FK_payments_order; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "FK_payments_order" FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: refunds FK_refunds_order; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.refunds
    ADD CONSTRAINT "FK_refunds_order" FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE;


--
-- Name: gift_codes fk_gift_codes_creator; Type: FK CONSTRAINT; Schema: public; Owner: soulflare_clare
--

ALTER TABLE ONLY public.gift_codes
    ADD CONSTRAINT fk_gift_codes_creator FOREIGN KEY (creator_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- PostgreSQL database dump complete
--

