const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  poweredByHeader: false,
  reactStrictMode: true,
  
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },
  
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: '**',
      }
    ],
  },
  
  experimental: {
    optimizeCss: true
  },
  
  // 生产环境优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // 构建时忽略类型检查和 lint 以加快速度
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // webpack配置
  webpack: (config, { dev, isServer }) => {
    // 添加路径别名
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.join(__dirname, 'src'),
    };

    // 优化 chunk 加载
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          maxSize: 100000,
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name(module) {
                if (!module.context) {
                  return 'vendor';
                }
                
                const matches = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
                if (!matches) {
                  return 'vendor';
                }

                const packageName = matches[1];
                if (['antd', '@ant-design', 'react', 'react-dom'].some(pkg => 
                  packageName.startsWith(pkg)
                )) {
                  return `npm.${packageName.replace('@', '')}`;
                }
                
                return 'vendor';
              },
              priority: 20
            },
            common: {
              minChunks: 2,
              priority: 10,
              reuseExistingChunk: true
            }
          }
        },
        runtimeChunk: 'single'
      };
    }

    return config;
  },

  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
};

module.exports = nextConfig;
