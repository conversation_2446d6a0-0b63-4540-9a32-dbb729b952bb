# 企业AI课堂前端项目

## 项目结构

```
frontend/
├── src/                    # 源代码目录
│   ├── app/               # Next.js 13 App Router 页面
│   ├── components/        # React 组件
│   ├── lib/               # 工具函数和服务
│   ├── styles/            # 全局样式
│   └── types/             # TypeScript 类型定义
├── public/                # 静态资源
├── .env.example          # 环境变量示例
├── .env.production       # 生产环境配置
├── next.config.js        # Next.js 配置
├── tailwind.config.js    # Tailwind CSS 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 项目依赖和脚本
```

## 开发指南

### 环境准备
1. Node.js >= 18.0.0
2. PNPM >= 8.0.0

### 安装依赖
```bash
pnpm install
```

### 开发服务器
```bash
pnpm dev
```

### 构建
```bash
pnpm build
```

### 生产环境运行
```bash
pnpm start
```

## 技术栈
- React 18
- Next.js 13 (App Router)
- TypeScript
- Tailwind CSS
- Shadcn/ui

## 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式

## 目录说明

### `/src/app`
使用 Next.js 13 的 App Router 组织页面结构：
- `/admin` - 管理后台
- `/dashboard` - 用户仪表盘
- `/courses` - 课程相关页面
- `/auth` - 认证相关页面

### `/src/components`
- `ui/` - 基础 UI 组件
- `course/` - 课程相关组件
- `layout/` - 布局组件
- `shared/` - 共享组件

### `/src/lib`
- `api/` - API 请求封装
- `services/` - 业务服务
- `utils/` - 工具函数
- `hooks/` - 自定义 Hooks

## 环境变量
请参考 `.env.example` 文件设置必要的环境变量：
- `NEXT_PUBLIC_API_URL` - 后端 API 地址
- `NEXT_PUBLIC_APP_NAME` - 应用名称
- `NEXT_PUBLIC_APP_DESCRIPTION` - 应用描述

## Docker 部署
项目包含 Dockerfile，可以直接构建 Docker 镜像：
```bash
docker build -t ai-classroom-frontend .
``` 