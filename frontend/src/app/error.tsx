'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error(error)
  }, [error])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900">Something went wrong</h2>
        <p className="mt-2 text-gray-600">Sorry, there was a problem loading the page</p>
        <div className="mt-6">
          <Button
            onClick={reset}
            variant="outline"
          >
            Try again
          </Button>
        </div>
      </div>
    </div>
  )
} 