'use client'

import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useAuthNavigation } from '@/auth/hooks/use-auth-navigation'
import { LoginForm } from '@/components/auth/login-form'
import { AuthErrorReset } from '@/components/auth/auth-error-reset'
import { useToast } from '@/components/ui/use-toast'
import { tokenManager } from '@/auth/core/simplified-token-manager'

export default function LoginPage() {
  const searchParams = useSearchParams()
  const { login, error, isAuthenticated } = useAuth()
  const { toast } = useToast()
  const { 
    isLoading, 
    navigateAfterAuth 
  } = useAuthNavigation()
  
  // Check if in error state
  const isErrorState = !!error
  
  useEffect(() => {
    const returnUrl = searchParams.get('returnUrl')
    const success = searchParams.get('success')
    const token = searchParams.get('token')
    
    // Handle Google OAuth callback success
    if (success === 'true' && token) {
      // Store the token received from URL
      tokenManager.setToken(token);
      
      // 需要获取用户信息并存储
      const handleGoogleAuthSuccess = async () => {
        try {
          const { authApi } = await import('@/auth/services/simplified-auth-api');
          const user = await authApi.getCurrentUser();
          tokenManager.setUser(user);
          
          toast({
            title: 'Login Successful',
            description: 'Welcome back! You have logged in with Google.',
          })
          
          // Clean up URL by removing sensitive token parameter
          const url = new URL(window.location.href);
          url.searchParams.delete('token');
          window.history.replaceState({}, '', url.toString());
          
          // 触发认证上下文更新
          window.dispatchEvent(new CustomEvent('auth:success'));
          
          // 短暂延迟确保状态更新
          setTimeout(() => {
            navigateAfterAuth(returnUrl || '/dashboard')
          }, 100);
          
        } catch (error) {
          console.error('Google auth user fetch failed:', error);
          tokenManager.clear();
          toast({
            title: 'Login Failed',
            description: 'Failed to get user information. Please try again.',
            variant: 'destructive',
          })
        }
      };
      
      handleGoogleAuthSuccess();
      return
    } else if (success === 'true') {
      // Fallback for cookie-based auth
      toast({
        title: 'Login Successful',
        description: 'Welcome back! You have logged in with Google.',
      })
      // Redirect to dashboard or return URL
      navigateAfterAuth(returnUrl || '/dashboard')
      return
    }
    
    // If target is auth-rules page, redirect directly
    if (returnUrl === '/auth-rules') {
      window.location.href = '/auth-rules'
      return
    }

    if (isLoading) {
      console.log('[LoginPage] Loading, skip navigation')
      return
    }

    if (isAuthenticated) {
      console.log('[LoginPage] Preparing redirect', {
        returnUrl,
        targetUrl: returnUrl || '/dashboard',
        isClient: true,
        isLoading,
        currentTime: new Date().toISOString()
      })
      navigateAfterAuth(returnUrl || '/dashboard')
    }
  }, [isAuthenticated, isLoading, searchParams, navigateAfterAuth, toast])

  const handleLogin = async (values: { email: string; password: string }) => {
    try {
      console.log('[LoginPage] Starting login', {
        email: values.email,
        currentTime: new Date().toISOString()
      })
      
      await login(values.email, values.password)
      
      console.log('[LoginPage] Login successful, waiting for state update and navigation', {
        currentTime: new Date().toISOString()
      })

      toast({
        title: 'Login Successful',
        description: 'Welcome back!',
      })
    } catch (error) {
      console.error('[LoginPage] Login failed:', error)
      toast({
        title: 'Login Failed',
        description: error instanceof Error ? error.message : 'Please try again later',
        variant: 'destructive',
      })
      throw error
    }
  }
  
  // Handle state reset
  const handleStateReset = () => {
    console.log('[LoginPage] User manually reset auth state', {
      previousState: error,
      timestamp: new Date().toISOString()
    })
    
    toast({
      title: 'Auth State Reset',
      description: 'System will refresh in a few seconds...',
    })
  }

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          SoulTalkAI
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              "Let AI be your mental companion, accompanying you through every emotional moment."
            </p>
            <footer className="text-sm">SoulTalkAI Team</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Welcome Back
            </h1>
            <p className="text-sm text-muted-foreground">
              Sign in with your Google account for the best experience
            </p>
          </div>
          
          {/* Error state prompt and reset button */}
          {isErrorState && (
            <AuthErrorReset onReset={handleStateReset} />
          )}
          
          <LoginForm onSubmit={handleLogin} />
          <p className="px-8 text-center text-sm text-muted-foreground">
            Don't have an account?{' '}
            <a
              href="/register"
              className="underline underline-offset-4 hover:text-primary"
            >
              Sign up now
            </a>
          </p>
        </div>
      </div>
    </div>
  )
} 