'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseService } from '@/lib/services/course.service';
import { useToast } from '@/components/ui/use-toast';
import { Clock, BookOpen, CheckCircle } from 'lucide-react';
import { useAuth } from '@/auth/contexts/simplified-auth-context';
import Link from 'next/link';

export default function FreeEnrollPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
    
    const fetchCourse = async () => {
      try {
        const data = await CourseService.getCourse(courseId);
        
        console.log('Free course enrollment page - course data:', data);
        console.log('Free course enrollment page - course price:', data.price, typeof data.price);
        
        // If course price is not 0 or already purchased, redirect
        if (Number(data.price) !== 0) {
          router.replace(`/all-courses/${courseId}`);
          return;
        }
        
        if (data.isPurchased) {
          router.replace(`/my-courses/${courseId}`);
          return;
        }
        
        setCourse(data);
      } catch (err) {
        setError('Failed to load course information, please try again later');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId, router]);

  const handleEnroll = async () => {
    if (!course) return;
    
    if (!isAuthenticated) {
      toast({
        variant: 'destructive',
        title: 'Please Login First',
        description: 'Please login to your account to join courses'
      });
      
      // Save current page URL to return after login
      const returnUrl = encodeURIComponent(window.location.pathname);
      router.push(`/login?returnUrl=${returnUrl}`);
      return;
    }
    
    setEnrolling(true);
    try {
      await CourseService.enrollFreeCourse(courseId);
      setSuccess(true);
      
      toast({
        title: 'Enrollment Successful',
        description: `You have successfully enrolled in the course "${course.title}"`,
      });
      
      setTimeout(() => {
        router.push(`/my-courses/${courseId}`);
      }, 1500);
    } catch (err: any) {
      toast({
        variant: 'destructive',
        title: 'Enrollment Failed',
        description: err.response?.data?.message || 'Operation failed, please try again later',
      });
    } finally {
      setEnrolling(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || 'Course not found'}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <Card className="p-6">
          {!isClient ? (
            // 服务端渲染时显示占位内容
            <div className="min-h-[400px] flex justify-center items-center">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="mb-6">
                <h2 className="text-2xl font-bold mb-2">{course.title}</h2>
                <p className="text-muted-foreground">{course.description}</p>
              </div>

              <div className="flex items-center gap-6 mb-6 text-muted-foreground">
                <Badge variant="secondary">{course.level}</Badge>
                <Badge variant="default" className="bg-softPurple-500">Free</Badge>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{course.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  <span>{course.lessonsCount} 课时</span>
                </div>
              </div>

              <div className="border-t pt-6">
                {success ? (
                  <div className="text-center p-4">
                                  <CheckCircle className="h-16 w-16 text-softPurple-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-softPurple-700 mb-2">
                      Enrollment Successful!
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      You have successfully enrolled in this course. Redirecting to course learning page...
                    </p>
                  </div>
                ) : !isAuthenticated ? (
                  <div className="bg-blue-50 p-4 rounded-lg mb-6">
                    <h3 className="text-lg font-medium mb-2 text-blue-700">Please Login First</h3>
                    <p className="text-blue-600 mb-4">Please login to your account to join courses, so we can add the course to your learning list.</p>
                    <Button 
                      asChild
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Link href={`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`}>
                        Login Now
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="text-center mb-6">
                      <h3 className="text-lg font-medium mb-2">Join Free Course</h3>
                      <p className="text-muted-foreground">
                        Click the button below to join this course immediately, no payment or redemption required
                      </p>
                    </div>
                    
                    <Button
                      onClick={handleEnroll}
                      disabled={enrolling}
                      className="w-full bg-softPurple-600 hover:bg-softPurple-700 text-white"
                      size="lg"
                    >
                      {enrolling ? (
                        <>
                          <LoadingSpinner className="mr-2 h-4 w-4" />
                          Enrolling...
                        </>
                      ) : (
                        'Join Course Now'
                      )}
                    </Button>
                  </>
                )}
              </div>
            </>
          )}
        </Card>
      </div>
    </div>
  );
} 