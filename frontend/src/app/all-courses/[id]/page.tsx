'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen } from 'lucide-react';
import Link from 'next/link';
import SubscriptionCTA from '@/components/subscription/subscription-cta';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

export default function CoursePage() {
  const params = useParams();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasActiveSubscription } = useSubscriptionStatus();

  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        const [courseData, lessonsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCoursePublicLessons(courseId),
        ]);
        
        setCourse(courseData);
        setLessons(lessonsData);
      } catch (err) {
        setError('Failed to load course information, please try again later');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || 'Course not found'}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Course Header Information */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Badge variant="secondary">{course.level}</Badge>
            <Badge variant="default" className={`${
              Number(course.price) === 0 
                ? "bg-softPurple-500" 
                : "bg-softPurple-500"
            }`}>
              {Number(course.price) === 0 ? "Free" : `$${course.price}`}
            </Badge>
          </div>
          <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
          <p className="text-lg text-muted-foreground mb-6">{course.description}</p>
          <div className="flex items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>{course.lessonsCount} Lessons</span>
            </div>
          </div>
        </div>

        {/* Purchase Button */}
        <div className="mb-8">
          <Button 
            asChild 
            size="lg" 
            className={`w-full md:w-auto ${
              Number(course.price) === 0 
                ? "bg-softPurple-600 hover:bg-softPurple-700" 
                : ""
            }`}
          >
            <Link href={
              Number(course.price) === 0 
                ? `/all-courses/free-enroll/${course.id}` 
                : `/all-courses/purchase/${course.id}`
            }>
              {Number(course.price) === 0 ? "Start Free Trial" : "Start Healing"}
            </Link>
          </Button>
        </div>

        {/* Subscription CTA for non-subscribers on paid courses */}
        {!hasActiveSubscription && Number(course.price) > 0 && (
          <div className="mb-8">
            <SubscriptionCTA 
              variant="card" 
              context="course-detail"
              courseId={course.id}
              accessReason={course.accessReason}
            />
          </div>
        )}

        {/* Course Lessons List */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold mb-4">Course Lessons</h2>
          {lessons.map((lesson) => (
            <Card key={lesson.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">Lesson {lesson.order}</span>
                  </div>
                  <h3 className="text-lg font-medium mt-1">{lesson.title}</h3>
                  <p className="text-muted-foreground mt-1">{lesson.description}</p>
                  <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.duration}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
} 