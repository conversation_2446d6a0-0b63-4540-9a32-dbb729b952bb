'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { ChevronLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';
import { InteractiveLesson } from '@/components/course/interactive-lesson';
import { CourseStep } from '@/types/course';
import { parseMarkdownToSteps } from '@/lib/utils/markdown';
import SubscriptionCTA from '@/components/subscription/subscription-cta';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';

export default function LessonPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { hasActiveSubscription } = useSubscriptionStatus();
  const courseId = params.id as string;
  const lessonId = params.lessonId as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lesson, setLesson] = useState<CourseLesson | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [steps, setSteps] = useState<CourseStep[]>([]);

  useEffect(() => {
    const fetchLessonData = async () => {
      try {
        const [lessonData, courseData, lessonsData] = await Promise.all([
          CourseService.getLesson(lessonId),
          CourseService.getCourse(courseId),
          CourseService.getCourseLessons(courseId),
        ]);

        setLesson(lessonData);
        setCourse(courseData);
        setLessons(lessonsData);
        setSteps(parseMarkdownToSteps(lessonData.content));
      } catch (err) {
        setError('加载对话主题失败，请稍后重试');
        console.error('Failed to fetch lesson:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchLessonData();
  }, [courseId, lessonId]);

  const handleComplete = async () => {
    try {
      await CourseService.completeLesson(lessonId);
      toast({
        title: '对话完成',
        description: '您已成功完成本次对话主题',
      });

      // 查找下一个主题
      const currentIndex = lessons.findIndex((l) => l.id === lessonId);
      if (currentIndex < lessons.length - 1) {
        const nextLesson = lessons[currentIndex + 1];
        router.push(`/all-courses/${courseId}/lessons/${nextLesson.id}`);
      } else {
        router.push(`/all-courses/${courseId}`);
      }
    } catch (err) {
      toast({
        title: '操作失败',
        description: '完成对话主题时发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course || !lesson) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '对话主题不存在'}
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href={`/all-courses/${courseId}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            返回对话
          </Link>
        </Button>
      </div>

      <InteractiveLesson
        title={lesson.title}
        steps={steps}
        onComplete={handleComplete}
        hasNextLesson={lessons.findIndex((l) => l.id === lessonId) < lessons.length - 1}
      />

      {/* Subscription CTA popup for non-subscribers during learning */}
      {!hasActiveSubscription && (
        <SubscriptionCTA 
          variant="popup" 
          context="learning"
          showDismiss={true}
        />
      )}
    </div>
  );
} 