'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShoppingBag, MessageCircle, Store } from 'lucide-react';
import { useState, useEffect } from 'react';

// 购买渠道信息
const purchaseChannels = [
  {
    title: '淘宝店',
    description: '淘宝官方店铺获取邀测码',
    icon: ShoppingBag,
    link: 'https://shop.taobao.com/xxx', // 替换为实际的淘宝店链接
    color: 'text-orange-500',
  },
  {
    title: '小红书',
    description: '在小红书平台获取邀测码',
    icon: MessageCircle,
    link: 'https://xiaohongshu.com/xxx', // 替换为实际的小红书链接
    color: 'text-red-500',
  },
  {
    title: '微信小店',
    description: '通过微信小店获取邀测码',
    icon: Store,
    link: 'https://shop.weixin.qq.com/xxx', // 替换为实际的微信小店链接
    color: 'text-green-500',
  },
];

export default function GiftCardsPage() {
  // 添加客户端渲染标记，避免水合错误
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {!isClient ? (
          // 服务端渲染时显示占位内容
          <div className="min-h-[400px] flex justify-center items-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-60 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mb-10"></div>
              <div className="grid gap-6 md:grid-cols-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-48 bg-gray-100 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // 客户端渲染时显示完整内容
          <>
            <div className="mb-8">
              <h1 className="text-3xl font-bold mb-4">邀测码获取</h1>
              <p className="text-lg text-muted-foreground">
                选择以下任意渠道获取邀测码，获取邀测码后即可兑换课程。
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {purchaseChannels.map((channel) => (
                <Card key={channel.title} className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex flex-col items-center text-center">
                    <channel.icon className={`h-12 w-12 ${channel.color} mb-4`} />
                    <h2 className="text-xl font-semibold mb-2">{channel.title}</h2>
                    <p className="text-muted-foreground mb-6">{channel.description}</p>
                    <Button
                      asChild
                      className="w-full"
                      onClick={() => window.open(channel.link, '_blank')}
                    >
                      <a href={channel.link} target="_blank" rel="noopener noreferrer">
                        立即获取
                      </a>
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-2">获取说明</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-700">
                <li>邀测码一经获取，不支持退换</li>
                <li>邀测码有效期为三个月</li>
                <li>每个邀测码仅可兑换一次</li>
                <li>如遇问题请联系客服</li>
              </ul>
            </div>
          </>
        )}
      </div>
    </div>
  );
} 