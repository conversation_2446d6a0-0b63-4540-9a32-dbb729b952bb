'use client'

import { useEffect, useState } from 'react'
import { Course, CourseService } from '@/lib/services/course.service'
import { CategoryService, Category } from '@/lib/services/category.service'
import { CourseCard } from '@/components/course/course-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import SubscriptionCTA from '@/components/subscription/subscription-cta'
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus'

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { hasActiveSubscription } = useSubscriptionStatus()

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch courses and categories data simultaneously
        const [coursesData, categoriesData] = await Promise.all([
          CourseService.getCourses(),
          CategoryService.getCategories()
        ])
        setCourses(coursesData)
        setCategories(categoriesData)
      } catch (err) {
        setError('Failed to load conversation list, please try again later')
        console.error('Failed to fetch data:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Group courses by category
  const groupCoursesByCategory = () => {
    const grouped: { [key: string]: { name: string; courses: Course[] } } = {}
    
    // Initialize category groups
    categories.forEach(category => {
      grouped[category.id] = {
        name: category.name,
        courses: []
      }
    })
    
    // Add uncategorized group
    grouped['uncategorized'] = {
      name: 'Uncategorized',
      courses: []
    }
    
    // Group courses
    courses.forEach(course => {
      if (course.catalogue && course.catalogue.id) {
        if (grouped[course.catalogue.id]) {
          grouped[course.catalogue.id].courses.push(course)
        }
      } else {
        grouped['uncategorized'].courses.push(course)
      }
    })
    
    return grouped
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error}
      </div>
    )
  }

  const groupedCourses = groupCoursesByCategory()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-4">Soul Dialogues</h1>
        <p className="text-center text-gray-600">Explore profound conversations that speak to your soul and embark on a journey of self-discovery</p>
      </div>

      {/* Subscription CTA Banner for non-subscribers */}
      {!hasActiveSubscription && (
        <div className="mb-8">
          <SubscriptionCTA 
            variant="banner" 
            context="course-list"
            showDismiss={true}
          />
        </div>
      )}

      {/* Dynamically render category sections */}
      {Object.entries(groupedCourses).map(([categoryId, { name, courses: categoryCourses }]) => {
        // Don't show if this category has no courses
        if (categoryCourses.length === 0) return null

        return (
          <section key={categoryId} className="mb-12">
            <div className="flex items-center gap-4 mb-6">
              <h2 className="text-2xl font-semibold">{name}</h2>
              <div className="flex-1 h-[1px] bg-border"></div>
              <span className="text-sm text-gray-500">
                {categoryCourses.length} conversations
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryCourses.map((course) => (
                <CourseCard
                  key={course.id}
                  id={course.id}
                  title={course.title}
                  description={course.description}
                  duration={course.duration}
                  level={course.level}
                  lessonsCount={course.lessonsCount}
                  price={course.price}
                  isPurchased={course.isPurchased}
                />
              ))}
            </div>
          </section>
        )
      })}

      {/* If there are no courses */}
      {courses.length === 0 && (
        <div className="text-center text-gray-500 min-h-[400px] flex items-center justify-center">
          <div>
            <p className="text-lg mb-2">No soul dialogues available</p>
            <p className="text-sm">Please check back later</p>
          </div>
        </div>
      )}
    </div>
  )
} 