'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen, CheckCircle, Star, Zap, Users, Award, ArrowRight, Shield, Infinity as InfinityIcon } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import Link from 'next/link';
import { useAuth } from '@/auth/contexts/simplified-auth-context';
import { StripePaymentDialog } from '@/components/payment/stripe-payment-dialog';
import { useOrderPolling } from '@/hooks/useOrderPolling';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';


export default function CoursePurchasePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();
  const { hasActiveSubscription } = useSubscriptionStatus();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  // 支付相关状态
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [orderNo, setOrderNo] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [creatingOrder, setCreatingOrder] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const fetchCourse = async () => {
      try {
        const data = await CourseService.getCourse(courseId);
        if (data.isPurchased) {
          router.replace(`/all-courses/${courseId}`);
          return;
        }
        setCourse(data);
      } catch (err) {
        setError('Failed to load course information, please try again later');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId, router]);

  // 订单状态轮询
  const { startPolling, stopPolling } = useOrderPolling(
    // 支付成功回调
    () => {
      setPaymentDialogOpen(false);
      toast({
        title: 'Payment Successful',
        description: `You have successfully purchased the course "${course?.title}"`,
      });
      setTimeout(() => {
        router.push(`/my-courses/${courseId}`);
      }, 1500);
    },
    // 订单过期回调
    () => {
      setPaymentDialogOpen(false);
      toast({
        variant: 'destructive',
        title: 'Order Expired',
        description: 'Please create a new order',
      });
    }
  );

  // 创建订单并支付
  const handlePurchase = async () => {
    if (!course) return;

    if (!isAuthenticated) {
      toast({
        variant: 'destructive',
        title: 'Please Login First',
        description: 'Please login to your account to purchase courses'
      });

      // 保存当前页面URL，登录后可以返回
      const returnUrl = encodeURIComponent(window.location.pathname);
      router.push(`/login?returnUrl=${returnUrl}`);
      return;
    }

    setCreatingOrder(true);
    try {
      // 创建订单
      const response = await api.post('/api/payment/orders', { courseId });
      const { orderNo: newOrderNo } = response.data;

      // 获取Stripe支付意向
      const paymentResponse = await api.get(`/api/payment/create-payment-intent/${newOrderNo}`);
      const { clientSecret: newClientSecret } = paymentResponse.data;

      if (!newClientSecret) {
        throw new Error('Failed to get payment information');
      }

      setOrderNo(newOrderNo);
      setClientSecret(newClientSecret);
      setPaymentDialogOpen(true);

      // 开始轮询订单状态
      startPolling(newOrderNo);
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to create order, please try again later';
      toast({
        variant: 'destructive',
        title: 'Failed to Create Order',
        description: errorMessage,
      });
    } finally {
      setCreatingOrder(false);
    }
  };

  // 关闭支付对话框
  const handleClosePaymentDialog = () => {
    setPaymentDialogOpen(false);
    stopPolling();
  };

  let content;
  if (loading) {
    content = (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  } else if (error || !course) {
    content = (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || 'Course not found'}
      </div>
    );
  } else {
    content = (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="p-6">
            {!isClient ? (
              // 服务端渲染时显示占位内容
              <div className="min-h-[400px] flex justify-center items-center">
                <LoadingSpinner />
              </div>
            ) : (
              // 客户端渲染时显示完整内容
              <>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold mb-2">{course?.title || "Loading..."}</h2>
                  <p className="text-muted-foreground">{course?.description || "Loading description..."}</p>
                </div>

                <div className="flex items-center gap-6 mb-6 text-muted-foreground">
                  <Badge variant="secondary">{course?.level || "Loading"}</Badge>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{course?.duration || "Loading"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    <span>{course?.lessonsCount || 0} lessons</span>
                  </div>
                </div>

                {/* Subscription recommendation banner */}
                {!hasActiveSubscription && (
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6 rounded-lg mb-6">
                    <div className="flex items-center mb-3">
                      <Award className="h-6 w-6 mr-2" />
                      <span className="text-lg font-bold">🎉 Recommended: Get Full Access with Subscription!</span>
                    </div>
                    <p className="text-green-100 mb-4">
                      Instead of buying individual courses, unlock everything with our subscription plan. 
                      Save money and access our entire library!
                    </p>
                    <div className="flex flex-wrap gap-3 text-sm">
                      <div className="flex items-center">
                        <InfinityIcon className="h-4 w-4 mr-1" />
                        <span>Unlimited access</span>
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        <span>All courses</span>
                      </div>
                      <div className="flex items-center">
                        <Zap className="h-4 w-4 mr-1" />
                        <span>New content monthly</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Detailed comparison */}
                <div className="mb-6">
                  <h3 className="text-xl font-bold mb-4 text-gray-800">Choose Your Learning Path</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    
                    {/* Subscription Option - Featured */}
                    <div className="relative bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-300 rounded-xl p-6">
                      <div className="absolute top-0 right-0 bg-green-500 text-white px-3 py-1 rounded-bl-lg rounded-tr-xl text-sm font-medium">
                        🔥 Best Value
                      </div>
                      
                      <div className="flex items-center mb-4">
                        <div className="bg-green-500 p-2 rounded-lg mr-3">
                          <Star className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-xl font-bold text-green-800">Subscription Plan</h4>
                          <p className="text-green-600">Unlimited access to everything</p>
                        </div>
                      </div>

                      <div className="space-y-3 mb-6">
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span>Access to <strong>ALL courses</strong> in our library</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span>New courses added <strong>every month</strong></span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span><strong>Unlimited AI assistance</strong> for learning</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span>Priority support and community access</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span>Download resources and certificates</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                          <span>Cancel anytime, no long-term commitment</span>
                        </div>
                      </div>

                      <div className="bg-white p-4 rounded-lg mb-4">
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Starting from</p>
                          <div className="text-2xl font-bold text-green-600">$29<span className="text-base font-normal">/month</span></div>
                          <p className="text-xs text-gray-500">Equivalent to ${(29/10).toFixed(1)} per course if you take 10+ courses</p>
                        </div>
                      </div>

                      <Button
                        asChild
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        size="lg"
                      >
                        <Link href="/subscription/plans">
                          <Star className="h-4 w-4 mr-2" />
                          Choose Subscription Plan
                        </Link>
                      </Button>
                    </div>

                    {/* Individual Purchase Option */}
                    <div className="bg-white border border-gray-200 rounded-xl p-6">
                      <div className="flex items-center mb-4">
                        <div className="bg-blue-500 p-2 rounded-lg mr-3">
                          <BookOpen className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-xl font-bold text-gray-800">Individual Purchase</h4>
                          <p className="text-gray-600">One-time payment for this course</p>
                        </div>
                      </div>

                      <div className="space-y-3 mb-6">
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                          <span>Lifetime access to <strong>this course only</strong></span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                          <span>Download all course materials</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                          <span>Basic community access</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <span className="h-4 w-4 mr-2 flex-shrink-0">✗</span>
                          <span>No access to other courses</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <span className="h-4 w-4 mr-2 flex-shrink-0">✗</span>
                          <span>Limited AI assistance</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <span className="h-4 w-4 mr-2 flex-shrink-0">✗</span>
                          <span>No new content updates</span>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg mb-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">${course?.price || 0}</div>
                          <p className="text-xs text-gray-500">One-time payment</p>
                        </div>
                      </div>

                      <Button
                        onClick={handlePurchase}
                        disabled={creatingOrder}
                        variant="outline"
                        className="w-full border-blue-500 text-blue-600 hover:bg-blue-50"
                        size="lg"
                      >
                        {creatingOrder ? (
                          <>
                            <LoadingSpinner className="mr-2 h-4 w-4" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <BookOpen className="h-4 w-4 mr-2" />
                            Purchase This Course Only
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Value proposition */}
                  <div className="mt-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <Shield className="h-5 w-5 text-amber-600 mr-2 flex-shrink-0 mt-0.5" />
                      <div>
                        <h5 className="font-semibold text-amber-800 mb-1">💡 Smart Choice Tip</h5>
                        <p className="text-sm text-amber-700">
                          If you plan to take more than 2-3 courses, subscription saves you money! 
                          Plus, you'll get access to new courses as they're released.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Login prompt or subscriber status */}
                {!isAuthenticated ? (
                  <div className="bg-blue-50 p-4 rounded-lg border-t mt-6">
                    <h3 className="text-lg font-medium mb-2 text-blue-700">Please Login First</h3>
                    <p className="text-blue-600 mb-4">Please login to your account to access purchase options and subscription plans.</p>
                    <Button
                      asChild
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Link href={`/login?returnUrl=${encodeURIComponent(window.location.pathname)}`}>
                        Login Now
                      </Link>
                    </Button>
                  </div>
                ) : hasActiveSubscription ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6 mt-6">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-3">
                        <CheckCircle className="h-8 w-8 text-green-600 mr-2" />
                        <span className="text-xl font-bold text-green-700">You're All Set!</span>
                      </div>
                      <p className="text-green-600 mb-4">
                        As a subscriber, you already have full access to this course and our entire library.
                      </p>
                      <Button
                        asChild
                        className="bg-green-600 hover:bg-green-700"
                        size="lg"
                      >
                        <Link href={`/all-courses/${courseId}`}>
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Start Learning Now
                        </Link>
                      </Button>
                    </div>
                  </div>
                ) : null}
              </>
            )}
          </Card>
        </div>
      </div>
    );
  }

  return (
    <>
      {content}

      {/* Stripe支付对话框 */}
      <StripePaymentDialog
        open={paymentDialogOpen}
        onOpenChange={handleClosePaymentDialog}
        clientSecret={clientSecret || undefined}
        orderNo={orderNo || undefined}
        amount={course ? (course.price || 0) : 0} // Use course price directly, don't multiply by 100
        isLoading={!clientSecret && paymentDialogOpen}
        onCancel={handleClosePaymentDialog}
        onSuccess={() => {
          setPaymentDialogOpen(false);
          toast({
            title: 'Payment Successful',
            description: `You have successfully purchased the course "${course?.title}"`,
          });
          setTimeout(() => {
            router.push(`/my-courses/${courseId}`);
          }, 1500);
        }}
        onError={(error) => {
          toast({
            variant: 'destructive',
            title: 'Payment Failed',
            description: error || 'Please try again later',
          });
        }}
      />
    </>
  );
}