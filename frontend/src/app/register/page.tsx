'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { RegisterForm } from '@/components/auth/register-form'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useToast } from '@/components/ui/use-toast'
import { LoadingWrapper } from '@/components/auth/loading-wrapper'

export default function RegisterPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading, register } = useAuth()
  const { toast } = useToast()

  // Handle authentication state changes
  useEffect(() => {
    console.log('[RegisterPage] Auth state check', {
      isAuthenticated,
      isLoading,
      timestamp: new Date().toISOString()
    })

    // Commented out redirect logic, allow logged-in users to access register page
    // if (!isLoading && isAuthenticated) {
    //   console.log('[RegisterPage] Already authenticated, redirect to dashboard', {
    //     timestamp: new Date().toISOString()
    //   })
    //   router.push('/dashboard')
    // }
  }, [isAuthenticated, isLoading, router])

  // Handle registration submission
  const handleRegister = async (data: { name: string; email: string; password: string }) => {
    try {
      await register(data)
      toast({
        title: 'Registration Successful',
        description: 'Welcome to SoulTalkAI!',
      })
      // Redirect to dashboard after successful registration
      router.push('/dashboard')
    } catch (error) {
      console.error('[RegisterPage] Registration failed:', error)
      toast({
        title: 'Registration Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      })
      throw error // Propagate error up for form component to handle
    }
  }

  // Use LoadingWrapper to handle loading state
  return (
    <LoadingWrapper>
      <div className="container relative h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
          <div className="absolute inset-0 bg-zinc-900" />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 h-6 w-6"
            >
              <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
            </svg>
            SoulTalkAI
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                Join SoulTalkAI and begin your mental healing journey.
              </p>
            </blockquote>
          </div>
        </div>
        <div className="lg:p-8">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="flex flex-col space-y-2 text-center">
              <h1 className="text-2xl font-semibold tracking-tight">
                Create Account
              </h1>
              <p className="text-sm text-muted-foreground">
                Enter your information to create an account
              </p>
            </div>
            <RegisterForm onSubmit={handleRegister} />
          </div>
        </div>
      </div>
    </LoadingWrapper>
  )
}