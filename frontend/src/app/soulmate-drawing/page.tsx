'use client'

import { useState, useCallback, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Progress } from '@/components/ui/progress'
import { Heart, Sparkles, Palette, Stars } from 'lucide-react'
import { SoulQuestionnaireStep } from '@/components/soulmate-drawing/soul-questionnaire-step'
import { VisualPreferencesStep } from '@/components/soulmate-drawing/visual-preferences-step'
import { ImageGenerationStep } from '@/components/soulmate-drawing/image-generation-step'
import { SoulStoryStep } from '@/components/soulmate-drawing/soul-story-step'

interface SoulmateData {
  soulQualities: {
    gender: string
    personalityType: string[]
    energyLevel: string
    spiritualConnection: string[]
    emotionalDepth: string
    lifePhilosophy: string[]
    dreamConnection: string
  }
  visualPreferences: {
    artisticStyle: string
    colorPalette: string
    mood: string
    setting: string
    timeOfDay: string
    aestheticVibes: string[]
  }
  generatedImage?: string
  soulStory?: string
}

const steps = [
  {
    id: 1,
    title: 'Soul Essence Discovery',
    description: 'Take our comprehensive soulmate questionnaire to discover your perfect match\'s spiritual qualities',
    icon: Heart,
    seoDescription: 'Spiritual compatibility assessment through personalized soul questionnaire'
  },
  {
    id: 2,
    title: 'Visual Harmony Selection',
    description: 'Customize artistic style, colors, and aesthetic preferences for your AI soulmate portrait',
    icon: Palette,
    seoDescription: 'AI portrait customization with visual preferences and artistic styles'
  },
  {
    id: 3,
    title: 'AI Portrait Generation',
    description: 'Watch our advanced AI create your personalized soulmate drawing in real-time',
    icon: Sparkles,
    seoDescription: 'Real-time AI soulmate portrait generation with advanced algorithms'
  },
  {
    id: 4,
    title: 'Mystical Soul Story',
    description: 'Receive a personalized story about your spiritual connection and destined relationship',
    icon: Stars,
    seoDescription: 'Personalized spiritual love story based on your soulmate compatibility'
  }
]

export default function SoulmateDrawingPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [soulmateData, setSoulmateData] = useState<SoulmateData>({
    soulQualities: {
      gender: '',
      personalityType: [],
      energyLevel: '',
      spiritualConnection: [],
      emotionalDepth: '',
      lifePhilosophy: [],
      dreamConnection: ''
    },
    visualPreferences: {
      artisticStyle: '',
      colorPalette: '',
      mood: '',
      setting: '',
      timeOfDay: '',
      aestheticVibes: []
    }
  })
  const [isGenerating, setIsGenerating] = useState(false)

  const updateSoulmateData = useCallback((stepData: Partial<SoulmateData>) => {
    setSoulmateData(prev => ({ ...prev, ...stepData }))
  }, [])

  const handleNext = useCallback(() => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }, [currentStep])

  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }, [currentStep])

  const progress = useMemo(() => (currentStep / steps.length) * 100, [currentStep])

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <SoulQuestionnaireStep
            data={soulmateData.soulQualities}
            onUpdate={(data) => updateSoulmateData({ soulQualities: data })}
            onNext={handleNext}
          />
        )
      case 2:
        return (
          <VisualPreferencesStep
            data={soulmateData.visualPreferences}
            onUpdate={(data) => updateSoulmateData({ visualPreferences: data })}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        )
      case 3:
        return (
          <ImageGenerationStep
            soulmateData={soulmateData}
            onUpdate={updateSoulmateData}
            onNext={handleNext}
            onPrevious={handlePrevious}
            isGenerating={isGenerating}
            setIsGenerating={setIsGenerating}
          />
        )
      case 4:
        return (
          <SoulStoryStep
            soulmateData={soulmateData}
            onUpdate={updateSoulmateData}
            onPrevious={handlePrevious}
          />
        )
      default:
        return null
    }
  }

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AI Soulmate Drawing Generator",
    "description": "Generate stunning AI portraits of your destined soulmate through personalized soul questionnaires and mystical visualization.",
    "url": "https://soultalk.ai/soulmate-drawing",
    "applicationCategory": "PersonalityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Organization",
      "name": "SoulTalkAI",
      "url": "https://soultalk.ai"
    },
    "featureList": [
      "AI-powered soulmate portrait generation",
      "Personalized soul questionnaire",
      "Visual preference customization",
      "Mystical story generation",
      "Free soulmate drawing tool"
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1247"
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50">
        <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            AI Soulmate Drawing Generator - Create Your Perfect Match Portrait
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover your destined soulmate through our free AI soulmate drawing generator. 
            Create personalized soulmate portraits using advanced AI technology and spiritual questionnaires. 
            Visualize your perfect soul connection and bring your dream relationship to life with mystical AI artistry.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-2 text-sm text-gray-500">
            <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full">Free Soulmate Generator</span>
            <span className="bg-pink-100 text-pink-700 px-3 py-1 rounded-full">AI Portrait Creation</span>
            <span className="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full">Spiritual Compatibility</span>
            <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full">Dream Match Visualization</span>
          </div>
        </motion.div>

        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex justify-between items-center mb-6">
            {steps.map((step, index) => {
              const isActive = currentStep === step.id
              const isCompleted = currentStep > step.id
              const StepIcon = step.icon

              return (
                <div key={step.id} className="flex flex-col items-center relative">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white scale-110'
                        : isCompleted
                        ? 'bg-gradient-to-r from-green-400 to-blue-500 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}
                  >
                    <StepIcon className="w-5 h-5" />
                  </div>
                  <div className="text-center">
                    <p className={`text-sm font-medium ${isActive ? 'text-purple-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-400 max-w-20">
                      {step.description}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`absolute top-6 left-12 w-full h-0.5 transition-all duration-300 ${
                        isCompleted ? 'bg-gradient-to-r from-green-400 to-blue-500' : 'bg-gray-200'
                      }`}
                      style={{ width: 'calc(100vw / 4 - 3rem)' }}
                    />
                  )}
                </div>
              )
            })}
          </div>
          <Progress value={progress} className="w-full h-2" />
        </motion.div>

        {/* Current Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="mb-8"
        >
          {renderCurrentStep()}
        </motion.div>

        {/* SEO Content Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-16 bg-white/50 backdrop-blur-sm rounded-3xl p-8 border border-purple-100"
        >
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              How Our AI Soulmate Drawing Generator Works
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-purple-700 mb-3">Free AI Soulmate Portrait Creation</h3>
                <p className="text-gray-600 mb-4">
                  Our advanced AI soulmate generator uses cutting-edge artificial intelligence to create personalized soulmate drawings based on your spiritual preferences and personality traits. This free soulmate drawing tool combines mystical insights with modern technology.
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Personalized soul questionnaire analysis</li>
                  <li>• AI-powered visual preference matching</li>
                  <li>• Real-time soulmate portrait generation</li>
                  <li>• Spiritual compatibility assessment</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-pink-700 mb-3">Perfect Match Visualization Technology</h3>
                <p className="text-gray-600 mb-4">
                  Discover your destined soulmate through our unique combination of spiritual questionnaires and AI artistry. Our soulmate finder helps you visualize your perfect romantic match while exploring deep spiritual connections and relationship compatibility.
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Mystical AI portrait algorithms</li>
                  <li>• Dream soulmate visualization</li>
                  <li>• Personalized love story generation</li>
                  <li>• Spiritual relationship insights</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
        </div>
      </div>
    </>
  )
}