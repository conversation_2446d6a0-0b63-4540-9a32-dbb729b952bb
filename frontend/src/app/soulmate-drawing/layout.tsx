import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'AI Soulmate Drawing Generator | Create Your Perfect Match Portrait | SoulTalkAI',
  description: 'Generate stunning AI portraits of your destined soulmate through personalized soul questionnaires. Free soulmate drawing tool with mystical visualization and spiritual connection insights. Discover your perfect match today!',
  keywords: [
    'soulmate drawing',
    'AI soulmate generator',
    'soulmate portrait',
    'AI portrait generation',
    'soul connection',
    'spiritual visualization',
    'destined love',
    'mystical art',
    'soul questionnaire',
    'soulmate finder',
    'AI love match',
    'spiritual compatibility',
    'dream soulmate',
    'free soulmate drawing',
    'personalized portrait',
    'AI relationship tool'
  ],
  openGraph: {
    title: 'AI Soulmate Drawing Generator - Create Your Perfect Match Portrait',
    description: 'Generate stunning AI portraits of your destined soulmate through personalized soul questionnaires. Free soulmate drawing tool with mystical visualization.',
    type: 'website',
    url: 'https://soultalk.ai/soulmate-drawing',
    siteName: 'SoulTalkAI',
    images: [
      {
        url: '/soulmate-drawing-og.jpg',
        width: 1200,
        height: 630,
        alt: 'AI Soulmate Drawing Generator - SoulTalkAI'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Soulmate Drawing Generator - Create Your Perfect Match Portrait',
    description: 'Generate stunning AI portraits of your destined soulmate through personalized soul questionnaires. Free soulmate drawing tool.',
    images: ['/soulmate-drawing-twitter.jpg']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://soultalk.ai/soulmate-drawing'
  }
}

export default function SoulmateDrawingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}