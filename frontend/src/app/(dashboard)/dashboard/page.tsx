'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { StatsService, DashboardStats, RecentLearning } from '@/lib/services/stats.service'
import { formatDistanceToNow } from 'date-fns'
import Link from 'next/link'
import SubscriptionCTA from '@/components/subscription/subscription-cta'
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus'

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentLearning, setRecentLearning] = useState<RecentLearning[]>([])
  const [loading, setLoading] = useState(true)
  const { hasActiveSubscription } = useSubscriptionStatus()

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [dashboardStats, recentLearningData] = await Promise.all([
          StatsService.getDashboardStats(),
          StatsService.getRecentLearning(),
        ])
        setStats(dashboardStats)
        setRecentLearning(recentLearningData)
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">My Support</h1>

      {/* Subscription CTA for non-subscribers */}
      {!hasActiveSubscription && (
        <div className="mb-6">
          <SubscriptionCTA 
            variant="inline" 
            context="dashboard"
          />
        </div>
      )}
      
      {/* Statistics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">Completed Chats</h3>
          <p className="text-3xl font-bold">{stats?.studiedCourses || 0}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">Emotional State</h3>
          <div className="space-y-2">
            <Progress value={stats?.totalProgress || 0} />
            <p className="text-sm text-gray-500">{stats?.totalProgress || 0}%</p>
          </div>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">Support Plans</h3>
          <p className="text-3xl font-bold">{stats?.totalCourses || 0}</p>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">Growth Record</h3>
          <p className="text-3xl font-bold">--</p>
          <p className="text-sm text-gray-500">Coming Soon</p>
        </Card>
      </div>

      {/* Recent chats */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Recent Chats</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentLearning.map((course) => (
            <Link key={course.courseId} href={`/my-courses/${course.courseId}`}>
              <Card className="p-6 hover:bg-gray-50 transition-colors">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-1">{course.courseName}</h3>
                    <p className="text-sm text-gray-500">
                      Last chat: {formatDistanceToNow(new Date(course.lastVisitTime), { 
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm text-gray-500 mb-2">
                      <span>Completion Progress</span>
                      <span>{Math.min(Math.max(course.progress || 0, 0), 100)}%</span>
                    </div>
                    <Progress value={Math.min(Math.max(course.progress || 0, 0), 100)} />
                  </div>
                  {course.lastLesson && (
                    <p className="text-sm text-gray-500">
                      Recent topic: {course.lastLesson.title}
                    </p>
                  )}
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
} 