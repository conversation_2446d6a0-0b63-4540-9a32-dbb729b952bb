'use client'

import { RouteGuard } from '@/components/auth/route-guard'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// 创建一个新的 QueryClient 实例
const queryClient = new QueryClient()

export default function Layout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <QueryClientProvider client={queryClient}>
      <RouteGuard requireAuth={true}>
        <DashboardLayout>
          {children}
        </DashboardLayout>
      </RouteGuard>
    </QueryClientProvider>
  )
} 