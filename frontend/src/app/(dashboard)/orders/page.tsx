'use client';

import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { api } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import Link from 'next/link';

interface Order {
  id: string;
  orderNo: string;
  amount: number;
  status: string;
  createdAt: string;
  course?: {
    id: string;
    title: string;
  };
}

interface Subscription {
  id: string;
  planName: string;
  price: number;
  startDate: string;
  endDate: string;
  status: string;
  isActive: boolean;
  autoRenew: boolean;
  aiCallLimit: number;
  aiCallUsed: number;
  cancelledAt?: string;
  createdAt: string;
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [cancellingId, setCancellingId] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [ordersResponse, subscriptionsResponse] = await Promise.all([
          api.get('/api/payment/orders'),
          api.get('/api/subscription/current').catch(() => ({ data: null }))
        ]);
        
        setOrders(ordersResponse.data);
        
        if (subscriptionsResponse.data) {
          setSubscriptions([subscriptionsResponse.data]);
        }
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'Failed to fetch data',
          description: 'Please try again later',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const getOrderStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary">Pending</Badge>;
      case 'PAID':
        return <Badge variant="default" className="bg-softPurple-500">Paid</Badge>;
      case 'EXPIRED':
        return <Badge variant="destructive">Expired</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSubscriptionStatusBadge = (subscription: Subscription) => {
    if (!subscription.isActive) {
      return <Badge variant="outline">Inactive</Badge>;
    }
    
    const now = new Date();
    const endDate = new Date(subscription.endDate);
    const startDate = new Date(subscription.startDate);
    
    if (startDate > now) {
      return <Badge variant="secondary">Not Started</Badge>;
    }
    
    if (endDate < now) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    
    switch (subscription.status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      case 'expired':
        return <Badge variant="outline">Expired</Badge>;
      default:
        return <Badge variant="outline">{subscription.status}</Badge>;
    }
  };

  const handleCancelSubscription = async (subscriptionId: string, immediate: boolean = false) => {
    if (cancellingId) return;
    
    setCancellingId(subscriptionId);
    try {
      await api.put('/api/subscription/cancel', {
        immediate,
        reason: immediate ? 'User immediate cancellation' : 'User cancelled auto-renewal'
      });
      
      toast({
        title: 'Operation successful',
        description: immediate ? 'Subscription cancelled immediately' : 'Auto-renewal cancelled, subscription will end after expiry',
      });
      
      // 重新获取数据
      window.location.reload();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Operation failed',
        description: 'Please try again later',
      });
    } finally {
      setCancellingId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">My Orders & Subscriptions</h1>
      
      <Tabs defaultValue="subscriptions" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>
        
        <TabsContent value="subscriptions" className="space-y-4">
          {subscriptions.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No subscription records
            </div>
          ) : (
            subscriptions.map((subscription) => (
              <Card key={subscription.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">{subscription.planName}</span>
                      {getSubscriptionStatusBadge(subscription)}
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                      <div>
                        <p>Price: ${Number(subscription.price).toFixed(2)}</p>
                        <p>Start Date: {format(new Date(subscription.startDate), 'MMM dd, yyyy')}</p>
                        <p>End Date: {format(new Date(subscription.endDate), 'MMM dd, yyyy')}</p>
                      </div>
                      <div>
                        <p>AI Calls: {subscription.aiCallUsed}/{subscription.aiCallLimit}</p>
                        <p>Auto Renew: {subscription.autoRenew ? 'Yes' : 'No'}</p>
                        {subscription.cancelledAt && (
                          <p>Cancelled At: {format(new Date(subscription.cancelledAt), 'MMM dd, yyyy')}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <div className="text-sm text-muted-foreground">
                      Created: {format(new Date(subscription.createdAt), 'MMM dd, yyyy HH:mm')}
                    </div>
                    
                    {subscription.isActive && !subscription.cancelledAt && (
                      <div className="flex gap-2">
                        {subscription.autoRenew && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                Cancel Auto-Renewal
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Cancel Auto-Renewal</AlertDialogTitle>
                                <AlertDialogDescription>
                                  After cancelling auto-renewal, your subscription will end after expiry without automatic renewal. You can re-enable auto-renewal at any time.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleCancelSubscription(subscription.id, false)}
                                  disabled={cancellingId === subscription.id}
                                >
                                  Confirm Cancel Renewal
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                        
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm">
                              Cancel Subscription
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
                              <AlertDialogDescription>
                                After cancelling your subscription immediately, you will no longer be able to use the subscription services. Paid fees are non-refundable. This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleCancelSubscription(subscription.id, true)}
                                disabled={cancellingId === subscription.id}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Confirm Cancel Now
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))
          )}
        </TabsContent>
        
        <TabsContent value="orders" className="space-y-4">
          {orders.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No order records
            </div>
          ) : (
            orders.map((order) => (
              <Card key={order.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Order No: {order.orderNo}</span>
                      {getOrderStatusBadge(order.status)}
                    </div>
                    <p className="text-muted-foreground">
                      Course: {order.course?.title || 'Unknown Course'}
                    </p>
                    <p className="text-muted-foreground">
                      Amount: ${Number(order.amount).toFixed(2)}
                    </p>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <div className="text-sm text-muted-foreground">
                      Created: {format(new Date(order.createdAt), 'MMM dd, yyyy HH:mm')}
                    </div>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/orders/${order.orderNo}`}>
                        View Details
                      </Link>
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 