'use client';

import { useEffect, useState } from 'react';
import { CourseCard } from '@/components/course/course-card';
import { Course, CourseService } from '@/lib/services/course.service';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function MyCoursesPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        console.log('[MyCourses] Starting to fetch purchased courses list');
        setLoading(true);
        setError(null);

        const purchasedCourses = await CourseService.getPurchasedCourses();
        console.log('[MyCourses] Successfully fetched purchased courses list', {
          coursesCount: purchasedCourses.length,
          courses: purchasedCourses,
          timestamp: new Date().toISOString(),
        });

        setCourses(purchasedCourses);
      } catch (err: any) {
        console.error('[MyCourses] Failed to fetch courses list', {
          error: err,
          message: err.message,
          response: err.response?.data,
          timestamp: new Date().toISOString(),
        });
        
        setError(err.response?.data?.message || 'Failed to load courses, please try again later');
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 min-h-[200px] flex items-center justify-center">
        {error}
      </div>
    );
  }

  if (courses.length === 0) {
    return (
      <div className="text-center text-muted-foreground min-h-[200px] flex items-center justify-center">
        You haven't purchased any courses yet
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">My Support</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map((course) => (
          <CourseCard
            key={course.id}
            id={course.id}
            title={course.title}
            description={course.description}
            duration={course.duration}
            level={course.level}
            lessonsCount={course.lessonsCount}
            price={course.price}
            isPurchased={true}
            basePath="/my-courses"
          />
        ))}
      </div>
    </div>
  );
} 