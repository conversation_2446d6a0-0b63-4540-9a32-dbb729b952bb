'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';
import { InteractiveLesson } from '@/components/course/interactive-lesson';
import { parseMarkdownToSteps } from '@/lib/utils/markdown';
import { CourseStep } from '@/lib/types/course';

export default function LessonPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const courseId = params.id as string;
  const lessonId = params.lessonId as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [lesson, setLesson] = useState<CourseLesson | null>(null);
  const [steps, setSteps] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [courseData, lessonsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCourseLessons(courseId),
        ]);

        setCourse(courseData);
        setLessons(lessonsData);
        const currentLesson = lessonsData.find((l) => l.id === lessonId);
        if (currentLesson) {
          setLesson(currentLesson);
          // Load course content
          try {
            const content = await CourseService.getLessonContent(courseId, lessonId);
            // Parse course content into steps
            const parsedSteps = parseMarkdownToSteps(content);
            setSteps(parsedSteps);
          } catch (err) {
            console.error('Failed to load lesson content:', err);
            setError('Failed to load course content, please refresh the page and try again');
          }
        }
      } catch (err) {
        setError('Failed to load course information, please try again later');
        console.error('Failed to fetch lesson:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId, lessonId]);

  const handleComplete = async () => {
    if (!lesson) {
      console.error('No lesson found');
      return;
    }

    try {
      console.log('Starting to handle course completion event', {
        courseId,
        lessonId,
        lessonOrder: lesson.order,
        lessonsCount: lessons.length
      });

      // Get next lesson
      const currentIndex = lessons.findIndex((l) => l.id === lessonId);
      console.log('Current lesson index:', currentIndex);
      
      const nextLesson = lessons[currentIndex + 1];
      console.log('Next lesson:', nextLesson);
      
      // Update current course progress
      console.log('Updating course progress...');
      await CourseService.updateProgress(courseId, lessonId);
      console.log('Course progress update completed');
      
      if (nextLesson) {
        // If there's a next lesson, navigate directly after updating progress
        const nextUrl = `/my-courses/${courseId}/lessons/${nextLesson.id}`;
        console.log('Preparing to navigate to next lesson:', nextUrl);
        window.location.href = nextUrl;
      } else {
        // If it's the last lesson, show completion message and return to course page
        console.log('This is the last lesson, preparing to return to course page');
        toast({
          title: 'Course Completed',
          description: 'Congratulations! You have completed this lesson!'
        });
        setTimeout(() => {
          console.log('Returning to course page');
          window.location.href = `/my-courses/${courseId}`;
        }, 1500);
      }
    } catch (err) {
      console.error('Course completion handling failed:', err);
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: 'Please try again later',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course || !lesson) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || 'Lesson not found'}
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href={`/my-courses/${courseId}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Course
          </Link>
        </Button>
      </div>

      <InteractiveLesson
        title={lesson.title}
        steps={steps}
        onComplete={handleComplete}
        hasNextLesson={lessons.findIndex((l) => l.id === lessonId) < lessons.length - 1}
      />
    </div>
  );
} 