import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export type User = {
  id: string;
  email: string;
  name: string;
  createdAt: string;
  lastLoginAt: string;
};

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'email',
    header: '邮箱',
  },
  {
    accessorKey: 'name',
    header: '用户名',
  },
  {
    accessorKey: 'createdAt',
    header: '注册时间',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return format(date, 'yyyy年MM月dd日', { locale: zhCN });
    },
  },
  {
    accessorKey: 'lastLoginAt',
    header: '最后登录',
    cell: ({ row }) => {
      const date = new Date(row.getValue('lastLoginAt'));
      return format(date, 'yyyy年MM月dd日', { locale: zhCN });
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const user = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">打开菜单</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>操作</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.id)}
            >
              复制用户ID
            </DropdownMenuItem>
            <DropdownMenuItem>查看详情</DropdownMenuItem>
            <DropdownMenuItem>禁用账号</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
]; 