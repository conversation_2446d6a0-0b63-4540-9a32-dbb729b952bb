'use client'

import { ThemeProvider } from '@/components/theme-provider'

interface AuthRulesLayoutProps {
  children: React.ReactNode
}

export default function AuthRulesLayout({ children }: AuthRulesLayoutProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <div className="min-h-screen bg-background font-sans antialiased">
        {children}
      </div>
    </ThemeProvider>
  )
} 