'use client'

import { useState } from 'react'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card } from '@/components/ui/card'
import { StateTransitionFlow } from '@/components/auth/state-transition-flow'

// 状态转换规则数据
const stateTransitions = {
  UNINITIALIZED: ['INITIALIZING', 'PUBLIC'],
  INITIALIZING: ['PUBLIC', 'AUTHENTICATED', 'UNAUTHENTICATED', 'ERROR'],
  PUBLIC: ['INITIALIZING', 'AUTHENTICATED', 'PUBLIC'],
  AUTHENTICATED: ['INITIALIZING', 'UNAUTHENTICATED', 'ERROR'],
  UNAUTHENTICATED: ['INITIALIZING', 'AUTHENTICATED'],
  ERROR: ['INITIALIZING', 'PUBLIC', 'UNAUTHENTICATED']
}

// 状态机规则管理组件
const StateTransitionRules = () => {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">状态转换规则</h2>
      </div>

      {/* 流程图 */}
      <div className="rounded-lg border bg-card">
        <StateTransitionFlow transitions={stateTransitions} />
      </div>

      {/* JSON 视图 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">JSON 配置</h3>
        <pre className="p-4 bg-muted rounded-lg overflow-auto">
          {JSON.stringify(stateTransitions, null, 2)}
        </pre>
      </div>
    </div>
  )
}

// 路由权限规则管理组件
const RoutePermissionRules = () => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">路由权限规则</h2>
      </div>
      <div className="space-y-4">
        <pre className="p-4 bg-muted rounded-lg overflow-auto">
          {JSON.stringify([
            {
              path: '/admin/*',
              roles: ['admin'],
              isPublic: false,
              redirectTo: '/admin/login'
            },
            {
              path: '/dashboard/*',
              roles: ['user', 'admin'],
              isPublic: false,
              redirectTo: '/login'
            },
            {
              path: '/login',
              roles: [],
              isPublic: true
            }
          ], null, 2)}
        </pre>
      </div>
    </div>
  )
}

// 错误处理规则管理组件
const ErrorHandlingRules = () => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">错误处理规则</h2>
      </div>
      <div className="space-y-4">
        <pre className="p-4 bg-muted rounded-lg overflow-auto">
          {JSON.stringify([
            {
              errorType: 'INVALID_CREDENTIALS',
              action: 'transition',
              nextState: 'UNAUTHENTICATED',
              message: '认证失败'
            },
            {
              errorType: 'UNAUTHORIZED',
              action: 'transition',
              nextState: 'ERROR',
              message: '无权访问'
            },
            {
              errorType: 'NETWORK_ERROR',
              action: 'retry',
              maxRetries: 3,
              retryDelay: 1000,
              nextState: 'ERROR',
              message: '网络错误'
            },
            {
              errorType: 'INITIALIZATION_FAILED',
              action: 'transition',
              nextState: 'ERROR',
              message: '初始化失败'
            },
            {
              errorType: 'INVALID_STATE',
              action: 'transition',
              nextState: 'ERROR',
              message: '无效的状态转换'
            },
            {
              errorType: 'UNKNOWN_ERROR',
              action: 'transition',
              nextState: 'ERROR',
              message: '发生未知错误'
            }
          ], null, 2)}
        </pre>
      </div>
    </div>
  )
}

// 调试工具面板组件
const DebugPanel = () => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">调试工具</h2>
      </div>
      <div className="space-y-4">
        <pre className="p-4 bg-muted rounded-lg overflow-auto">
          {JSON.stringify({
            enabled: true,
            logLevel: 'debug',
            persistLogs: true,
            maxLogEntries: 1000
          }, null, 2)}
        </pre>
      </div>
    </div>
  )
}

// 主页面组件
export default function AuthRulesManagement() {
  const [activeTab, setActiveTab] = useState('state-machine')

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">认证系统规则管理</h1>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="state-machine">状态机规则</TabsTrigger>
            <TabsTrigger value="route-permissions">路由权限</TabsTrigger>
            <TabsTrigger value="error-handling">错误处理</TabsTrigger>
            <TabsTrigger value="debug-tools">调试工具</TabsTrigger>
          </TabsList>

          <TabsContent value="state-machine">
            <Card className="p-6">
              <StateTransitionRules />
            </Card>
          </TabsContent>

          <TabsContent value="route-permissions">
            <Card className="p-6">
              <RoutePermissionRules />
            </Card>
          </TabsContent>

          <TabsContent value="error-handling">
            <Card className="p-6">
              <ErrorHandlingRules />
            </Card>
          </TabsContent>

          <TabsContent value="debug-tools">
            <Card className="p-6">
              <DebugPanel />
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 