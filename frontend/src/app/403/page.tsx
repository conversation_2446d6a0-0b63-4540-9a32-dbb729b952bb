'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'

export default function ForbiddenPage() {
  const router = useRouter()

  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">403</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          Access Denied
        </h2>
        <p className="text-gray-500 mb-8">
          Sorry, you don't have permission to access this page
        </p>
        <div className="space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            Go Back
          </Button>
          <Button
            onClick={() => router.replace('/dashboard')}
          >
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
} 