'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import SubscriptionPlans from '@/components/subscription/subscription-plans';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, CheckCircle, Star, Crown } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/auth/contexts/simplified-auth-context';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { api } from '@/lib/api';

export default function SubscriptionPlansPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { subscription, hasActiveSubscription, loading } = useSubscriptionStatus();
  
  const courseId = searchParams.get('courseId');
  const source = searchParams.get('source');
  const [referenceCourse, setReferenceCourse] = useState<any>(null);

  useEffect(() => {
    // If courseId is provided, fetch course details for context
    if (courseId) {
      fetchCourseDetails();
    }
  }, [courseId]);

  const fetchCourseDetails = async () => {
    try {
      const response = await api.get(`/api/courses/${courseId}`);
      setReferenceCourse(response.data);
    } catch (error) {
      console.error('Failed to fetch course details:', error);
    }
  };

  const handlePlanSelect = (plan: any) => {
    console.log('Selected plan:', plan);
    // This will be handled by the SubscriptionPlans component
  };

  const getPageTitle = () => {
    if (hasActiveSubscription) {
      return 'Manage Your Subscription';
    }
    if (referenceCourse) {
      return `Unlock "${referenceCourse.title}" with Subscription`;
    }
    return 'Choose Your Perfect Subscription Plan';
  };

  const getPageDescription = () => {
    if (hasActiveSubscription) {
      return 'You currently have an active subscription. You can change your plan or manage your subscription below.';
    }
    if (referenceCourse) {
      return `Get unlimited access to "${referenceCourse.title}" and hundreds of other courses with a subscription plan.`;
    }
    return 'Unlock unlimited AI learning possibilities with our flexible subscription plans.';
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-96 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          {/* Back button */}
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="mr-auto"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </div>

          <h1 className="text-4xl font-bold mb-4">{getPageTitle()}</h1>
          <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
            {getPageDescription()}
          </p>

          {/* Course reference card */}
          {referenceCourse && (
            <Card className="mb-8 max-w-2xl mx-auto border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Star className="w-5 h-5 text-purple-600" />
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                    Recommended for this course
                  </Badge>
                </div>
                <CardTitle className="text-lg">{referenceCourse.title}</CardTitle>
                <CardDescription>
                  Instead of paying ${referenceCourse.price} for this single course, 
                  get unlimited access to all courses with a subscription!
                </CardDescription>
              </CardHeader>
            </Card>
          )}

          {/* Current subscription status */}
          {hasActiveSubscription && subscription && (
            <Card className="mb-8 max-w-2xl mx-auto border-green-200 bg-green-50">
              <CardHeader>
                <div className="flex items-center justify-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <CardTitle className="text-lg text-green-800">Current Subscription</CardTitle>
                </div>
                <CardDescription className="text-green-700">
                  You have an active {subscription.planName} subscription.
                  {subscription.daysRemaining > 0 && (
                    <span className="block mt-1">
                      {subscription.daysRemaining} days remaining
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
            </Card>
          )}
        </div>

        {/* Subscription Plans */}
        <SubscriptionPlans
          onSelectPlan={handlePlanSelect}
          currentPlanId={subscription?.id}
        />

        {/* Benefits Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-8">Why Choose a Subscription?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Crown className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold">Unlimited Course Access</h3>
              <p className="text-gray-600">
                Access all courses in our library, including new releases and exclusive content.
              </p>
            </div>
            <div className="space-y-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold">AI Learning Support</h3>
              <p className="text-gray-600">
                Get personalized AI tutoring and unlimited conversations to enhance your learning.
              </p>
            </div>
            <div className="space-y-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold">Premium Features</h3>
              <p className="text-gray-600">
                Priority support, exclusive content, and early access to new features.
              </p>
            </div>
          </div>
        </div>

        {/* Login prompt for non-authenticated users */}
        {!isAuthenticated && (
          <Card className="mt-12 max-w-2xl mx-auto border-blue-200 bg-blue-50">
            <CardHeader className="text-center">
              <CardTitle className="text-blue-800">Ready to Get Started?</CardTitle>
              <CardDescription className="text-blue-700">
                Sign in to your account to subscribe and start learning immediately.
              </CardDescription>
              <div className="flex gap-4 justify-center mt-4">
                <Link href="/login?returnUrl=/subscription/plans">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Sign In
                  </Button>
                </Link>
                <Link href="/register?returnUrl=/subscription/plans">
                  <Button variant="outline" className="border-blue-600 text-blue-600">
                    Create Account
                  </Button>
                </Link>
              </div>
            </CardHeader>
          </Card>
        )}

        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Can I cancel my subscription anytime?</CardTitle>
                <CardDescription>
                  Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What about the free trial?</CardTitle>
                <CardDescription>
                  All plans include a 7-day free trial. You can cancel during the trial period and won't be charged anything. Perfect for trying out our platform risk-free.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What happens to courses I purchased individually?</CardTitle>
                <CardDescription>
                  Any courses you purchased individually will remain accessible permanently, even if you cancel your subscription.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}