'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Plus,
  MoreVertical,
  Loader2,
  Edit,
  Trash2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { BuildingToolService } from '@/lib/services/building-tool.service';
import type { BuildingTool } from '@/lib/services/course.service';
import { BuildingToolDialog } from '@/components/admin/building-tool-dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export default function BuildingToolsPage() {
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingTool, setDeletingTool] = useState<BuildingTool | null>(null);
  const [editingTool, setEditingTool] = useState<BuildingTool | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 查询建站工具列表
  const { 
    data: tools = [], 
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['admin-building-tools'],
    queryFn: () => BuildingToolService.getAll(),
  });

  // 创建建站工具
  const createMutation = useMutation({
    mutationFn: BuildingToolService.create,
    onSuccess: () => {
      toast({
        title: '创建成功',
        description: '建站工具已创建',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-building-tools'] });
      setIsCreateDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '创建失败',
        description: error instanceof Error ? error.message : '无法创建建站工具，请重试',
      });
    },
  });

  // 删除建站工具
  const deleteMutation = useMutation({
    mutationFn: BuildingToolService.delete,
    onSuccess: () => {
      toast({
        title: '删除成功',
        description: '建站工具已删除',
      });
      setIsDeleteAlertOpen(false);
      queryClient.invalidateQueries({ queryKey: ['admin-building-tools'] });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: error instanceof Error ? error.message : '无法删除建站工具，请重试',
      });
    },
  });

  // 更新建站工具
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<BuildingTool> }) =>
      BuildingToolService.update(id, data),
    onSuccess: () => {
      toast({
        title: '更新成功',
        description: '建站工具信息已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-building-tools'] });
      setIsEditDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: error instanceof Error ? error.message : '无法更新建站工具，请重试',
      });
    },
  });

  const handleCreateTool = async (data: any) => {
    await createMutation.mutateAsync(data);
  };

  const handleDeleteTool = async (tool: BuildingTool) => {
    setDeletingTool(tool);
    setIsDeleteAlertOpen(true);
  };

  const handleEditTool = async (data: Partial<BuildingTool>) => {
    if (!editingTool) return;
    await updateMutation.mutateAsync({
      id: editingTool.id,
      data,
    });
  };

  const handleStartEdit = (tool: BuildingTool) => {
    setEditingTool(tool);
    setIsEditDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingTool) {
      await deleteMutation.mutateAsync(deletingTool.id);
    }
  };

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-slate-500">
        <p className="text-lg">加载失败</p>
        <p className="text-sm mt-2">请刷新页面重试</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold tracking-tight">建站工具管理</h2>
          <p className="text-sm text-slate-500">
            管理系统中的所有建站工具
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          新建工具
        </Button>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>工具名称</TableHead>
              <TableHead>分类</TableHead>
              <TableHead>难度</TableHead>
              <TableHead>预估时间</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>排序</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : tools.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center text-slate-500">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              tools.map((tool) => (
                <TableRow key={tool.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{tool.title}</div>
                      <div className="text-sm text-slate-500">{tool.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>{tool.category}</TableCell>
                  <TableCell>
                    <Badge variant={tool.difficulty === '初级' ? 'default' : tool.difficulty === '中级' ? 'secondary' : 'destructive'}>
                      {tool.difficulty}
                    </Badge>
                  </TableCell>
                  <TableCell>{tool.estimatedTime}</TableCell>
                  <TableCell>
                    <Badge variant={tool.isActive ? 'default' : 'secondary'}>
                      {tool.isActive ? '启用' : '禁用'}
                    </Badge>
                  </TableCell>
                  <TableCell>{tool.sortOrder}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleStartEdit(tool)}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteTool(tool)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 创建建站工具对话框 */}
      <BuildingToolDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateTool}
        mode="create"
      />

      {/* 编辑建站工具对话框 */}
      <BuildingToolDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleEditTool}
        initialData={editingTool || undefined}
        mode="edit"
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除工具 "{deletingTool?.title}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 