'use client';

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'

// 模拟系统设置数据
const mockSettings = {
  general: {
    siteName: 'IP Navigator',
    siteDescription: '智能专利检索与分析平台',
    logo: '/logo.png',
    favicon: '/favicon.ico',
    contactEmail: '<EMAIL>',
  },
  security: {
    enableTwoFactor: true,
    passwordMinLength: 8,
    passwordExpiration: 90,
    maxLoginAttempts: 5,
    sessionTimeout: 30,
  },
  ai: {
    defaultProvider: 'openai',
    maxTokens: 2000,
    temperature: 0.7,
    enableContentFilter: true,
    streamResponse: true,
  },
  email: {
    provider: 'smtp',
    host: 'smtp.example.com',
    port: 587,
    username: '<EMAIL>',
    enableSSL: true,
    fromName: 'IP Navigator',
  },
}

export default function SettingsPage() {
  const [settings, setSettings] = useState(mockSettings)
  const [activeTab, setActiveTab] = useState('general')

  const handleSave = () => {
    // 这里添加保存设置的逻辑
    console.log('保存设置:', settings)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">系统设置</h1>
        <Button onClick={handleSave}>保存更改</Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="general">基本设置</TabsTrigger>
          <TabsTrigger value="security">安全设置</TabsTrigger>
          <TabsTrigger value="ai">AI 设置</TabsTrigger>
          <TabsTrigger value="email">邮件设置</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card className="p-6">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="siteName">网站名称</Label>
                <Input
                  id="siteName"
                  value={settings.general.siteName}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      general: { ...settings.general, siteName: e.target.value },
                    })
                  }
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="siteDescription">网站描述</Label>
                <Input
                  id="siteDescription"
                  value={settings.general.siteDescription}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      general: { ...settings.general, siteDescription: e.target.value },
                    })
                  }
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="contactEmail">联系邮箱</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={settings.general.contactEmail}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      general: { ...settings.general, contactEmail: e.target.value },
                    })
                  }
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>双因素认证</Label>
                  <div className="text-sm text-muted-foreground">
                    启用双因素认证以提高账户安全性
                  </div>
                </div>
                <Switch
                  checked={settings.security.enableTwoFactor}
                  onCheckedChange={(checked) =>
                    setSettings({
                      ...settings,
                      security: { ...settings.security, enableTwoFactor: checked },
                    })
                  }
                />
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label htmlFor="passwordMinLength">密码最小长度</Label>
                <Input
                  id="passwordMinLength"
                  type="number"
                  value={settings.security.passwordMinLength}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      security: {
                        ...settings.security,
                        passwordMinLength: parseInt(e.target.value),
                      },
                    })
                  }
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="sessionTimeout">会话超时时间（分钟）</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      security: {
                        ...settings.security,
                        sessionTimeout: parseInt(e.target.value),
                      },
                    })
                  }
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="ai" className="space-y-4">
          <Card className="p-6">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="defaultProvider">默认 AI 服务商</Label>
                <Select
                  value={settings.ai.defaultProvider}
                  onValueChange={(value) =>
                    setSettings({
                      ...settings,
                      ai: { ...settings.ai, defaultProvider: value },
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="google">Google AI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="maxTokens">最大 Token 数</Label>
                <Input
                  id="maxTokens"
                  type="number"
                  value={settings.ai.maxTokens}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      ai: { ...settings.ai, maxTokens: parseInt(e.target.value) },
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>内容过滤</Label>
                  <div className="text-sm text-muted-foreground">
                    启用 AI 响应内容过滤
                  </div>
                </div>
                <Switch
                  checked={settings.ai.enableContentFilter}
                  onCheckedChange={(checked) =>
                    setSettings({
                      ...settings,
                      ai: { ...settings.ai, enableContentFilter: checked },
                    })
                  }
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card className="p-6">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="emailProvider">邮件服务提供商</Label>
                <Select
                  value={settings.email.provider}
                  onValueChange={(value) =>
                    setSettings({
                      ...settings,
                      email: { ...settings.email, provider: value },
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="smtp">SMTP</SelectItem>
                    <SelectItem value="sendgrid">SendGrid</SelectItem>
                    <SelectItem value="mailgun">Mailgun</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="smtpHost">SMTP 服务器</Label>
                <Input
                  id="smtpHost"
                  value={settings.email.host}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      email: { ...settings.email, host: e.target.value },
                    })
                  }
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="smtpPort">SMTP 端口</Label>
                <Input
                  id="smtpPort"
                  type="number"
                  value={settings.email.port}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      email: { ...settings.email, port: parseInt(e.target.value) },
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用 SSL</Label>
                  <div className="text-sm text-muted-foreground">
                    使用 SSL 加密邮件传输
                  </div>
                </div>
                <Switch
                  checked={settings.email.enableSSL}
                  onCheckedChange={(checked) =>
                    setSettings({
                      ...settings,
                      email: { ...settings.email, enableSSL: checked },
                    })
                  }
                />
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
