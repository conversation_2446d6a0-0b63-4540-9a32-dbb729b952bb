'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Plus,
  MoreVertical,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { CourseService, Course } from '@/lib/services/course.service';
import { CourseDialog } from '@/components/course/course-dialog';
import { CourseToolsDialog } from '@/components/admin/course-tools-dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialog<PERSON>ooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter } from 'next/navigation';

export default function CoursesPage() {
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isToolsDialogOpen, setIsToolsDialogOpen] = useState(false);
  const [deletingCourse, setDeletingCourse] = useState<Course | null>(null);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [toolsCourse, setToolsCourse] = useState<Course | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const router = useRouter();

  // 查询课程列表
  const { 
    data: courses = [], 
    isLoading,
    isError,
  } = useQuery({
    queryKey: ['admin-courses'],
    queryFn: () => CourseService.getCourses(true), // 包含未发布的课程
  });

  // 创建课程
  const createMutation = useMutation({
    mutationFn: CourseService.createCourse,
    onSuccess: () => {
      toast({
        title: '创建成功',
        description: '课程已创建',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
      setIsCreateDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '创建失败',
        description: error instanceof Error ? error.message : '无法创建课程，请重试',
      });
    },
  });

  // 删除课程
  const deleteMutation = useMutation({
    mutationFn: CourseService.deleteCourse,
    onSuccess: () => {
      toast({
        title: '删除成功',
        description: '课程已删除',
      });
      setIsDeleteAlertOpen(false);
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: error instanceof Error ? error.message : '无法删除课程，请重试',
      });
    },
  });

  // 切换发布状态
  const togglePublishMutation = useMutation({
    mutationFn: CourseService.togglePublishCourse,
    onSuccess: () => {
      toast({
        title: '操作成功',
        description: '课程状态已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: error instanceof Error ? error.message : '无法更新课程状态，请重试',
      });
    },
  });

  // 更新课程
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Course> }) =>
      CourseService.updateCourse(id, data),
    onSuccess: () => {
      toast({
        title: '更新成功',
        description: '课程信息已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
      setIsEditDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: error instanceof Error ? error.message : '无法更新课程，请重试',
      });
    },
  });

  const handleCreateCourse = async (data: any) => {
    await createMutation.mutateAsync(data);
  };

  const handleDeleteCourse = async (course: any) => {
    setDeletingCourse(course);
    setIsDeleteAlertOpen(true);
  };

  const handleTogglePublish = async (course: any) => {
    await togglePublishMutation.mutateAsync(course.id);
  };

  const handleEditCourse = async (data: Partial<Course>) => {
    if (!editingCourse) return;
    await updateMutation.mutateAsync({
      id: editingCourse.id,
      data,
    });
  };

  const handleStartEdit = (course: Course) => {
    setEditingCourse(course);
    setIsEditDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingCourse) {
      await deleteMutation.mutateAsync(deletingCourse.id);
    }
  };

  const handleManageLessons = (course: Course) => {
    router.push(`/admin/courses/${course.id}/lessons`);
  };

  const handleManageTools = (course: Course) => {
    setToolsCourse(course);
    setIsToolsDialogOpen(true);
  };

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-slate-500">
        <p className="text-lg">加载失败</p>
        <p className="text-sm mt-2">请刷新页面重试</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold tracking-tight">课程管理</h2>
          <p className="text-sm text-slate-500">
            管理系统中的所有课程
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          新建课程
        </Button>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>课程名称</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>分类</TableHead>
              <TableHead>价格</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : courses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center text-slate-500">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              courses.map((course) => (
                <TableRow key={course.id}>
                  <TableCell>{course.title}</TableCell>
                  <TableCell className="max-w-[300px] truncate">
                    {course.description}
                  </TableCell>
                  <TableCell>
                    {course.catalogue ? (
                      <Badge variant="outline">{course.catalogue.name}</Badge>
                    ) : (
                      <span className="text-slate-400">未分类</span>
                    )}
                  </TableCell>
                  <TableCell>¥{course.price}</TableCell>
                  <TableCell>
                    <Badge 
                      variant="secondary" 
                      className={`${course.isPublished ? 'bg-green-500' : 'bg-slate-500'} text-white cursor-pointer`}
                      onClick={() => handleTogglePublish(course)}
                    >
                      {course.isPublished ? '已发布' : '未发布'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {format(new Date(course.createdAt), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleStartEdit(course)}
                        >
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleManageLessons(course)}
                        >
                          管理章节
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleManageTools(course)}
                        >
                          管理工具
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleTogglePublish(course)}
                        >
                          {course.isPublished ? '取消发布' : '发布'}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteCourse(course)}
                        >
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 创建课程对话框 */}
      <CourseDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateCourse}
        mode="create"
      />

      {/* 编辑课程对话框 */}
      <CourseDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleEditCourse}
        initialData={editingCourse || undefined}
        mode="edit"
      />

      {/* 课程工具管理对话框 */}
      {toolsCourse && (
        <CourseToolsDialog
          open={isToolsDialogOpen}
          onOpenChange={setIsToolsDialogOpen}
          courseId={toolsCourse.id}
          courseName={toolsCourse.title}
        />
      )}

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个课程吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 