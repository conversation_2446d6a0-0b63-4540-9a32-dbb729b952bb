'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Plus,
  MoreVertical,
  Loader2,
  ArrowLeft,
  GripVertical,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import { CourseService, Course, CourseLesson } from '@/lib/services/course.service';
import { LessonDialog } from '@/components/course/lesson-dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

// 可排序的表格行组件
function SortableTableRow({ lesson, onEdit, onDelete, onEditScript }: {
  lesson: CourseLesson;
  onEdit: (lesson: CourseLesson) => void;
  onDelete: (lesson: CourseLesson) => void;
  onEditScript: (lessonId: string) => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: lesson.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 1 : 0,
    position: isDragging ? 'relative' : 'static',
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <TableRow ref={setNodeRef} style={style as any}>
      <TableCell>
        <div {...attributes} {...listeners} className="cursor-grab">
          <GripVertical className="h-4 w-4 text-slate-400" />
        </div>
      </TableCell>
      <TableCell>{lesson.title}</TableCell>
      <TableCell>{lesson.duration}</TableCell>
      <TableCell className="max-w-[300px] truncate">
        {lesson.description}
      </TableCell>
      <TableCell className="font-mono text-sm">
        {lesson.scriptPath}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(lesson)}>
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onEditScript(lesson.id)}
            >
              编辑脚本
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600"
              onClick={() => onDelete(lesson)}
            >
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
}

export default function CourseLessonsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const courseId = params.courseId as string;

  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingLesson, setDeletingLesson] = useState<CourseLesson | null>(null);
  const [editingLesson, setEditingLesson] = useState<CourseLesson | null>(null);

  // 查询课程信息
  const { data: course, isLoading: isLoadingCourse } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => CourseService.getCourse(courseId),
  });

  // 查询章节列表
  const { data: lessons = [], isLoading: isLoadingLessons } = useQuery({
    queryKey: ['course-lessons', courseId],
    queryFn: () => CourseService.getCourseLessons(courseId),
  });

  // 创建章节
  const createMutation = useMutation({
    mutationFn: (data: Partial<CourseLesson>) =>
      CourseService.createLesson(courseId, data),
    onSuccess: () => {
      toast({
        title: '创建成功',
        description: '疗愈主题已创建',
      });
      queryClient.invalidateQueries({ queryKey: ['course-lessons', courseId] });
      setIsCreateDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '创建失败',
        description: error instanceof Error ? error.message : '无法创建疗愈主题，请重试',
      });
    },
  });

  // 更新章节
  const updateMutation = useMutation({
    mutationFn: ({ lessonId, data }: { lessonId: string; data: Partial<CourseLesson> }) =>
      CourseService.updateLesson(courseId, lessonId, data),
    onSuccess: () => {
      toast({
        title: '更新成功',
        description: '章节已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['course-lessons', courseId] });
      setIsEditDialogOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: error instanceof Error ? error.message : '无法更新章节，请重试',
      });
    },
  });

  // 删除章节
  const deleteMutation = useMutation({
    mutationFn: (lessonId: string) => CourseService.deleteLesson(courseId, lessonId),
    onSuccess: () => {
      toast({
        title: '删除成功',
        description: '章节已删除',
      });
      queryClient.invalidateQueries({ queryKey: ['course-lessons', courseId] });
      setIsDeleteAlertOpen(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: error instanceof Error ? error.message : '无法删除章节，请重试',
      });
    },
  });

  // 添加重排序mutation
  const reorderMutation = useMutation({
    mutationFn: (lessonIds: string[]) =>
      CourseService.reorderLessons(courseId, lessonIds),
    onSuccess: () => {
      toast({
        title: '排序成功',
        description: '章节顺序已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['course-lessons', courseId] });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '排序失败',
        description: error instanceof Error ? error.message : '无法更新章节顺序，请重试',
      });
    },
  });

  // 添加拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = lessons.findIndex((lesson) => lesson.id === active.id);
      const newIndex = lessons.findIndex((lesson) => lesson.id === over.id);
      
      const newLessons = arrayMove(lessons, oldIndex, newIndex);
      reorderMutation.mutate(newLessons.map(lesson => lesson.id));
    }
  };

  const handleCreateLesson = async (data: Partial<CourseLesson>) => {
    await createMutation.mutateAsync(data);
  };

  const handleUpdateLesson = async (data: Partial<CourseLesson>) => {
    if (!editingLesson) return;
    await updateMutation.mutateAsync({
      lessonId: editingLesson.id,
      data,
    });
  };

  const handleDeleteLesson = async (lesson: CourseLesson) => {
    setDeletingLesson(lesson);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (deletingLesson) {
      await deleteMutation.mutateAsync(deletingLesson.id);
    }
  };

  const handleStartEdit = (lesson: CourseLesson) => {
    setEditingLesson(lesson);
    setIsEditDialogOpen(true);
  };

  const isLoading = isLoadingCourse || isLoadingLessons;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-2xl font-semibold tracking-tight">
              {isLoadingCourse ? '加载中...' : `${course?.title} - 主题管理`}
            </h2>
          </div>
          <p className="text-sm text-slate-500">
            管理疗愈主题内容和顺序
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加主题
        </Button>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[30px]"></TableHead>
              <TableHead>主题标题</TableHead>
              <TableHead>时长</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>脚本文件</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                </TableCell>
              </TableRow>
            ) : lessons.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center text-slate-500">
                  暂无主题
                </TableCell>
              </TableRow>
            ) : (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={lessons.map(lesson => lesson.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {lessons.map((lesson) => (
                    <SortableTableRow
                      key={lesson.id}
                      lesson={lesson}
                      onEdit={handleStartEdit}
                      onDelete={handleDeleteLesson}
                      onEditScript={(lessonId) =>
                        router.push(`/admin/courses/${courseId}/lessons/${lessonId}/script`)
                      }
                    />
                  ))}
                </SortableContext>
              </DndContext>
            )}
          </TableBody>
        </Table>
      </div>

      {/* 创建主题对话框 */}
      <LessonDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateLesson}
        mode="create"
      />

      {/* 编辑主题对话框 */}
      <LessonDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleUpdateLesson}
        initialData={editingLesson || undefined}
        mode="edit"
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个疗愈主题吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 