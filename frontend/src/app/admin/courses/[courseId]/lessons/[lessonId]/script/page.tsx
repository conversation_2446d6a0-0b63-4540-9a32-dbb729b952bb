'use client';

import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, Save, Image, Video, Music, MessageSquare, Palette, Wand2, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { CourseService } from '@/lib/services/course.service';
import { cn } from '@/lib/utils';
import { Markdown } from '@/components/markdown';
import { api } from '@/lib/api';
import { AiCourseGenerator } from '@/components/course/AiCourseGenerator';
import { CourseEditor } from '@/components/course/CourseEditor';
import { AiCourseGeneratorModal } from '@/components/course/AiCourseGeneratorModal';
import { AiContentRegeneratorModal } from '@/components/course/AiContentRegeneratorModal';

export default function LessonScriptPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const courseId = params.courseId as string;
  const lessonId = params.lessonId as string;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [content, setContent] = useState('');
  const [activeTab, setActiveTab] = useState('edit');
  const [isUploading, setIsUploading] = useState(false);
  const [aiGenerateModalVisible, setAiGenerateModalVisible] = useState(false);
  const [aiRegenerateModalVisible, setAiRegenerateModalVisible] = useState(false);
  const [selectedContent, setSelectedContent] = useState('');
  const [selectedSection, setSelectedSection] = useState<string | undefined>(undefined);

  // 查询课程信息
  const { data: course } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => CourseService.getCourse(courseId),
  });

  // 查询章节信息
  const { data: lessons } = useQuery({
    queryKey: ['course-lessons', courseId],
    queryFn: () => CourseService.getCourseLessons(courseId),
  });

  // 查询脚本内容
  const { data: scriptContent, isLoading } = useQuery({
    queryKey: ['lesson-script', courseId, lessonId],
    queryFn: () => CourseService.getLessonContent(courseId, lessonId),
  });

  // 保存脚本内容
  const saveMutation = useMutation({
    mutationFn: (content: string) =>
      CourseService.updateLessonContent(courseId, lessonId, content),
    onSuccess: () => {
      toast({
        title: '保存成功',
        description: '脚本内容已保存',
      });
      queryClient.invalidateQueries({
        queryKey: ['lesson-script', courseId, lessonId],
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: error instanceof Error ? error.message : '无法保存脚本内容，请重试',
      });
    },
  });

  // 初始化编辑器内容
  useEffect(() => {
    if (scriptContent) {
      setContent(scriptContent);
    }
  }, [scriptContent]);

  const currentLesson = lessons?.find(lesson => lesson.id === lessonId);

  const handleSave = async () => {
    await saveMutation.mutateAsync(content);
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    const isAudio = file.type.startsWith('audio/');

    if (!isImage && !isVideo && !isAudio) {
      toast({
        variant: 'destructive',
        title: '上传失败',
        description: '请选择图片、视频或音频文件',
      });
      return;
    }

    // 检查文件大小
    const maxSize = isImage ? 5 * 1024 * 1024 : 
                   isVideo ? 100 * 1024 * 1024 : 
                   20 * 1024 * 1024; // 图片5MB，视频100MB，音频20MB
    if (file.size > maxSize) {
      toast({
        variant: 'destructive',
        title: '上传失败',
        description: `${isImage ? '图片' : isVideo ? '视频' : '音频'}大小不能超过${
          isImage ? '5MB' : isVideo ? '100MB' : '20MB'
        }`,
      });
      return;
    }

    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', file);

      const endpoint = isImage ? '/api/upload/image' : 
                      isVideo ? '/api/upload/video' : 
                      '/api/upload/audio';
      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const url = response.data.url;
      const mediaType = isImage ? 'image' : isVideo ? 'video' : 'audio';
      const mediaMarkdown = `[media:${mediaType} title="${file.name}"]`;
      
      // 在光标位置插入媒体 Markdown
      const textarea = document.querySelector('textarea');
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newContent = content.substring(0, start) + 
          mediaMarkdown + '\n' + 
          url + '\n\n' +
          content.substring(end);
        setContent(newContent);
      } else {
        // 如果找不到光标位置，就追加到末尾
        setContent(content + '\n' + mediaMarkdown + '\n' + url + '\n');
      }

      toast({
        title: '上传成功',
        description: `${isImage ? '图片' : isVideo ? '视频' : '音频'}已插入到编辑器中`,
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: '上传失败',
        description: error instanceof Error ? error.message : `无法上传${
          isImage ? '图片' : isVideo ? '视频' : '音频'
        }，请重试`,
      });
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleInsertAIPractice = () => {
    const aiPracticeTemplate = `[practice:chat mode="interactive"]
在这里写入你的练习要求和说明...
[/practice]

`;
    
    // 在光标位置插入模板
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + 
        aiPracticeTemplate +
        content.substring(end);
      setContent(newContent);
    } else {
      // 如果找不到光标位置，就追加到末尾
      setContent(content + '\n' + aiPracticeTemplate);
    }

    toast({
      title: '插入成功',
      description: 'AI对话练习模板已插入到编辑器中',
    });
  };

  const handleInsertAIImagePractice = () => {
    const aiImageTemplate = `[practice:image mode="create" size="1024x1024"]
在这里写入图片生成的要求和说明...
[/practice]

`;
    
    // 在光标位置插入模板
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + 
        aiImageTemplate +
        content.substring(end);
      setContent(newContent);
    } else {
      // 如果找不到光标位置，就追加到末尾
      setContent(content + '\n' + aiImageTemplate);
    }

    toast({
      title: '插入成功',
      description: 'AI绘画练习模板已插入到编辑器中',
    });
  };

  const handleInsertCode = () => {
    const codeTemplate = `\`\`\`javascript
// 在这里写入代码...
\`\`\`

`;
    
    // 在光标位置插入模板
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + 
        codeTemplate +
        content.substring(end);
      setContent(newContent);
    } else {
      // 如果找不到光标位置，就追加到末尾
      setContent(content + '\n' + codeTemplate);
    }

    toast({
      title: '插入成功',
      description: '代码块模板已插入到编辑器中',
    });
  };

  const handleAiGenerate = () => {
    setAiGenerateModalVisible(true);
  };

  const handleTextSelect = () => {
    const textarea = document.querySelector('textarea');
    if (textarea) {
      const selectedText = textarea.value.substring(
        textarea.selectionStart,
        textarea.selectionEnd
      );
      
      if (selectedText) {
        setSelectedContent(selectedText);
        setAiRegenerateModalVisible(true);
      } else {
        toast({
          title: '请先选择内容',
          description: '请在编辑器中选择要重新生成的内容',
          variant: 'destructive',
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-1">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-semibold tracking-tight">
            {isLoading ? '加载中...' : `${course?.title} - ${currentLesson?.title}`}
          </h2>
        </div>
        <p className="text-sm text-slate-500">
          编辑课程章节脚本内容
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <p className="text-sm text-slate-500 mb-2">AI 功能</p>
          <div className="flex items-center space-x-2">
            <Button
              variant="default"
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleAiGenerate}
            >
              <Wand2 className="h-4 w-4 mr-2" />
              AI 生成
            </Button>
            <Button
              variant="outline"
              onClick={handleTextSelect}
            >
              <Wand2 className="h-4 w-4 mr-2" />
              重新生成选中内容
            </Button>
          </div>
        </div>

        <div>
          <p className="text-sm text-slate-500 mb-2">媒体与练习插入</p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handleInsertAIPractice}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              插入AI对话练习
            </Button>
            <Button
              variant="outline"
              onClick={handleInsertAIImagePractice}
            >
              <Palette className="h-4 w-4 mr-2" />
              插入AI绘画练习
            </Button>
            <Button
              variant="outline"
              onClick={handleInsertCode}
            >
              <Code className="h-4 w-4 mr-2" />
              插入代码
            </Button>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              <Image className="h-4 w-4 mr-2" />
              {isUploading ? '上传中...' : '插入图片'}
            </Button>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              <Video className="h-4 w-4 mr-2" />
              {isUploading ? '上传中...' : '插入视频'}
            </Button>
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              <Music className="h-4 w-4 mr-2" />
              {isUploading ? '上传中...' : '插入音频'}
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex items-center justify-between mb-2">
          <TabsList>
            <TabsTrigger value="edit">编辑</TabsTrigger>
            <TabsTrigger value="preview">预览</TabsTrigger>
          </TabsList>
          <Button
            variant="default"
            className="bg-blue-600 hover:bg-blue-700"
            onClick={handleSave}
          >
            <Save className="h-4 w-4 mr-2" />
            保存
          </Button>
        </div>
        <TabsContent value="edit" className="mt-4">
          <CourseEditor
            value={content}
            onChange={setContent}
            onSave={handleSave}
          />
        </TabsContent>
        <TabsContent value="preview" className="mt-4">
          <div className={cn(
            "rounded-md border p-6",
            "prose prose-slate dark:prose-invert max-w-none",
            "min-h-[600px] bg-white dark:bg-slate-950"
          )}>
            <Markdown content={content} />
          </div>
        </TabsContent>
      </Tabs>

      {aiGenerateModalVisible && (
        <AiCourseGeneratorModal
          visible={aiGenerateModalVisible}
          onClose={() => {
            setAiGenerateModalVisible(false);
          }}
          onGenerated={(generatedContent) => {
            setContent(generatedContent);
            setAiGenerateModalVisible(false);
          }}
          courseTitle={course?.title || ''}
          currentLesson={{
            title: currentLesson?.title || '',
            description: currentLesson?.description || '',
            duration: currentLesson?.duration || '',
          }}
        />
      )}

      {aiRegenerateModalVisible && (
        <AiContentRegeneratorModal
          visible={aiRegenerateModalVisible}
          onClose={() => {
            setAiRegenerateModalVisible(false);
            setSelectedContent('');
          }}
          onGenerated={(generatedContent) => {
            const newContent = content.replace(selectedContent, generatedContent);
            setContent(newContent);
            setAiRegenerateModalVisible(false);
            setSelectedContent('');
          }}
          selectedContent={selectedContent}
        />
      )}

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*,video/*,audio/*"
        onChange={handleFileChange}
      />
    </div>
  );
} 