'use client';

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { AuthGuard } from '@/components/auth/auth-guard'
import { AdminLayout } from '@/components/layout/admin-layout'

interface AdminRootLayoutProps {
  children: React.ReactNode
}

export default function AdminRootLayout({ children }: AdminRootLayoutProps) {
  const pathname = usePathname()
  const isLoginPage = pathname === '/admin/login'
  const isAuthRulesPage = pathname === '/admin/auth-rules'
  const isPublicPage = isLoginPage || isAuthRulesPage
  const [mounted, setMounted] = useState(false)

  // 在客户端渲染后设置为已挂载
  useEffect(() => {
    setMounted(true)
  }, [])

  // 避免在服务器端渲染时输出任何内容，完全避免水合错误
  if (!mounted) {
    return null
  }

  // 公开页面不需要 AuthGuard 保护
  if (isPublicPage) {
    return <>{children}</>
  }

  return (
    <AuthGuard requireAdmin>
      <AdminLayout>{children}</AdminLayout>
    </AuthGuard>
  )
} 