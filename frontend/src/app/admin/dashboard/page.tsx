'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Users,
  FileText,
  Bot,
  TrendingUp,
  Activity,
  Search,
  Clock,
  BarChart2,
  LucideIcon,
} from 'lucide-react'

// 类型定义
type ActivityType = 'user' | 'search' | 'template'

interface Activity {
  id: number | string
  type: ActivityType
  action: string
  detail: string
  time: string
}

interface Overview {
  totalUsers: number
  activeUsers: number
  totalTemplates: number
  activeTemplates: number
  totalSearches: number
  avgResponseTime: string
}

interface Trends {
  userGrowth: string
  searchVolume: string
  aiUsage: string
}

interface Performance {
  cpu: number
  memory: number
  storage: number
  network: number
}

interface Stats {
  overview: Overview
  trends: Trends
  performance: Performance
}

interface PerformanceConfigItem {
  label: string
  value: keyof Performance
  color: string
}

type IconMap = {
  [K in ActivityType]: LucideIcon
}

// 模拟统计数据
const mockStats: Stats = {
  overview: {
    totalUsers: 1250,
    activeUsers: 850,
    totalTemplates: 45,
    activeTemplates: 38,
    totalSearches: 15680,
    avgResponseTime: '1.2s',
  },
  trends: {
    userGrowth: '+12%',
    searchVolume: '+25%',
    aiUsage: '+30%',
  },
  performance: {
    cpu: 32,
    memory: 45,
    storage: 28,
    network: 65,
  },
}

// 模拟最近活动数据
const mockRecentActivities: Activity[] = [
  {
    id: 1,
    type: 'user',
    action: '新用户注册',
    detail: '张三完成注册并开始使用系统',
    time: '10分钟前',
  },
  {
    id: 2,
    type: 'search',
    action: '批量检索',
    detail: '用户执行了50个专利的批量检索',
    time: '30分钟前',
  },
  {
    id: 3,
    type: 'template',
    action: '模版更新',
    detail: '管理员更新了专利分析报告模版',
    time: '1小时前',
  },
]

// 图标映射
const iconMap: IconMap = {
  user: Users,
  search: Search,
  template: FileText,
}

// 性能指标配置
const performanceConfig: PerformanceConfigItem[] = [
  { label: 'CPU 使用率', value: 'cpu', color: 'bg-blue-500' },
  { label: '内存使用率', value: 'memory', color: 'bg-green-500' },
  { label: '存储使用率', value: 'storage', color: 'bg-yellow-500' },
  { label: '网络使用率', value: 'network', color: 'bg-purple-500' },
]

export default function DashboardPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">仪表盘</h2>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="performance">性能</TabsTrigger>
          <TabsTrigger value="activity">活动</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* 数据概览卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总用户数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.overview.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  活跃用户: {mockStats.overview.activeUsers}
                </p>
                <div className="text-xs text-green-500 mt-1">
                  {mockStats.trends.userGrowth} 较上月
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">模版数量</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.overview.totalTemplates}</div>
                <p className="text-xs text-muted-foreground">
                  已启用: {mockStats.overview.activeTemplates}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">检索总量</CardTitle>
                <Search className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.overview.totalSearches}</div>
                <p className="text-xs text-muted-foreground">
                  平均响应时间: {mockStats.overview.avgResponseTime}
                </p>
                <div className="text-xs text-green-500 mt-1">
                  {mockStats.trends.searchVolume} 较上月
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 系统状态卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>系统状态</CardTitle>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Activity className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-sm">系统运行正常</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    CPU: {mockStats.performance.cpu}% | 内存: {mockStats.performance.memory}%
                  </div>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Bot className="mr-2 h-4 w-4 text-blue-500" />
                    <span className="text-sm">AI 服务状态正常</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    响应时间: {mockStats.overview.avgResponseTime}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="mr-2 h-4 w-4 text-orange-500" />
                    <span className="text-sm">系统负载正常</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    网络: {mockStats.performance.network}%
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 