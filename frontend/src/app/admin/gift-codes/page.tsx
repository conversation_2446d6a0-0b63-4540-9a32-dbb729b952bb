'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { GiftCodeList } from '@/components/gift-codes/gift-code-list'
import { api } from '@/lib/api/index'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Course } from '@/lib/services/course.service'

export default function GiftCodesPage() {
  const [loading, setLoading] = useState(false)
  const [courseId, setCourseId] = useState('')
  const [validDays, setValidDays] = useState('30')
  const [quantity, setQuantity] = useState('1')
  const [courses, setCourses] = useState<Course[]>([])
  const router = useRouter()

  useEffect(() => {
    // 获取课程列表
    const fetchCourses = async () => {
      try {
        const response = await api.get('/api/courses')
        setCourses(response.data)
      } catch (error) {
        console.error('获取课程列表失败:', error)
        toast.error('获取课程列表失败')
      }
    }

    fetchCourses()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!courseId) {
      toast.error('请选择课程')
      return
    }
    setLoading(true)

    try {
      const response = await api.post('/api/gift-codes', {
        courseId,
        validDays: parseInt(validDays),
        quantity: parseInt(quantity)
      })

      const codes = response.data
      toast.success(`成功创建 ${codes.length} 个礼品码`)
      window.location.reload()
    } catch (error) {
      console.error('创建礼品码失败:', error)
      toast.error('创建礼品码失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>创建礼品码</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="courseId">选择课程</Label>
              <Select value={courseId} onValueChange={setCourseId}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择课程" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.title} (¥{course.price})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="validDays">有效天数</Label>
              <Input
                id="validDays"
                type="number"
                value={validDays}
                onChange={(e) => setValidDays(e.target.value)}
                min="1"
                max="365"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                礼品码的有效期限（1-365天）
              </p>
            </div>
            <div>
              <Label htmlFor="quantity">生成数量</Label>
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                min="1"
                max="100"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                单次最多可生成100个礼品码
              </p>
            </div>
            <Button type="submit" disabled={loading}>
              {loading ? '创建中...' : '创建礼品码'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>礼品码列表</CardTitle>
        </CardHeader>
        <CardContent>
          <GiftCodeList />
        </CardContent>
      </Card>
    </div>
  )
} 