'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  Users, 
  TrendingUp, 
  Settings,
  Plus,
  BarChart3,
  DollarSign,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { api } from '@/lib/api';

interface SubscriptionStats {
  totalSubscribers: number;
  monthlyRevenue: number;
  activeTrials: number;
  conversionRate: number;
}

interface RecentActivity {
  id: string;
  type: 'new_subscription' | 'trial_ended' | 'cancelled' | 'upgraded';
  message: string;
  timestamp: string;
  userEmail?: string;
}

export default function AdminSubscriptionsPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 并行获取所有需要的数据
      const [statsResponse, activitiesResponse] = await Promise.all([
        api.get('/api/admin/subscription/stats').catch(() => null),
        api.get('/api/admin/subscription/activities').catch(() => null)
      ]);

      if (statsResponse) {
        setStats(statsResponse.data);
      } else {
        // 如果API不存在，使用计算的默认值
        setStats({
          totalSubscribers: 0,
          monthlyRevenue: 0,
          activeTrials: 0,
          conversionRate: 0
        });
      }

      if (activitiesResponse) {
        setRecentActivities(activitiesResponse.data);
      } else {
        setRecentActivities([]);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || '获取订阅数据失败');
      // 设置默认值以防出错
      setStats({
        totalSubscribers: 0,
        monthlyRevenue: 0,
        activeTrials: 0,
        conversionRate: 0
      });
      setRecentActivities([]);
    } finally {
      setLoading(false);
    }
  };

  const getActivityBadge = (type: string) => {
    switch (type) {
      case 'new_subscription':
        return <Badge variant="secondary">新订阅</Badge>;
      case 'trial_ended':
        return <Badge variant="outline">试用结束</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">取消订阅</Badge>;
      case 'upgraded':
        return <Badge variant="default">升级</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      
      if (diffMins < 1) return '刚刚';
      if (diffMins < 60) return `${diffMins}分钟前`;
      if (diffHours < 24) return `${diffHours}小时前`;
      return date.toLocaleDateString('zh-CN');
    } catch {
      return timestamp;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">订阅管理</h1>
          <p className="text-gray-600">管理订阅计划、用户和收入统计</p>
        </div>
        <Link href="/admin/subscriptions/plans/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            新建订阅计划
          </Button>
        </Link>
      </div>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订阅用户</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubscribers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                当前活跃订阅用户数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">月度收入</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${stats.monthlyRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                本月订阅收入总额
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">试用用户</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeTrials}</div>
              <p className="text-xs text-muted-foreground">
                活跃试用期用户
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">转化率</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.conversionRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                试用转付费用户比例
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 标签页内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="plans">订阅计划</TabsTrigger>
          <TabsTrigger value="users">用户订阅</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 最近活动 */}
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>最新的订阅相关活动</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentActivities.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暂无最近活动
                  </div>
                ) : (
                  recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getActivityBadge(activity.type)}
                        <span className="text-sm">{activity.message}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(activity.timestamp)}
                      </span>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
                <CardDescription>常用的管理操作</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/admin/subscriptions/plans">
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="w-4 h-4 mr-2" />
                    管理订阅计划
                  </Button>
                </Link>
                <Link href="/admin/subscriptions/users">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="w-4 h-4 mr-2" />
                    查看用户订阅
                  </Button>
                </Link>
                <Link href="/admin/subscriptions/analytics">
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    订阅分析报告
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <CardTitle>订阅计划管理</CardTitle>
              <CardDescription>配置和管理所有订阅计划</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">订阅计划管理功能正在开发中</p>
                <Link href="/admin/subscriptions/plans">
                  <Button>
                    <Settings className="w-4 h-4 mr-2" />
                    进入计划管理
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>用户订阅管理</CardTitle>
              <CardDescription>查看和管理所有用户的订阅状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">用户订阅管理功能正在开发中</p>
                <Link href="/admin/subscriptions/users">
                  <Button>
                    <Users className="w-4 h-4 mr-2" />
                    进入用户管理
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>数据分析</CardTitle>
              <CardDescription>订阅相关的数据分析和报告</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">数据分析功能正在开发中</p>
                <Button>
                  <TrendingUp className="w-4 h-4 mr-2" />
                  查看分析报告
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}