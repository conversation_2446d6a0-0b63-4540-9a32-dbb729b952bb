'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Save, AlertCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { api } from '@/lib/api';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'monthly' | 'quarterly' | 'yearly';
  durationDays: number;
  aiCallLimit: number;
  maxCourses: number | null;
  trialDays: number;
  features: string[];
  status: 'active' | 'inactive';
  stripePriceId: string | null;
}

export default function EditSubscriptionPlanPage() {
  const router = useRouter();
  const params = useParams();
  const planId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [featuresText, setFeaturesText] = useState('');

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    type: 'monthly' as 'monthly' | 'quarterly' | 'yearly',
    durationDays: 30,
    aiCallLimit: 1000,
    maxCourses: null as number | null,
    trialDays: 7,
    status: 'active' as 'active' | 'inactive',
    stripePriceId: ''
  });

  useEffect(() => {
    if (planId) {
      fetchPlan();
    }
  }, [planId]);

  const fetchPlan = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/subscription/plans/${planId}`);
      const planData = response.data;
      
      setPlan(planData);
      setFormData({
        name: planData.name,
        description: planData.description,
        price: planData.price,
        type: planData.type,
        durationDays: planData.durationDays,
        aiCallLimit: planData.aiCallLimit,
        maxCourses: planData.maxCourses,
        trialDays: planData.trialDays,
        status: planData.status,
        stripePriceId: planData.stripePriceId || ''
      });
      setFeaturesText(planData.features?.join('\n') || '');
    } catch (err: any) {
      setError(err.response?.data?.message || '获取订阅计划失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const features = featuresText
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      // 确保数字字段是正确的数据类型
      const updateData = {
        ...formData,
        price: Number(formData.price),
        durationDays: Number(formData.durationDays),
        aiCallLimit: Number(formData.aiCallLimit),
        maxCourses: formData.maxCourses ? Number(formData.maxCourses) : null,
        trialDays: Number(formData.trialDays),
        features,
        stripePriceId: formData.stripePriceId.trim() || null
      };

      console.log('Sending update data:', updateData);
      await api.put(`/api/subscription/plans/${planId}`, updateData);
      router.push('/admin/subscriptions/plans');
    } catch (err: any) {
      console.error('Update subscription plan error:', err);
      console.error('Error response:', err.response?.data);
      
      let errorMessage = '更新订阅计划失败';
      
      if (err.response?.data) {
        if (err.response.data.message) {
          errorMessage = err.response.data.message;
        } else if (err.response.data.error) {
          errorMessage = err.response.data.error;
        } else if (Array.isArray(err.response.data.message)) {
          errorMessage = err.response.data.message.join('; ');
        }
      }
      
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!plan) {
    return (
      <div className="space-y-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>订阅计划不存在或已被删除</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center gap-4">
        <Link href="/admin/subscriptions/plans">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">编辑订阅计划</h1>
          <p className="text-gray-600">修改订阅计划配置和价格信息</p>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>设置订阅计划的基本信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">计划名称</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  placeholder="例如：专业版"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">计划描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="描述这个订阅计划的特点"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">价格 (USD)</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.price}
                    onChange={(e) => updateFormData('price', parseFloat(e.target.value) || 0)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">计费周期</Label>
                  <Select value={formData.type} onValueChange={(value) => updateFormData('type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">月付</SelectItem>
                      <SelectItem value="quarterly">季付</SelectItem>
                      <SelectItem value="yearly">年付</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 功能配置 */}
          <Card>
            <CardHeader>
              <CardTitle>功能配置</CardTitle>
              <CardDescription>设置订阅计划包含的功能和限制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="aiCallLimit">AI对话次数限制</Label>
                <Input
                  id="aiCallLimit"
                  type="number"
                  min="0"
                  value={formData.aiCallLimit}
                  onChange={(e) => updateFormData('aiCallLimit', parseInt(e.target.value) || 0)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxCourses">最大课程数量</Label>
                <Input
                  id="maxCourses"
                  type="number"
                  min="0"
                  value={formData.maxCourses || ''}
                  onChange={(e) => updateFormData('maxCourses', e.target.value ? parseInt(e.target.value) : null)}
                  placeholder="留空表示无限制"
                />
                <p className="text-sm text-gray-500">留空表示无限制访问所有课程</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="durationDays">有效期 (天)</Label>
                  <Input
                    id="durationDays"
                    type="number"
                    min="1"
                    value={formData.durationDays}
                    onChange={(e) => updateFormData('durationDays', parseInt(e.target.value) || 1)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="trialDays">免费试用期 (天)</Label>
                  <Input
                    id="trialDays"
                    type="number"
                    min="0"
                    value={formData.trialDays}
                    onChange={(e) => updateFormData('trialDays', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stripe 配置 */}
        <Card>
          <CardHeader>
            <CardTitle>Stripe 支付配置</CardTitle>
            <CardDescription>配置 Stripe 支付相关信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="stripePriceId">Stripe Price ID</Label>
              <Input
                id="stripePriceId"
                value={formData.stripePriceId}
                onChange={(e) => updateFormData('stripePriceId', e.target.value)}
                placeholder="例如：price_1234567890abcdef"
              />
              <p className="text-sm text-gray-500">
                需要先在 Stripe 后台创建对应的价格，然后将 Price ID 填写在这里
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 功能特性 */}
        <Card>
          <CardHeader>
            <CardTitle>功能特性</CardTitle>
            <CardDescription>列出这个订阅计划的特色功能，每行一个特性</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="features">特性列表</Label>
              <Textarea
                id="features"
                value={featuresText}
                onChange={(e) => setFeaturesText(e.target.value)}
                placeholder="每行填写一个特性，例如：&#10;无限AI对话&#10;专属客服支持&#10;优先访问新功能"
                rows={6}
              />
            </div>
          </CardContent>
        </Card>

        {/* 状态设置 */}
        <Card>
          <CardHeader>
            <CardTitle>状态设置</CardTitle>
            <CardDescription>控制订阅计划的启用状态</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="status">启用状态</Label>
                <p className="text-sm text-gray-500">
                  停用的计划不会在前端显示，但现有订阅不受影响
                </p>
              </div>
              <Switch
                id="status"
                checked={formData.status === 'active'}
                onCheckedChange={(checked) => updateFormData('status', checked ? 'active' : 'inactive')}
              />
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-4">
          <Link href="/admin/subscriptions/plans">
            <Button type="button" variant="outline">
              取消
            </Button>
          </Link>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            <Save className="w-4 h-4 mr-2" />
            保存更改
          </Button>
        </div>
      </form>
    </div>
  );
}