'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Eye,
  MoreHorizontal,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/lib/api';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'monthly' | 'quarterly' | 'yearly';
  durationDays: number;
  aiCallLimit: number;
  maxCourses: number | null;
  trialDays: number;
  features: string[];
  status: 'active' | 'inactive';
  stripePriceId: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function AdminSubscriptionPlansPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/subscription/plans');
      setPlans(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取订阅计划失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('确定要删除这个订阅计划吗？此操作不可恢复。')) {
      return;
    }

    try {
      await api.delete(`/api/subscription/plans/${planId}`);
      setPlans(plans.filter(plan => plan.id !== planId));
    } catch (err: any) {
      alert(err.response?.data?.message || '删除失败');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getPlanTypeText = (type: string) => {
    switch (type) {
      case 'monthly':
        return '月付';
      case 'quarterly':
        return '季付';
      case 'yearly':
        return '年付';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: string, stripePriceId: string | null) => {
    if (!stripePriceId) {
      return <Badge variant="destructive">未配置Stripe</Badge>;
    }
    
    return status === 'active' ? (
      <Badge variant="default">启用</Badge>
    ) : (
      <Badge variant="secondary">停用</Badge>
    );
  };

  const filteredPlans = plans.filter(plan =>
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    plan.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">订阅计划管理</h1>
          <p className="text-gray-600">管理所有订阅计划和价格配置</p>
        </div>
        <Link href="/admin/subscriptions/plans/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            新建计划
          </Button>
        </Link>
      </div>

      {/* 搜索栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索订阅计划..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 订阅计划列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订阅计划列表</CardTitle>
          <CardDescription>
            共 {filteredPlans.length} 个计划
            {searchQuery && ` (搜索: "${searchQuery}")`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredPlans.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-4">暂无订阅计划</div>
              <Link href="/admin/subscriptions/plans/new">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  创建第一个计划
                </Button>
              </Link>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>计划名称</TableHead>
                  <TableHead>价格</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>AI对话次数</TableHead>
                  <TableHead>课程数量</TableHead>
                  <TableHead>Stripe配置</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPlans.map((plan) => (
                  <TableRow key={plan.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{plan.name}</div>
                        <div className="text-sm text-gray-500">{plan.description}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono">
                      {formatPrice(plan.price)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getPlanTypeText(plan.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {plan.aiCallLimit.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {plan.maxCourses ? plan.maxCourses.toString() : '无限制'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {plan.stripePriceId ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-gray-600">已配置</span>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-4 h-4 text-red-500" />
                            <span className="text-sm text-red-600">未配置</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(plan.status, plan.stripePriceId)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <Link href={`/admin/subscriptions/plans/${plan.id}`}>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                          </Link>
                          <Link href={`/admin/subscriptions/plans/${plan.id}/edit`}>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                          </Link>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeletePlan(plan.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* 温馨提示 */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-blue-800">
              <div className="font-medium mb-1">Stripe Price ID 配置说明</div>
              <div className="text-sm">
                每个订阅计划都需要在 Stripe 后台创建对应的价格，并在这里配置 Price ID。
                没有配置 Stripe Price ID 的计划无法用于支付。
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}