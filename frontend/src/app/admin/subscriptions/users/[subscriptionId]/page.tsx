'use client';

import { useState, useEffect } from 'react';
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  User,
  Calendar,
  CreditCard,
  Settings,
  Clock,
  DollarSign,
  Loader2,
  AlertCircle,
  CheckCircle,
  Save
} from 'lucide-react';
import { api } from '@/lib/api';

interface UserSubscriptionDetail {
  id: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
  planName: string;
  status: 'active' | 'trial' | 'cancelled' | 'expired';
  price: number;
  isActive: boolean;
  startDate: string;
  endDate: string;
  trialEndDate?: string;
  createdAt: string;
  updatedAt: string;
  plan?: {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
  };
  paymentHistory: {
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }[];
}

export default function SubscriptionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const subscriptionId = params.subscriptionId as string;

  const [subscription, setSubscription] = useState<UserSubscriptionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 管理操作状态
  const [newStatus, setNewStatus] = useState('');
  const [extensionDays, setExtensionDays] = useState('');

  useEffect(() => {
    if (subscriptionId) {
      fetchSubscriptionDetail();
    }
  }, [subscriptionId]);

  const fetchSubscriptionDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get(`/api/admin/subscription/users/${subscriptionId}`);
      const data: UserSubscriptionDetail = response.data;
      
      setSubscription(data);
      setNewStatus(data.status);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取订阅详情失败');
    } finally {
      setLoading(false);
    }
  };

  const updateSubscriptionStatus = async () => {
    if (!newStatus || newStatus === subscription?.status) return;

    try {
      setUpdating(true);
      setError(null);
      setSuccess(null);

      await api.put(`/api/admin/subscription/users/${subscriptionId}/status`, {
        status: newStatus
      });

      setSuccess('订阅状态已更新');
      await fetchSubscriptionDetail();
    } catch (err: any) {
      setError(err.response?.data?.message || '更新订阅状态失败');
    } finally {
      setUpdating(false);
    }
  };

  const extendSubscription = async () => {
    const days = parseInt(extensionDays);
    if (!days || days <= 0) {
      setError('请输入有效的延长天数');
      return;
    }

    try {
      setUpdating(true);
      setError(null);
      setSuccess(null);

      await api.put(`/api/admin/subscription/users/${subscriptionId}/extend`, {
        extensionDays: days
      });

      setSuccess(`订阅期限已延长 ${days} 天`);
      setExtensionDays('');
      await fetchSubscriptionDetail();
    } catch (err: any) {
      setError(err.response?.data?.message || '延长订阅期限失败');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusBadge = (status: string, isActive: boolean) => {
    if (!isActive) {
      return <Badge variant="secondary">已停用</Badge>;
    }

    switch (status) {
      case 'active':
        return <Badge variant="default">活跃</Badge>;
      case 'trial':
        return <Badge variant="outline">试用</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">已取消</Badge>;
      case 'expired':
        return <Badge variant="secondary">已过期</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error && !subscription) {
    return (
      <div className="space-y-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">订阅不存在</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-3xl font-bold">订阅详情</h1>
          <p className="text-gray-600">管理用户订阅信息</p>
        </div>
      </div>

      {/* 消息提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="w-5 h-5" />
              <span>{success}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 用户信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                用户信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">用户名</Label>
                  <p className="text-sm">{subscription.user.name || subscription.user.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">邮箱</Label>
                  <p className="text-sm">{subscription.user.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 订阅信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                订阅信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">订阅计划</Label>
                  <p className="text-sm font-medium">{subscription.planName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">状态</Label>
                  <div className="mt-1">
                    {getStatusBadge(subscription.status, subscription.isActive)}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">价格</Label>
                  <p className="text-sm font-medium">{formatPrice(subscription.price)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">订阅ID</Label>
                  <p className="text-sm text-gray-500 font-mono">{subscription.id}</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">开始日期</Label>
                  <p className="text-sm">{formatDate(subscription.startDate)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">结束日期</Label>
                  <p className="text-sm">{formatDate(subscription.endDate)}</p>
                </div>
                {subscription.trialEndDate && (
                  <>
                    <div>
                      <Label className="text-sm font-medium text-gray-600">试用结束日期</Label>
                      <p className="text-sm">{formatDate(subscription.trialEndDate)}</p>
                    </div>
                  </>
                )}
                <div>
                  <Label className="text-sm font-medium text-gray-600">创建时间</Label>
                  <p className="text-sm">{formatDate(subscription.createdAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 计划详情 */}
          {subscription.plan && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  计划详情
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-600">计划名称</Label>
                    <p className="text-sm">{subscription.plan.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">计划价格</Label>
                    <p className="text-sm">{formatPrice(subscription.plan.price)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-600">计划时长</Label>
                    <p className="text-sm">{subscription.plan.duration} 天</p>
                  </div>
                </div>
                {subscription.plan.description && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">计划描述</Label>
                    <p className="text-sm">{subscription.plan.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* 管理操作 */}
        <div className="space-y-6">
          {/* 状态管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                状态管理
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">订阅状态</Label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">活跃</SelectItem>
                    <SelectItem value="trial">试用</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                    <SelectItem value="expired">已过期</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={updateSubscriptionStatus}
                disabled={updating || newStatus === subscription.status}
                className="w-full"
              >
                {updating ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                更新状态
              </Button>
            </CardContent>
          </Card>

          {/* 期限管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                期限管理
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="extension">延长天数</Label>
                <Input
                  id="extension"
                  type="number"
                  placeholder="输入要延长的天数"
                  value={extensionDays}
                  onChange={(e) => setExtensionDays(e.target.value)}
                  min="1"
                />
              </div>
              <Button 
                onClick={extendSubscription}
                disabled={updating || !extensionDays}
                className="w-full"
              >
                {updating ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Calendar className="w-4 h-4 mr-2" />
                )}
                延长订阅
              </Button>
            </CardContent>
          </Card>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/subscriptions/users">
                <Button variant="outline" className="w-full">
                  返回订阅列表
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}