'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Search, 
  Filter, 
  Eye, 
  Users,
  Loader2,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { api } from '@/lib/api';

interface UserSubscription {
  id: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
  planName: string;
  status: 'active' | 'trial' | 'cancelled' | 'expired';
  price: number;
  isActive: boolean;
  startDate: string;
  endDate: string;
  trialEndDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserSubscriptionListResponse {
  data: UserSubscription[];
  total: number;
  page: number;
  limit: number;
}

export default function AdminSubscriptionUsersPage() {
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 查询参数
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [limit] = useState(20);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string>('ALL');
  const [searchInputValue, setSearchInputValue] = useState('');

  useEffect(() => {
    fetchSubscriptions();
  }, [page, search, status]);

  const fetchSubscriptions = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (search) params.append('search', search);
      if (status && status !== 'ALL') params.append('status', status);

      const response = await api.get(`/api/admin/subscription/users?${params}`);
      const data: UserSubscriptionListResponse = response.data;

      setSubscriptions(data.data);
      setTotal(data.total);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取用户订阅数据失败');
      setSubscriptions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setSearch(searchInputValue.trim());
    setPage(1);
  };


  const getStatusBadge = (status: string, isActive: boolean) => {
    if (!isActive) {
      return <Badge variant="secondary">已停用</Badge>;
    }

    switch (status) {
      case 'active':
        return <Badge variant="default">活跃</Badge>;
      case 'trial':
        return <Badge variant="outline">试用</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">已取消</Badge>;
      case 'expired':
        return <Badge variant="secondary">已过期</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(price);
  };

  const totalPages = Math.ceil(total / limit);

  if (loading && subscriptions.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">用户订阅管理</h1>
          <p className="text-gray-600">查看和管理所有用户的订阅状态</p>
        </div>
        <Link href="/admin/subscriptions">
          <Button variant="outline">
            返回订阅概览
          </Button>
        </Link>
      </div>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            搜索和筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="flex gap-2">
                <Input
                  placeholder="搜索用户邮箱或用户名..."
                  value={searchInputValue}
                  onChange={(e) => setSearchInputValue(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button onClick={handleSearch}>
                  <Search className="w-4 h-4 mr-2" />
                  搜索
                </Button>
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择订阅状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部状态</SelectItem>
                  <SelectItem value="active">活跃</SelectItem>
                  <SelectItem value="trial">试用</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 订阅统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总订阅数</p>
                <p className="text-2xl font-bold">{total}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 订阅列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订阅列表</CardTitle>
          <CardDescription>
            共 {total} 条订阅记录，当前第 {page} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          ) : subscriptions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无订阅数据
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户</TableHead>
                    <TableHead>订阅计划</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>结束日期</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscriptions.map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{subscription.user.name || subscription.user.email}</div>
                          <div className="text-sm text-gray-500">{subscription.user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {subscription.planName}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(subscription.status, subscription.isActive)}
                      </TableCell>
                      <TableCell>
                        {formatPrice(subscription.price)}
                      </TableCell>
                      <TableCell>
                        {formatDate(subscription.startDate)}
                      </TableCell>
                      <TableCell>
                        {formatDate(subscription.endDate)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Link href={`/admin/subscriptions/users/${subscription.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-1" />
                              查看
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    显示第 {(page - 1) * limit + 1} - {Math.min(page * limit, total)} 条，
                    共 {total} 条记录
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(Math.max(1, page - 1))}
                      disabled={page <= 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      上一页
                    </Button>
                    <span className="flex items-center px-3 text-sm">
                      第 {page} / {totalPages} 页
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(Math.min(totalPages, page + 1))}
                      disabled={page >= totalPages}
                    >
                      下一页
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}