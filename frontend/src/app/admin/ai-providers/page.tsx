'use client';

import { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Card } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Search, Plus, BarChart2, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AiProvider, ProviderType, ProviderStatus, ProviderDefaultType } from '@/types/ai';
import { fetchAiProviders } from '@/lib/api/admin';
import { AiProviderDialog } from '@/components/admin/ai-provider-dialog';
import { testAiProvider } from '@/services/ai-provider';
import TestResultCard from '@/components/ai-provider/TestResultCard';
import { TestResult } from '@/types/ai-provider';
import dayjs from 'dayjs';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { api } from '@/lib/api/index';

const typeMap: Record<ProviderType, { label: string; className: string }> = {
  [ProviderType.OPENAI]: { label: 'OpenAI', className: 'bg-blue-500/10 text-blue-700' },
  [ProviderType.AZURE_OPENAI]: { label: 'Azure OpenAI', className: 'bg-purple-500/10 text-purple-700' },
  [ProviderType.ANTHROPIC]: { label: 'Anthropic', className: 'bg-indigo-500/10 text-indigo-700' },
  [ProviderType.MOONSHOT]: { label: '豆包', className: 'bg-green-500/10 text-green-700' },
  [ProviderType.GEMINI]: { label: 'Google Gemini', className: 'bg-orange-500/10 text-orange-700' },
  [ProviderType.VOLCENGINE]: { label: '火山引擎', className: 'bg-red-500/10 text-red-700' },
  [ProviderType.MISTRAL]: { label: 'Mistral AI', className: 'bg-teal-500/10 text-teal-700' },
  [ProviderType.DEEPSEEK]: { label: 'Deepseek', className: 'bg-pink-500/10 text-pink-700' },
  [ProviderType.FLUX]: { label: 'Flux', className: 'bg-violet-500/10 text-violet-700' },
  [ProviderType.SILICONFLOW]: { label: 'SiliconFlow', className: 'bg-cyan-500/10 text-cyan-700' },
  [ProviderType.CUSTOM]: { label: '自定义', className: 'bg-gray-500/10 text-gray-700' },
};

const statusMap: Record<ProviderStatus, { label: string; className: string }> = {
  [ProviderStatus.ACTIVE]: { label: '已启用', className: 'bg-green-500/10 text-green-700' },
  [ProviderStatus.INACTIVE]: { label: '已禁用', className: 'bg-gray-500/10 text-gray-700' },
  [ProviderStatus.TESTING]: { label: '测试中', className: 'bg-yellow-500/10 text-yellow-700' },
};

export default function AIProvidersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState<AiProvider | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUsageDialogOpen, setIsUsageDialogOpen] = useState(false);
  const [testingId, setTestingId] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isTestResultModalOpen, setIsTestResultModalOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 使用 react-query 获取数据
  const { data: providers = [], isLoading } = useQuery({
    queryKey: ['admin', 'ai-providers'],
    queryFn: fetchAiProviders,
  });

  // 搜索过滤
  const filteredProviders = providers.filter(provider =>
    provider.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 检查是否已有默认服务商
  const hasDefaultImageProvider = providers.some(p => p.defaultType?.toLowerCase() === ProviderDefaultType.IMAGE.toLowerCase());
  const hasDefaultTextProvider = providers.some(p => p.defaultType?.toLowerCase() === ProviderDefaultType.TEXT.toLowerCase());

  // 打开添加对话框
  const handleAddProvider = () => {
    setSelectedProvider(null);
    setIsDialogOpen(true);
  };

  // 打开编辑对话框
  const handleEditProvider = (provider: AiProvider) => {
    setSelectedProvider(provider);
    setIsDialogOpen(true);
  };

  // 查看用量
  const handleViewUsage = (provider: AiProvider) => {
    setSelectedProvider(provider);
    setIsUsageDialogOpen(true);
  };

  const handleTest = async (id: string) => {
    try {
      setTestingId(id);
      setTestResult(null);
      const result = await testAiProvider(id);
      setTestResult(result);
      setIsTestResultModalOpen(true);
      toast({
        title: "测试完成",
        description: result.success ? "测试通过" : "测试失败",
        variant: result.success ? "default" : "destructive",
      });
      // 刷新列表以获取最新的测试状态
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
    } catch (error) {
      toast({
        title: "测试失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setTestingId(null);
    }
  };

  // 设置为默认服务商
  const handleSetDefault = async (provider: AiProvider) => {
    try {
      await api.put(`/api/admin/ai-providers/${provider.id}/default`);
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
      const serviceType = provider.type === ProviderType.FLUX ? '图片' : '文本';
      toast({
        title: '设置成功',
        description: `已将 ${provider.name} 设置为默认${serviceType}服务商`,
      });
    } catch (error) {
      toast({
        title: '设置失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  };

  // 用/禁用服务商
  const handleToggleStatus = async (provider: AiProvider) => {
    try {
      const newStatus = provider.status === ProviderStatus.ACTIVE
        ? ProviderStatus.INACTIVE
        : ProviderStatus.ACTIVE;

      await api.put(`/api/admin/ai-providers/${provider.id}`, {
        status: newStatus,
      });
      
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
      toast({
        title: '状态更新成功',
        description: `已${newStatus === ProviderStatus.ACTIVE ? '启用' : '禁用'} ${provider.name}`,
      });
    } catch (error) {
      toast({
        title: '状态更新失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  };

  // 删除服务商
  const handleDelete = async (provider: AiProvider) => {
    try {
      if (!window.confirm(`确定要删除服务商 "${provider.name}" 吗？此操作不可恢复。`)) {
        return;
      }

      await api.delete(`/api/admin/ai-providers/${provider.id}`);
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
      toast({
        title: '删除成功',
        description: `已删除服务商 ${provider.name}`,
      });
    } catch (error) {
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">AI 服务商管理</h1>
          <Button onClick={handleAddProvider}>
            <Plus className="mr-2 h-4 w-4" />
            添加服务商
          </Button>
        </div>

        <Card>
          <div className="p-4 flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索服务商..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>服务商</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>API 地址</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>更新时间</TableHead>
                <TableHead>最近测试</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredProviders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    没有找到匹配的服务商
                  </TableCell>
                </TableRow>
              ) : (
                filteredProviders.map((provider) => (
                  <TableRow key={provider.id}>
                    <TableCell className="font-medium">{provider.name}</TableCell>
                    <TableCell>
                      <Badge className={typeMap[provider.type]?.className}>
                        {typeMap[provider.type]?.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Badge className={statusMap[provider.status]?.className}>
                          {statusMap[provider.status]?.label}
                        </Badge>
                        {provider.defaultType && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            默认{provider.defaultType === ProviderDefaultType.IMAGE ? '图片' : '文本'}服务商
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{provider.config.apiEndpoint || '默认'}</TableCell>
                    <TableCell>{new Date(provider.createdAt).toLocaleString()}</TableCell>
                    <TableCell>{new Date(provider.updatedAt).toLocaleString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {provider.lastTestAt && (
                          <Tooltip>
                            <TooltipTrigger>
                              <Badge 
                                variant={provider.lastTestResult?.success ? "default" : "destructive"}
                                className={cn(
                                  "cursor-help",
                                  provider.lastTestResult?.success 
                                    ? "bg-green-100 text-green-700 hover:bg-green-200"
                                    : "bg-red-100 text-red-700 hover:bg-red-200"
                                )}
                              >
                                {provider.lastTestResult?.success ? '通过' : '失败'}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>最近测试时间：{dayjs(provider.lastTestAt).format('YYYY-MM-DD HH:mm:ss')}</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        <Button 
                          variant="ghost"
                          size="sm"
                          onClick={() => handleTest(provider.id)}
                          disabled={testingId === provider.id}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {testingId === provider.id ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              测试中
                            </>
                          ) : (
                            '测试'
                          )}
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleEditProvider(provider)}>
                            编辑配置
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewUsage(provider)}>
                            查看用量
                          </DropdownMenuItem>
                          {provider.status === ProviderStatus.ACTIVE && (
                            <>
                              {provider.type === ProviderType.FLUX ? (
                                provider.defaultType !== ProviderDefaultType.IMAGE && (
                                  <DropdownMenuItem onClick={() => handleSetDefault(provider)}>
                                    设为默认图片服务商
                                  </DropdownMenuItem>
                                )
                              ) : (
                                provider.defaultType !== ProviderDefaultType.TEXT && (
                                  <DropdownMenuItem onClick={() => handleSetDefault(provider)}>
                                    设为默认文本服务商
                                  </DropdownMenuItem>
                                )
                              )}
                            </>
                          )}
                          {(provider.status === ProviderStatus.INACTIVE || 
                            provider.status === ProviderStatus.TESTING) && (
                            <DropdownMenuItem 
                              onClick={() => handleToggleStatus(provider)}
                              className="text-green-600 focus:text-green-600 focus:bg-green-50"
                            >
                              启用服务
                            </DropdownMenuItem>
                          )}
                          {provider.status === ProviderStatus.ACTIVE && (
                            <DropdownMenuItem 
                              onClick={() => handleToggleStatus(provider)}
                              className="text-red-600 focus:text-red-600 focus:bg-red-50"
                            >
                              禁用服务
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            onClick={() => handleDelete(provider)}
                            className="text-red-600 focus:text-red-600 focus:bg-red-50"
                          >
                            删除服务商
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Card>

        {/* 使用新的 AiProviderDialog 组件 */}
        <AiProviderDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          provider={selectedProvider}
        />

        {/* 用量统计对话框 */}
        <Dialog open={isUsageDialogOpen} onOpenChange={setIsUsageDialogOpen}>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>用量统计</DialogTitle>
            </DialogHeader>
            <div className="mt-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">API 调用次数</p>
                      <p className="text-2xl font-bold">
                        {selectedProvider?.requestCount?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <BarChart2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                </Card>
              </div>
              <Card className="p-4">
                <h3 className="font-medium mb-2">最近使用趋势</h3>
                <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                  图表区域（开发中）
                </div>
              </Card>
            </div>
          </DialogContent>
        </Dialog>

        {/* 测试结果对话框 */}
        <Dialog open={isTestResultModalOpen} onOpenChange={setIsTestResultModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>测试结果</DialogTitle>
            </DialogHeader>
            {testResult && (
              <TestResultCard 
                status={testResult.success} 
                message={testResult.error} 
                details={testResult.details}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
} 