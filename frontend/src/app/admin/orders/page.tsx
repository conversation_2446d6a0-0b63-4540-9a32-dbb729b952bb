'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Download, Search, Eye } from 'lucide-react';
import { DatePicker } from '@/components/ui/date-picker';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
} from '@/components/ui/pagination';

// 分页配置
const PAGE_SIZE = 10;

interface Order {
  id: string;
  orderNo: string;
  amount: number;
  status: string;
  createdAt: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
  course?: {
    id: string;
    title: string;
  };
}

interface OrderListResponse {
  items: Order[];
  total: number;
  page: number;
  pageSize: number;
}

export default function OrdersPage() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取订单数据
  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<OrderListResponse>('/api/payment/admin/orders', {
        params: {
          page,
          pageSize,
        },
      });
      setOrders(response.data.items);
      setTotal(response.data.total);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: '获取订单失败',
        description: '请稍后重试',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 导出订单数据为CSV
  const exportOrders = async () => {
    try {
      const response = await api.get('/api/payment/admin/orders', {
        params: {
          page: 1,
          pageSize: totalOrders,
          status: statusFilter === 'all' ? undefined : statusFilter,
          startDate: dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
          endDate: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined,
          search: searchTerm || undefined,
        },
      });

      const headers = ['订单ID', '用户ID', '用户名', '课程ID', '课程名称', '金额', '状态', '创建时间', '支付时间'];
      const csvContent = [
        headers.join(','),
        ...response.data.orders.map((order: Order) => [
          order.orderNo,
          order.userId,
          order.userName,
          order.courseId,
          order.courseName,
          order.amount,
          order.status,
          order.createTime,
          order.paymentTime,
        ].join(',')),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `orders_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      toast({
        title: '导出订单失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    }
  };

  // 监听筛选条件变化
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, dateRange]);

  // 监听分页变化
  useEffect(() => {
    fetchOrders();
  }, [page, pageSize, toast]);

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { variant: 'default' | 'secondary' | 'destructive' | 'outline', text: string }> = {
      'PENDING': { variant: 'secondary', text: '待支付' },
      'PAID': { variant: 'default', text: '已支付' },
      'CANCELLED': { variant: 'outline', text: '已取消' },
      'REFUNDED': { variant: 'destructive', text: '已退款' },
      'EXPIRED': { variant: 'destructive', text: '已过期' },
    };

    const statusInfo = statusMap[status] || { variant: 'outline', text: status };
    return <Badge variant={statusInfo.variant}>{statusInfo.text}</Badge>;
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">订单管理</h1>
        <Button onClick={exportOrders} disabled={isLoading}>
          <Download className="mr-2 h-4 w-4" />
          导出订单
        </Button>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索订单ID、用户名或课程名称..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="订单状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="已完成">已完成</SelectItem>
            <SelectItem value="待支付">待支付</SelectItem>
            <SelectItem value="已取消">已取消</SelectItem>
          </SelectContent>
        </Select>
        <DatePicker
          date={dateRange}
          onSelect={setDateRange}
          placeholder="选择日期范围"
          mode="range"
          locale={zhCN}
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      ) : orders && orders.length > 0 ? (
        <div className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单号</TableHead>
                <TableHead>用户</TableHead>
                <TableHead>课程</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.orderNo}</TableCell>
                  <TableCell>{order.user?.username || order.user?.email}</TableCell>
                  <TableCell>{order.course?.title || '-'}</TableCell>
                  <TableCell>¥{order.amount}</TableCell>
                  <TableCell>{getStatusBadge(order.status)}</TableCell>
                  <TableCell>{format(new Date(order.createdAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}</TableCell>
                  <TableCell>
                    <Button variant="ghost" asChild>
                      <Link href={`/admin/orders/${order.orderNo}`}>
                        查看详情
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setPage((p) => Math.max(1, p - 1))}
                    disabled={page === 1}
                  />
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext
                    onClick={() => setPage((p) => p + 1)}
                    disabled={page * pageSize >= total}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      ) : (
        <div className="text-center py-10">
          <p className="text-gray-500">暂无订单数据</p>
        </div>
      )}
    </div>
  );
} 