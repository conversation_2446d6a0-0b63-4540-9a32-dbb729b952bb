'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/lib/api';
import { ArrowLeft, RefreshCw, Pencil } from 'lucide-react';
import Link from 'next/link';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface Order {
  orderNo: string;
  status: string;
  amount: number;
  createdAt: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
  course: {
    id: string;
    title: string;
    price: number;
  };
  payment?: {
    paymentNo: string;
    status: string;
    paidAt?: string;
  };
  refund?: {
    refundNo: string;
    status: string;
    amount: number;
    reason?: string;
    createdAt: string;
  };
}

// 添加状态映射
const statusMap: Record<string, string> = {
  '待支付': 'PENDING',
  '已支付': 'PAID',
  '已取消': 'CANCELLED',
  '已退款': 'REFUNDED',
  '已过期': 'EXPIRED',
};

export default function OrderDetailPage() {
  const { orderNo } = useParams();
  const { toast } = useToast();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [reason, setReason] = useState('');

  const fetchOrder = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(`/api/payment/admin/orders/${orderNo}`);
      setOrder(response.data);
    } catch (error) {
      toast({
        title: '获取订单详情失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrder();
  }, [orderNo]);

  const handleStatusUpdate = async () => {
    try {
      setIsUpdating(true);
      // 将中文状态转换为英文状态
      const mappedStatus = statusMap[newStatus];
      if (!mappedStatus) {
        toast({
          title: '更新失败',
          description: '无效的订单状态',
          variant: 'destructive',
        });
        return;
      }

      const response = await api.post(`/api/payment/admin/orders/${orderNo}/status`, {
        status: mappedStatus,
        reason: reason || undefined,
      });
      setOrder(response.data);
      toast({
        title: '订单状态更新成功',
        description: '订单状态已更新',
      });
    } catch (error) {
      toast({
        title: '更新订单状态失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-gray-500">订单不存在</p>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, string> = {
      '已完成': 'bg-green-100 text-green-800',
      '待支付': 'bg-yellow-100 text-yellow-800',
      '已取消': 'bg-red-100 text-red-800',
      '已退款': 'bg-purple-100 text-purple-800',
    };
    return (
      <Badge className={statusMap[status] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8">
      <div className="flex items-center justify-between">
        <Button variant="ghost" asChild>
          <Link href="/admin/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回订单列表
          </Link>
        </Button>
        <div className="flex items-center space-x-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Pencil className="mr-2 h-4 w-4" />
                修改状态
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>修改订单状态</DialogTitle>
                <DialogDescription>
                  请选择新的订单状态，并填写修改原因（可选）
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="status">订单状态</Label>
                  <Select value={newStatus} onValueChange={setNewStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="已完成">已完成</SelectItem>
                      <SelectItem value="待支付">待支付</SelectItem>
                      <SelectItem value="已取消">已取消</SelectItem>
                      <SelectItem value="已退款">已退款</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reason">修改原因</Label>
                  <Textarea
                    id="reason"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="请输入修改原因（可选）"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={handleStatusUpdate}
                  disabled={!newStatus || isUpdating}
                >
                  {isUpdating ? '更新中...' : '确认修改'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <Button variant="outline" onClick={fetchOrder}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>订单信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-500">订单号</span>
                <span>{order.orderNo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">状态</span>
                {getStatusBadge(order.status)}
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">金额</span>
                <span>¥{order.amount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">创建时间</span>
                <span>{new Date(order.createdAt).toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-500">用户ID</span>
                <span>{order.user.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">用户名</span>
                <span>{order.user.username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">邮箱</span>
                <span>{order.user.email}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>课程信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-500">课程ID</span>
                <span>{order.course.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">课程名称</span>
                <span>{order.course.title}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">课程价格</span>
                <span>¥{order.course.price}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {order.payment && (
          <Card>
            <CardHeader>
              <CardTitle>支付信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">支付单号</span>
                  <span>{order.payment.paymentNo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">支付状态</span>
                  {getStatusBadge(order.payment.status)}
                </div>
                {order.payment.paidAt && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">支付时间</span>
                    <span>{new Date(order.payment.paidAt).toLocaleString()}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {order.refund && (
          <Card>
            <CardHeader>
              <CardTitle>退款信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">退款单号</span>
                  <span>{order.refund.refundNo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">退款状态</span>
                  {getStatusBadge(order.refund.status)}
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">退款金额</span>
                  <span>¥{order.refund.amount}</span>
                </div>
                {order.refund.reason && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">退款原因</span>
                    <span>{order.refund.reason}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-500">申请时间</span>
                  <span>{new Date(order.refund.createdAt).toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
} 