import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// 课程ID类型
type CourseId = 'enterprise-ai-basic' | '1' | '2' | 'deepseek-guide';

// 课程ID到目录名的映射
const COURSE_DIR_MAP: Record<CourseId, string> = {
  'enterprise-ai-basic': 'course_1_enterprise_ai_basic',
  '1': 'course_2_ai_assistant',
  '2': 'course_3_langchain',
  'deepseek-guide': 'course_deepseek_productivity_guide'
};

export async function GET(
  request: Request,
  { params }: { params: { courseId: string; lessonId: string } }
) {
  try {
    // 获取课程目录名
    const courseDirName = COURSE_DIR_MAP[params.courseId as CourseId];
    if (!courseDirName) {
      return NextResponse.json(
        { error: '课程不存在' },
        { status: 404 }
      );
    }

    // 根据课程ID选择不同的文件命名格式
    let fileName;
    if (params.courseId === 'deepseek-guide') {
      fileName = `chapter${params.lessonId}.md`;
    } else {
      fileName = `lesson_${params.lessonId}_script.md`;
    }

    // 从文件系统读取课程内容
    const filePath = path.join(process.cwd(), '..', 'article', courseDirName, fileName);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch {
      return NextResponse.json(
        { error: '课程内容不存在' },
        { status: 404 }
      );
    }
    
    const content = await fs.readFile(filePath, 'utf-8');
    
    if (!content) {
      return NextResponse.json(
        { error: '课程内容为空' },
        { status: 404 }
      );
    }
    
    // 直接返回原始的Markdown内容
    return NextResponse.json({
      title: `第${params.lessonId}章`,
      content: content,
    });
  } catch (error) {
    console.error('获取课程内容失败:', error);
    return NextResponse.json(
      { error: '获取课程内容失败' },
      { status: 500 }
    );
  }
} 