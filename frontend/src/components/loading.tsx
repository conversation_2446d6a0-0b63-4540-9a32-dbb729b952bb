import { Loader2 } from 'lucide-react'

interface LoadingProps {
  size?: number
  className?: string
}

export function Loading({ size = 24, className }: LoadingProps) {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <Loader2 className={`h-${size} w-${size} animate-spin ${className || ''}`} />
    </div>
  )
}

export function LoadingPage() {
  return (
    <div className="flex h-screen w-full items-center justify-center">
      <Loading size={32} />
    </div>
  )
}

export function LoadingOverlay() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <Loading size={32} />
    </div>
  )
} 