import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Switch,
  InputNumber,
} from 'antd';
import { regenerateSection, RegenerateSectionParams } from '@/lib/api/course';
import { AxiosError } from 'axios';

const { TextArea } = Input;
const { Option } = Select;

interface AiContentRegeneratorModalProps {
  visible: boolean;
  onClose: () => void;
  onGenerated: (content: string) => void;
  selectedContent: string;
  courseTitle: string;
  currentLesson: any;
}

export const AiContentRegeneratorModal = ({
  visible,
  onClose,
  onGenerated,
  selectedContent,
  courseTitle,
  currentLesson,
}: AiContentRegeneratorModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提取章节标题
  useEffect(() => {
    if (selectedContent) {
      const lines = selectedContent.split('\n');
      const titleLine = lines.find(line => line.trim().startsWith('## '));
      if (titleLine) {
        const title = titleLine.replace('## ', '').trim();
        form.setFieldValue('sectionTitle', title);
      }
    }
  }, [selectedContent, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const params: RegenerateSectionParams = {
        sectionTitle: values.sectionTitle,
        outline: selectedContent,
        selectedContent,
        difficulty: values.difficulty,
        interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
        includeExercises: values.includeExercises,
      };

      const response = await regenerateSection(params);
      if (response.content) {
        onGenerated(response.content);
        message.success('Content regenerated successfully!');
        onClose();
      }
    } catch (error) {
      console.error('Generation failed:', error);
      const errorMessage = error instanceof AxiosError 
        ? error.response?.data?.message || error.message
        : 'Unknown error';
      message.error('Generation failed: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="AI Regenerate Content"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="title"
          label="对话与主题"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input 
            disabled 
            value={`${courseTitle} - ${currentLesson.title}`} 
            placeholder="当前对话和主题的标题"
          />
        </Form.Item>

        <Form.Item
          name="rawMaterial"
          label="主题描述"
          rules={[{ required: true, message: '请输入主题描述' }]}
          extra="这里的内容将作为生成主题内容的基础，您可以根据需要修改"
        >
          <TextArea
            rows={6}
            placeholder="请输入主题描述，可以是大纲、关键点或完整文档"
          />
        </Form.Item>

        <Form.Item
          name="targetAudience"
          label="目标受众"
          rules={[{ required: true, message: '请输入目标受众' }]}
        >
          <Input placeholder="例如：情绪困扰者、关系困扰者等" />
        </Form.Item>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="difficulty"
            label="疗愈类型"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="beginner">情绪疏导</Option>
              <Option value="intermediate">关系咨询</Option>
              <Option value="advanced">个人成长</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tone"
            label="语气风格"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="casual">温暖关怀</Option>
              <Option value="formal">专业引导</Option>
            </Select>
          </Form.Item>
        </Space>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="sectionCount"
            label="主题数量"
            style={{ width: '200px' }}
          >
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="interactionCount"
            label="对话轮数"
            style={{ width: '200px' }}
          >
            <InputNumber min={10} max={50} style={{ width: '100%' }} />
          </Form.Item>
        </Space>

        <Form.Item
          name="includeExercises"
          label="包含练习"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              开始生成
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 