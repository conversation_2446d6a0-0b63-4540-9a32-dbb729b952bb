import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Switch,
  InputNumber,
} from 'antd';
import { generateCourse, CourseGenerationParams } from '@/lib/api/course';
import { AxiosError } from 'axios';
import { HEALING_TYPES } from '@/config/healing-types';

const { TextArea } = Input;
const { Option } = Select;

interface CurrentLesson {
  title: string;
  description: string;
  duration: string;
}

interface AiCourseGeneratorModalProps {
  visible: boolean;
  onClose: () => void;
  onGenerated: (content: string) => void;
  courseTitle: string;
  currentLesson: CurrentLesson;
}

export const AiCourseGeneratorModal = ({
  visible,
  onClose,
  onGenerated,
  courseTitle,
  currentLesson,
}: AiCourseGeneratorModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        title: `${courseTitle} - ${currentLesson.title}`,
        rawMaterial: currentLesson.description,
        targetAudience: '情绪困扰者',
        difficulty: 'beginner',
        tone: 'casual',
        includeExercises: true,
        sectionCount: 3,
        interactionCount: 20,
      });
    }
  }, [visible, form, courseTitle, currentLesson]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const params: CourseGenerationParams = {
        title: values.title,
        rawMaterial: values.rawMaterial,
        difficulty: values.difficulty,
        targetAudience: values.targetAudience,
        tone: values.tone,
        includeExercises: values.includeExercises,
        sectionCount: values.sectionCount ? Number(values.sectionCount) : undefined,
        interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
      };

      const response = await generateCourse(params);
      if (response.content) {
        onGenerated(response.content);
        message.success('内容生成成功！');
        onClose();
      }
    } catch (error) {
      console.error('生成失败:', error);
      const errorMessage = error instanceof AxiosError 
        ? error.response?.data?.message || error.message
        : '未知错误';
      message.error('生成失败：' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`AI 生成主题内容 - ${currentLesson.title}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="title"
          label="对话与主题"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input 
            disabled 
            value={`${courseTitle} - ${currentLesson.title}`} 
            placeholder="当前对话和主题的标题"
          />
        </Form.Item>

        <Form.Item
          name="rawMaterial"
          label="主题描述"
          rules={[{ required: true, message: '请输入主题描述' }]}
          extra="这里的内容将作为生成主题内容的基础，您可以根据需要修改"
        >
          <TextArea
            rows={6}
            placeholder="请输入主题描述，可以是大纲、关键点或完整文档"
          />
        </Form.Item>

        <Form.Item
          name="targetAudience"
          label="目标受众"
          rules={[{ required: true, message: '请输入目标受众' }]}
        >
          <Input placeholder="例如：情绪困扰者、关系困扰者等" />
        </Form.Item>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="difficulty"
            label="疗愈类型"
            style={{ width: '200px' }}
          >
            <Select>
              {HEALING_TYPES.map((type) => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tone"
            label="语气风格"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="casual">温暖关怀</Option>
              <Option value="formal">专业引导</Option>
            </Select>
          </Form.Item>
        </Space>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="sectionCount"
            label="主题数量"
            style={{ width: '200px' }}
          >
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="interactionCount"
            label="对话轮数"
            style={{ width: '200px' }}
          >
            <InputNumber min={10} max={50} style={{ width: '100%' }} />
          </Form.Item>
        </Space>

        <Form.Item
          name="includeExercises"
          label="包含练习"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              开始生成
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 