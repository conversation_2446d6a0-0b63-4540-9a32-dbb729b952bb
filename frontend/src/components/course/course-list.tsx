'use client';

import { useEffect, useState } from 'react';
import { CourseCard } from './course-card';
import { Course, CourseService } from '@/lib/services/course.service';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export function CourseList() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        console.log('[CourseList] 开始获取课程列表');
        setLoading(true);
        setError(null);

        const data = await CourseService.getCourses();
        console.log('[CourseList] 成功获取课程列表', {
          coursesCount: data.length,
          courses: data,
          timestamp: new Date().toISOString(),
        });

        setCourses(data);
      } catch (err: any) {
        console.error('[CourseList] 获取课程列表失败', {
          error: err,
          message: err.message,
          response: err.response?.data,
          timestamp: new Date().toISOString(),
        });
        
        setError(err.response?.data?.message || '加载课程失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 min-h-[200px] flex items-center justify-center">
        {error}
      </div>
    );
  }

  if (courses.length === 0) {
    return (
      <div className="text-center text-muted-foreground min-h-[200px] flex items-center justify-center">
        暂无课程
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {courses.map((course) => (
        <CourseCard
          key={course.id}
          id={course.id}
          title={course.title}
          description={course.description}
          duration={course.duration}
          level={course.level}
          lessonsCount={course.lessonsCount}
          price={course.price}
          isPurchased={course.isPurchased}
        />
      ))}
    </div>
  );
} 