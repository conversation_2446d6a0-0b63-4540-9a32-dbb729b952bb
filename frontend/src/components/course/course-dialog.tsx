'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQuery } from '@tanstack/react-query';
import { HEALING_TYPES } from '@/config/healing-types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Course } from '@/lib/services/course.service';
import { CategoryService } from '@/lib/services/category.service';

// 表单验证 Schema
const courseFormSchema = z.object({
  title: z.string().min(1, '请输入对话标题'),
  description: z.string().min(1, '请输入对话描述'),
  code: z.string().min(1, '请输入对话代码').regex(/^[a-z0-9_]+$/, '对话代码只能包含小写字母、数字和下划线'),
  level: z.enum(HEALING_TYPES.map(type => type.value) as [string, ...string[]], {
    required_error: '请选择疗愈类型',
  }),
  duration: z.string().min(1, '请输入对话时长'),
  price: z.string().min(1, '请输入对话价格'),
  catalogueId: z.string().optional(),
});

type CourseFormValues = z.infer<typeof courseFormSchema>;

type CourseSubmitData = Omit<CourseFormValues, 'price'> & {
  price: number;
};

interface CourseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CourseSubmitData) => Promise<void>;
  initialData?: Partial<Course>;
  mode?: 'create' | 'edit';
}

export function CourseDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode = 'create',
}: CourseDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取分类列表
  const { data: categories = [] } = useQuery({
    queryKey: ['categories'],
    queryFn: () => CategoryService.getCategories(),
  });

  const form = useForm<CourseFormValues>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      code: initialData?.code || '',

      level: initialData?.level || 'Emotional Guidance',
      duration: initialData?.duration || '',
      price: initialData?.price?.toString() || '0',
      catalogueId: initialData?.catalogue?.id || 'none',
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        title: initialData.title || '',
        description: initialData.description || '',
        code: initialData.code || '',
        level: initialData.level || 'Emotional Guidance',
        duration: initialData.duration || '',
        price: initialData.price?.toString() || '0',
        catalogueId: initialData.catalogue?.id || 'none',
      });
    }
  }, [form, initialData]);

  const handleSubmit = async (values: CourseFormValues) => {
    try {
      setIsSubmitting(true);
      // 处理categoryId，如果是"none"则设为undefined，并转换price为数字
      const submitData = {
        ...values,
        price: parseFloat(values.price),
        catalogueId: values.catalogueId === 'none' ? undefined : values.catalogueId,
      };
      await onSubmit(submitData);
      form.reset(); // 重置表单
      onOpenChange(false); // 关闭对话框
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{mode === 'create' ? '创建疗愈对话' : '编辑疗愈对话'}</DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? '创建一个新的疗愈对话。创建后默认为未发布状态。'
              : '编辑疗愈对话信息。'
            }
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>对话主题</FormLabel>
                  <FormControl>
                    <Input placeholder="输入对话主题" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>对话代码</FormLabel>
                  <FormControl>
                    <Input placeholder="输入对话代码，如：dialogue_1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>对话描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入对话描述"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>疗愈类型</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择疗愈类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {HEALING_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="catalogueId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分类</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择分类（可选）" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">无分类</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>对话时长</FormLabel>
                    <FormControl>
                      <Input placeholder="如：30分钟" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>价格 (元)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        min="0"
                        step="0.01"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? '保存中...' : mode === 'create' ? '创建' : '保存'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 