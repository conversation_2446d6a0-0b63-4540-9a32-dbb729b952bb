'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, BookOpen } from 'lucide-react';
import Link from 'next/link';

interface CourseCardProps {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: 'Emotional Guidance' | 'Relationship Support' | 'Personal Growth';
  lessonsCount: number;
  price: number;
  isPurchased?: boolean;
  basePath?: string; // 添加可选的基础路径属性
}

export function CourseCard({
  id,
  title,
  description,
  duration,
  level,
  lessonsCount,
  price,
  isPurchased,
  basePath = '/all-courses', // Default value is /all-courses
}: CourseCardProps) {
  return (
    <Card className="group hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="secondary">{level}</Badge>
          {isPurchased && (
            <Badge variant="default" className="bg-softPurple-500">
              Purchased
            </Badge>
          )}
        </div>
        <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
          {title}
        </h3>
        <p className="text-muted-foreground line-clamp-2">{description}</p>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>{duration}</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="h-4 w-4" />
            <span>{lessonsCount} lessons</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full">
          <Link href={`${basePath}/${id}`}>
            {isPurchased ? 'Continue Learning' : price === 0 ? 'Free Trial' : 'Start Learning'}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
} 