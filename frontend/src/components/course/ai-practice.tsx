'use client'

import React, { useState } from 'react'
import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Send, Image as ImageIcon, Loader2 } from 'lucide-react'
import { api } from '@/lib/api'
import { AiService } from '@/lib/services/ai.service'

interface AIPracticeProps {
  type: 'chat' | 'image'
  mode: string
  character?: string
  temperature?: number
  max_tokens?: number
  system_prompt?: string
  model?: string
  size?: string
  style?: string
  quality?: string
  initial_prompt?: string
  onComplete?: () => void
  placeholder?: string
}

export function AIPractice({
  type,
  mode,
  character,
  temperature,
  max_tokens,
  system_prompt,
  model,
  size,
  style,
  quality,
  initial_prompt,
  onComplete,
  placeholder = 'Enter your answer...',
}: AIPracticeProps) {
  const [messages, setMessages] = useState<Array<{ role: 'system' | 'user' | 'assistant'; content: string }>>(() => {
    const initialMessages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];
    if (system_prompt) {
      initialMessages.push({ role: 'system', content: system_prompt });
    }
    if (initial_prompt) {
      initialMessages.push({ role: 'assistant', content: `Exercise: ${initial_prompt}` });
    }
    return initialMessages;
  });
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)

  const handleSubmit = async () => {
    if (!input.trim()) return

    setIsLoading(true)
    try {
      if (type === 'chat') {
        const userMessage = initial_prompt 
          ? `Exercise: ${initial_prompt}\n\nMy answer: ${input}`
          : input;
        
        const newMessage = { role: 'user', content: userMessage }
        setMessages([...messages, newMessage])

        const response = await AiService.chat({
          messages: [...messages, newMessage],
          temperature,
          max_tokens,
          character,
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to get AI response');
        }

        setMessages([...messages, newMessage, { role: 'assistant', content: response.data }])
      } else if (type === 'image') {
        const response = await AiService.generateImages({
          prompt: input,
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to generate image');
        }

        setGeneratedImage(response.data[0])
      }

      setInput('')
    } catch (error) {
      console.error('Error in AI practice:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="p-4">
      <div className="flex flex-col gap-4">
        {type === 'chat' && (
          <div className="flex flex-col gap-2">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`p-2 rounded ${
                  message.role === 'user' ? 'bg-primary/10 ml-auto' : 'bg-secondary/10'
                }`}
              >
                <div className="font-bold">
                  {message.role === 'user' ? 'You' : character || 'AI Assistant'}
                </div>
                <div className="whitespace-pre-wrap">{message.content}</div>
              </div>
            ))}
          </div>
        )}

        {type === 'image' && generatedImage && (
          <div className="flex justify-center">
            <img src={generatedImage} alt="Generated" className="max-w-full h-auto" />
          </div>
        )}

        <div className="flex gap-2">
          {type === 'chat' ? (
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={placeholder}
              className="flex-1"
            />
          ) : (
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Describe the image you want to generate..."
              className="flex-1"
            />
          )}
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Processing...' : type === 'chat' ? 'Submit Answer' : 'Generate'}
          </Button>
        </div>

        {onComplete && (
          <Button onClick={onComplete} variant="outline" className="mt-4">
            Complete Exercise
          </Button>
        )}
      </div>
    </Card>
  )
} 