import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Switch,
  InputNumber,
} from 'antd';
import { generateCourse, regenerateSection, CourseGenerationParams, RegenerateSectionParams } from '@/lib/api/course';
import { AxiosError } from 'axios';
import { HEALING_TYPES } from '@/config/healing-types';

const { TextArea } = Input;
const { Option } = Select;

interface AiCourseGeneratorProps {
  visible: boolean;
  onClose: () => void;
  onGenerated: (content: string) => void;
  currentContent?: string;
  sectionTitle?: string;
  selectedContent?: string;
}

export const AiCourseGenerator = ({
  visible,
  onClose,
  onGenerated,
  currentContent,
  sectionTitle,
  selectedContent,
}: AiCourseGeneratorProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 根据模式构建不同的参数
      if (sectionTitle) {
        // 重新生成小节模式
        const params: RegenerateSectionParams = {
          sectionTitle,
          outline: currentContent || '',
          difficulty: values.difficulty,
          interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
          includeExercises: values.includeExercises,
          selectedContent,
        };
        const response = await regenerateSection(params);
        if (response.content) {
          onGenerated(response.content);
          message.success('Content generated successfully!');
          onClose();
        }
      } else {
        // 生成新课程模式
        const params: CourseGenerationParams = {
          title: values.title,
          rawMaterial: selectedContent || values.rawMaterial,
          difficulty: values.difficulty,
          targetAudience: values.targetAudience,
          tone: values.tone,
          includeExercises: values.includeExercises,
          sectionCount: values.sectionCount ? Number(values.sectionCount) : undefined,
          interactionCount: values.interactionCount ? Number(values.interactionCount) : undefined,
        };
        const response = await generateCourse(params);
        if (response.content) {
          onGenerated(response.content);
          message.success('Content generated successfully!');
          onClose();
        }
      }
    } catch (error) {
      console.error('Generation failed:', error);
      const errorMessage = error instanceof AxiosError 
        ? error.response?.data?.message || error.message
        : 'Unknown error';
      message.error('Generation failed: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={sectionTitle ? (selectedContent ? 'AI Regenerate Selected Content' : 'AI Regenerate Topic') : 'AI Generate Healing Dialogue'}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          difficulty: 'beginner',
          tone: 'casual',
          includeExercises: true,
          sectionCount: 3,
          interactionCount: 20,
          rawMaterial: selectedContent || '',
        }}
      >
        <Form.Item
          name="title"
          label="Dialogue Topic"
          rules={[{ required: true, message: 'Please enter dialogue topic' }]}
        >
          <Input placeholder="Please enter dialogue topic" />
        </Form.Item>

        <Form.Item
          name="rawMaterial"
          label={selectedContent ? "Selected Content" : "Raw Material"}
          rules={[{ required: true, message: 'Please enter raw material' }]}
        >
          <TextArea
            rows={6}
            placeholder={selectedContent ? "This is your selected content, will be regenerated based on this" : "Please enter dialogue raw material, can be outline, key points or complete document"}
            disabled={!!selectedContent}
          />
        </Form.Item>

        <Form.Item
          name="targetAudience"
          label="Target Audience"
          rules={[{ required: true, message: 'Please enter target audience' }]}
        >
          <Input placeholder="For example: emotionally distressed, relationship troubled, etc." />
        </Form.Item>

        <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item
            name="difficulty"
            label="Healing Type"
            style={{ width: '200px' }}
          >
            <Select>
              {HEALING_TYPES.map((type) => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tone"
            label="Tone Style"
            style={{ width: '200px' }}
          >
            <Select>
              <Option value="casual">Warm Care</Option>
              <Option value="formal">Professional Guidance</Option>
            </Select>
          </Form.Item>
        </Space>

        {!sectionTitle && (
          <Space size="large" style={{ width: '100%', justifyContent: 'space-between' }}>
            <Form.Item
              name="sectionCount"
              label="Topic Count"
              style={{ width: '200px' }}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="interactionCount"
              label="Dialogue Rounds"
              style={{ width: '200px' }}
            >
              <InputNumber min={10} max={50} style={{ width: '100%' }} />
            </Form.Item>
          </Space>
        )}

        <Form.Item
          name="includeExercises"
          label="Include Exercises"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>Cancel</Button>
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              Start Generation
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 