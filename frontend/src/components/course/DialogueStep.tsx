import React from 'react';

interface DialogueStepProps {
  speaker: string;
  content: string;
  isWaiting?: boolean;
}

const DialogueStep: React.FC<DialogueStepProps> = ({ speaker, content, isWaiting }) => {
  return (
    <div className="flex flex-col space-y-2">
      <div className="font-bold text-lg">
        {speaker === '系统' ? 'System' : speaker === 'AI助手' ? 'AI Assistant' : 'You'}
      </div>
      <div className="whitespace-pre-wrap">{content}</div>
      {isWaiting && (
        <div className="text-sm text-gray-500 mt-2">
          Please continue sharing your thoughts...
        </div>
      )}
    </div>
  );
};

export default DialogueStep; 