'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronRight, FileText, RefreshCw, PenLine, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { AIPractice } from './ai-practice'
import { ImagePractice } from './image-practice'
import { CourseStep } from '@/types/course'
import { MediaStep } from './media-step'
import { Markdown } from '@/components/markdown'

interface InteractiveLessonProps {
  title: string;
  steps: CourseStep[];
  onComplete?: () => void;
  hasNextLesson?: boolean;
}

export function InteractiveLesson({ title, steps, onComplete, hasNextLesson }: InteractiveLessonProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [userInput, setUserInput] = useState('');
  const [isReviewMode, setIsReviewMode] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [showContinueButton, setShowContinueButton] = useState(true);

  // Ensure steps is not empty
  useEffect(() => {
    console.log('Course steps:', steps);
    if (!steps || steps.length === 0) {
      console.error('No course steps found');
    }
  }, [steps]);

  // Get current step with null check
  const currentStep = steps?.[currentStepIndex] || {
    type: 'dialogue',
    speaker: 'System',
    content: 'Loading course content...'
  };

  // Auto scroll to latest content, use setTimeout to ensure scroll after rendering
  useEffect(() => {
    if (contentRef.current) {
      // Use setTimeout to ensure scroll after rendering
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [currentStepIndex]);

  // Listen for step changes
  useEffect(() => {
    if (currentStep) {
      console.log('Current step:', currentStep);
      if (currentStep.type === 'image_practice') {
        console.log('Image practice config:', currentStep.image_practice);
      }
    }
  }, [currentStepIndex, currentStep]);

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else if (onComplete) {
      onComplete();
    }
  };

  const handleInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim()) {
      handleNext();
    }
  };

  // Check if in review mode
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setIsReviewMode(urlParams.get('mode') === 'review');
  }, []);

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)] bg-gray-50">
      {/* Conversation History Area */}
      <div
        ref={contentRef}
        className="flex-1 overflow-y-auto px-4 py-6 pb-24"
      >
        <div className="max-w-3xl mx-auto space-y-6">
          {/* In review mode, show all steps; otherwise only show up to current step */}
          {(isReviewMode ? steps : steps.slice(0, currentStepIndex + 1)).map((step, index) => {
            const isCurrentStep = index === currentStepIndex;

            switch (step.type) {
              case 'dialogue':
                const isSystem = step.speaker === 'System';
                const isInstructor = step.speaker === 'Instructor';
                const isUser = !isSystem && !isInstructor;

                return (
                  <div
                    key={index}
                    className={`flex items-start gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}
                  >
                    {/* Avatar */}
                    <Avatar className="w-10 h-10 shrink-0">
                      <AvatarImage src={
                        isSystem ? "/images/system-avatar.png" :
                        isInstructor ? "/images/instructor-avatar.png" :
                        "/images/user-avatar.png"
                      } />
                      <AvatarFallback>
                        {isSystem ? 'S' : isInstructor ? 'T' : 'U'}
                      </AvatarFallback>
                    </Avatar>

                    {/* Conversation Content */}
                    <div className={`flex-1 space-y-2 ${isUser ? 'items-end' : 'items-start'}`}>
                      <div className="font-bold text-base text-gray-700">
                        {step.speaker}
                      </div>
                      <div className={`rounded-lg shadow-sm p-4 prose prose-base max-w-none ${
                        isSystem ? 'bg-softPurple-50 text-gray-800' :
                        isInstructor ? 'bg-softPurple-100 text-gray-800' :
                        'bg-white text-gray-800'
                      }`}>
                        <Markdown content={step.content || ''} />
                      </div>
                    </div>
                  </div>
                );

              case 'media':
                if (step.media) {
                  // Use more stable key based on media URL and type
                  const mediaKey = `${step.media.type}-${step.media.url}-${index}`;
                  return (
                    <MediaStep
                      key={mediaKey}
                      media={step.media}
                      className="my-4"
                    />
                  );
                }
                return null;

              case 'wait':
                // Don't show wait button in review mode
                if (isReviewMode) return null;
                return (
                  <div key={index} className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={handleNext}
                      disabled={!isCurrentStep}
                    >
                      {step.content || 'Continue'}
                    </Button>
                  </div>
                );

              case 'ai_practice':
                if (step.practice) {
                  return (
                    <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                      {step.content && (
                        <div className="mb-4 prose prose-sm max-w-none">
                          <Markdown content={step.content} />
                        </div>
                      )}
                      {/* Don't show AI exercise component in review mode */}
                      {!isReviewMode && isCurrentStep && (
                        <AIPractice
                          type={step.practice.type === 'image' ? 'image' : 'chat'}
                          mode={step.practice.mode}
                          character="assistant"
                          temperature={step.practice.temperature}
                          max_tokens={step.practice.max_tokens}
                          initial_prompt={step.content || ''}
                          model={step.practice.model}
                          onComplete={handleNext}
                          placeholder="Enter your answer..."
                        />
                      )}
                    </div>
                  );
                }
                return null;

              case 'image_practice':
                if (step.image_practice) {
                  console.log('Rendering image practice step:', step);
                  return (
                    <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                      {step.content && (
                        <div className="mb-4 whitespace-pre-wrap">{step.content}</div>
                      )}
                      {/* Don't show image practice component in review mode */}
                      {!isReviewMode && isCurrentStep && (
                        <ImagePractice
                          mode={step.image_practice.mode}
                          size={step.image_practice.size}
                          onComplete={handleNext}
                          onStatusChange={(isCompleted) => {
                            // Control continue button display based on image exercise completion status
                            setShowContinueButton(isCompleted)
                          }}
                        />
                      )}
                    </div>
                  );
                }
                return null;

              default:
                return null;
            }
          })}
        </div>
      </div>

      {/* Bottom button area - don't show in review mode */}
      {!isReviewMode && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t">
          <div className="max-w-3xl mx-auto">
            {currentStep.type === 'image_practice' ? (
              // For image exercise steps, only show continue button after completion
              showContinueButton && (
                <Button
                  onClick={handleNext}
                  className="w-full bg-softPurple-500 hover:bg-softPurple-600 text-white"
                  size="lg"
                >
                  Continue <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              )
            ) : (
              // Other steps show buttons normally
              currentStepIndex < steps.length - 1 ? (
                <Button
                  onClick={handleNext}
                  className="w-full bg-softPurple-500 hover:bg-softPurple-600 text-white"
                  size="lg"
                >
                  Continue <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              ) : (
                <Button
                  onClick={onComplete}
                  className="w-full bg-softPurple-500 hover:bg-softPurple-600 text-white"
                  size="lg"
                >
                  {hasNextLesson ? 'Next Lesson' : 'Complete Course'} <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
}