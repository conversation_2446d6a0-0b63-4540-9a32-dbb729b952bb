import React, { useState, useRef } from 'react';
import { Button, Space, Tooltip } from 'antd';
import { RobotOutlined, RedoOutlined, SaveOutlined } from '@ant-design/icons';
import { AiCourseGenerator } from './AiCourseGenerator';
import { Textarea } from '@/components/ui/textarea';

interface CourseEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSave?: () => void;
}

export const CourseEditor: React.FC<CourseEditorProps> = ({
  value,
  onChange,
  onSave,
}) => {
  const [aiModalVisible, setAiModalVisible] = useState(false);
  const [regenerateModalVisible, setRegenerateModalVisible] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string>();
  const [selectedContent, setSelectedContent] = useState<string>();
  const editorRef = useRef<HTMLTextAreaElement>(null);

  const handleAiGenerate = (content: string) => {
    onChange(content);
    if (onSave) {
      onSave();
    }
  };

  const handleRegenerateSection = (content: string) => {
    if (selectedContent && value) {
      // 替换选中的内容
      const newContent = value.replace(selectedContent, content);
      onChange(newContent);
    } else if (selectedSection && value) {
      // 如果没有选中内容但有选中的小节标题，替换整个小节
      const lines = value.split('\n');
      let inSection = false;
      let newContent = '';
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line.startsWith('## ') && line.includes(selectedSection)) {
          inSection = true;
          newContent += content + '\n';
          continue;
        }
        
        if (inSection && line.startsWith('## ')) {
          inSection = false;
        }
        
        if (!inSection) {
          newContent += line + '\n';
        }
      }
      
      onChange(newContent.trim());
    }

    if (onSave) {
      onSave();
    }
  };

  const handleSectionSelect = (section: string) => {
    setSelectedSection(section);
    setSelectedContent(undefined);
    setRegenerateModalVisible(true);
  };

  const handleTextSelect = () => {
    if (editorRef.current) {
      const selectedText = editorRef.current.value.substring(
        editorRef.current.selectionStart,
        editorRef.current.selectionEnd
      );
      
      if (selectedText) {
        // 从选中的文本中提取小节标题
        const lines = selectedText.split('\n');
        const sectionTitleLine = lines.find(line => line.startsWith('## '));
        const sectionTitle = sectionTitleLine?.replace('## ', '').trim();
        
        setSelectedContent(selectedText);
        setSelectedSection(sectionTitle);
        setRegenerateModalVisible(true);
      }
    }
  };

  return (
    <div className="course-editor">
      <Textarea
        ref={editorRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            if (onSave) onSave();
          }
        }}
        placeholder="在此输入课程内容..."
        className="min-h-[600px] font-mono"
      />

      <AiCourseGenerator
        visible={aiModalVisible}
        onClose={() => {
          setAiModalVisible(false);
          setSelectedContent(undefined);
          setSelectedSection(undefined);
        }}
        onGenerated={handleAiGenerate}
      />

      <AiCourseGenerator
        visible={regenerateModalVisible}
        onClose={() => {
          setRegenerateModalVisible(false);
          setSelectedContent(undefined);
          setSelectedSection(undefined);
        }}
        onGenerated={handleRegenerateSection}
        currentContent={value}
        sectionTitle={selectedSection}
        selectedContent={selectedContent}
      />

      <style jsx>{`
        .course-editor {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      `}</style>
    </div>
  );
}; 