import React, { memo, useRef, useEffect } from 'react';
import Image from 'next/image';
import { CourseMedia } from '@/types/course';

interface MediaStepProps {
  media: CourseMedia;
  className?: string;
}

function MediaStepComponent({ media, className }: MediaStepProps) {
  // 使用 useRef 保存媒体元素的引用
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 添加状态来控制是否显示加载指示器
  const [isLoading, setIsLoading] = React.useState(false);

  // 当组件重新渲染时，保持视频/音频的状态
  useEffect(() => {
    // 如果是视频元素
    if (media.type === 'video' && videoRef.current) {
      // 设置加载状态
      setIsLoading(true);

      // 保存当前的播放状态和时间
      const wasPlaying = !videoRef.current.paused;
      const currentTime = videoRef.current.currentTime;
      const volume = videoRef.current.volume;

      // 当视频加载完成后，恢复状态
      const handleLoadedData = () => {
        if (videoRef.current) {
          videoRef.current.currentTime = currentTime;
          videoRef.current.volume = volume;
          if (wasPlaying) {
            videoRef.current.play().catch(() => {});
          }
          // 加载完成，隐藏加载指示器
          setIsLoading(false);
        }
      };

      videoRef.current.addEventListener('loadeddata', handleLoadedData);

      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener('loadeddata', handleLoadedData);
        }
      };
    }

    // 如果是音频元素
    if (media.type === 'audio' && audioRef.current) {
      // 设置加载状态
      setIsLoading(true);

      // 保存当前的播放状态和时间
      const wasPlaying = !audioRef.current.paused;
      const currentTime = audioRef.current.currentTime;
      const volume = audioRef.current.volume;

      // 当音频加载完成后，恢复状态
      const handleLoadedData = () => {
        if (audioRef.current) {
          audioRef.current.currentTime = currentTime;
          audioRef.current.volume = volume;
          if (wasPlaying) {
            audioRef.current.play().catch(() => {});
          }
          // 加载完成，隐藏加载指示器
          setIsLoading(false);
        }
      };

      audioRef.current.addEventListener('loadeddata', handleLoadedData);

      return () => {
        if (audioRef.current) {
          audioRef.current.removeEventListener('loadeddata', handleLoadedData);
        }
      };
    }
  }, [media]);

  // 为图片添加加载状态
  const [imageLoading, setImageLoading] = React.useState(true);

  // 处理图片加载完成
  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // 当图片URL变化时重置加载状态
  useEffect(() => {
    if (media.type === 'image') {
      setImageLoading(true);
    }
  }, [media.url, media.type]);

  const renderMedia = () => {
    switch (media.type) {
      case 'image':
        return (
          <div className="relative w-full max-w-3xl mx-auto">
            {/* 图片加载指示器 */}
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-20 rounded-lg z-10">
                <div className="w-10 h-10 border-4 border-softPurple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <Image
              key={`image-${media.url}`}
              src={media.url}
              alt={media.title || ''}
              width={media.width || 800}
              height={media.height || 600}
              className="rounded-lg"
              priority={true}
              unoptimized={true} // 避免 Next.js 的图片优化导致重新加载
              onLoad={handleImageLoad}
            />
            {(media.title || media.description) && (
              <div className="mt-2 text-center text-sm text-gray-500">
                {media.title && <div className="font-medium">{media.title}</div>}
                {media.description && <div>{media.description}</div>}
              </div>
            )}
          </div>
        );
      case 'video':
        return (
          <div className="relative w-full max-w-3xl mx-auto">
            {/* 加载指示器 */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 rounded-lg z-10">
                <div className="w-12 h-12 border-4 border-softPurple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <video
              ref={videoRef}
              key={`video-${media.url}`}
              src={media.url}
              controls
              poster={media.poster}
              preload="metadata"
              className="w-full rounded-lg bg-black"
              controlsList="nodownload"
              playsInline
            >
              <source src={media.url} type="video/mp4" />
              <source src={media.url} type="video/webm" />
              <source src={media.url} type="video/ogg" />
              您的浏览器不支持视频播放。
            </video>
            {media.title && (
              <div className="mt-2 text-center text-sm text-gray-500">
                <div className="font-medium">{media.title}</div>
              </div>
            )}
          </div>
        );
      case 'audio':
        return (
          <div className="w-full max-w-3xl mx-auto relative">
            {/* 加载指示器 */}
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-20 rounded-lg z-10">
                <div className="w-8 h-8 border-4 border-softPurple-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
            <audio
              ref={audioRef}
              key={`audio-${media.url}`}
              src={media.url}
              controls
              preload="metadata"
              className="w-full"
              controlsList="nodownload"
            >
              您的浏览器不支持音频播放。
            </audio>
            {media.title && (
              <div className="mt-2 text-center text-sm text-gray-500">
                <div className="font-medium">{media.title}</div>
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={className}>
      {renderMedia()}
    </div>
  );
}

// 使用 memo 包装组件，避免不必要的重新渲染
export const MediaStep = memo(MediaStepComponent, (prevProps, nextProps) => {
  // 如果 media 对象的 url 和 type 相同，则认为组件不需要重新渲染
  return prevProps.media.url === nextProps.media.url &&
         prevProps.media.type === nextProps.media.type;
});
