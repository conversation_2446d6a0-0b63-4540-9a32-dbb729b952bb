import React, { useState, useCallback } from 'react';
import DialogueStep from './DialogueStep';
import <PERSON><PERSON><PERSON>tic<PERSON> from './AIPractice';

const InteractiveLesson: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isWaitingForInput, setIsWaitingForInput] = useState(false);
  const [steps, setSteps] = useState([]);

  const handleNext = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
      setIsWaitingForInput(false);
    }
  }, [currentStepIndex, steps.length]);

  const handleClick = useCallback(() => {
    const currentStep = steps[currentStepIndex];
    if (currentStep.type === 'dialogue' && currentStep.waitForInput && !isWaitingForInput) {
      setIsWaitingForInput(true);
    } else {
      handleNext();
    }
  }, [currentStepIndex, handleNext, steps, isWaitingForInput]);

  return (
    <div className="flex flex-col space-y-4 p-4">
      {steps[currentStepIndex] && (
        <div 
          className="cursor-pointer" 
          onClick={handleClick}
        >
          {steps[currentStepIndex].type === 'dialogue' && (
            <DialogueStep 
              speaker={steps[currentStepIndex].speaker} 
              content={steps[currentStepIndex].content}
              isWaiting={steps[currentStepIndex].waitForInput && !isWaitingForInput}
            />
          )}
          {steps[currentStepIndex].type === 'ai_practice' && (
            <AIPractice practice={steps[currentStepIndex].practice} onComplete={handleNext} />
          )}
        </div>
      )}
    </div>
  );
};

export default InteractiveLesson; 