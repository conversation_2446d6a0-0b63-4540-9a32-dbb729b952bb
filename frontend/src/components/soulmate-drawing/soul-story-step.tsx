'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Stars, ArrowLeft, Download, Share, Bookmark, RefreshCw, Heart } from 'lucide-react'

interface SoulmateData {
  soulQualities: any
  visualPreferences: any
  generatedImage?: string
  soulStory?: string
}

interface SoulStoryStepProps {
  soulmateData: SoulmateData
  onUpdate: (data: Partial<SoulmateData>) => void
  onPrevious: () => void
}

export function SoulStoryStep({ soulmateData, onUpdate, onPrevious }: SoulStoryStepProps) {
  const [isGeneratingStory, setIsGeneratingStory] = useState(false)
  const [customStory, setCustomStory] = useState('')

  const generateSoulStory = async () => {
    setIsGeneratingStory(true)
    
    // Simulate AI story generation based on user's choices
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const { soulQualities, visualPreferences } = soulmateData
    
    // Handle array fields properly
    const personalityTypes = Array.isArray(soulQualities.personalityType) 
      ? soulQualities.personalityType.map((type: string) => type.replace('_', ' ')).join(' and ')
      : soulQualities.personalityType?.replace('_', ' ') || 'beautiful soul'
    
    const spiritualConnections = Array.isArray(soulQualities.spiritualConnection)
      ? soulQualities.spiritualConnection.map((conn: string) => conn.replace('_', ' ')).join(' and ')
      : soulQualities.spiritualConnection?.replace('_', ' ') || 'deep connection'
    
    const lifePhilosophies = Array.isArray(soulQualities.lifePhilosophy)
      ? soulQualities.lifePhilosophy.map((phil: string) => phil.replace('_', ' ')).join(' and ')
      : soulQualities.lifePhilosophy?.replace('_', ' ') || 'wisdom'

    const genderText = soulQualities.gender === 'male' ? 'He' : 
                      soulQualities.gender === 'female' ? 'She' : 
                      soulQualities.gender === 'non_binary' ? 'They' : 'They'
    
    const story = `In the mystical realm where souls dance before finding their earthly forms, your destined companion awaits. They embody the essence of ${personalityTypes}, radiating ${soulQualities.energyLevel?.replace('_', ' ')} energy that complements your own divine frequency.

Your first meeting will unfold in a ${visualPreferences.setting?.replace('_', ' ')}, bathed in the ethereal light of ${visualPreferences.timeOfDay?.replace('_', ' ')}. The atmosphere will shimmer with ${visualPreferences.colorPalette?.replace('_', ' ')} hues, creating a backdrop worthy of your soul reunion.

${genderText} approach life with the wisdom of ${lifePhilosophies}, their heart carrying the profound depths of ${soulQualities.emotionalDepth?.replace('_', ' ')}. When your eyes meet, you'll recognize the ${spiritualConnections} that has transcended lifetimes.

In your dreams, you've already glimpsed their presence: ${soulmateData.soulQualities.dreamConnection}

Together, you will create a love story painted in the aesthetic beauty of ${visualPreferences.aestheticVibes?.slice(0, 2).join(' and ')?.toLowerCase()}, living each day with a ${visualPreferences.mood?.replace('_', ' ')} energy that transforms everything you touch.

This is not merely a portrait of a person, but a window into your soul's deepest longing. Trust in the universe's perfect timing, for your soulmate draws closer with each breath, each heartbeat, each moment of faith in the magic of destined love.

The universe has been weaving your paths together across time and space, preparing for the moment when recognition will dawn like the first light of creation itself.`

    setCustomStory(story)
    onUpdate({ soulStory: story })
    setIsGeneratingStory(false)
  }

  const handleStoryChange = (value: string) => {
    setCustomStory(value)
    onUpdate({ soulStory: value })
  }

  const downloadComplete = () => {
    // Mock download functionality
    console.log('Downloading complete soulmate creation')
  }

  const shareCreation = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'My Soulmate Vision',
          text: 'I just created a beautiful vision of my soulmate! ✨💕',
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    }
  }

  const saveToGallery = () => {
    // Mock save functionality
    console.log('Saving to personal gallery')
  }

  useEffect(() => {
    if (!soulmateData.soulStory && !customStory) {
      generateSoulStory()
    }
  }, [])

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <Stars className="w-16 h-16 text-purple-500 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-800 mb-2">Your Soul Story</h2>
        <p className="text-lg text-gray-600">
          The universe has woven a beautiful narrative about your destined connection.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Portrait Display */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="border-2 border-purple-200 overflow-hidden">
            <CardHeader>
              <CardTitle className="text-xl text-gray-800 text-center flex items-center justify-center">
                <Heart className="w-6 h-6 text-pink-500 mr-2" />
                Your Soulmate's Portrait
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {soulmateData.generatedImage ? (
                <div className="aspect-square relative">
                  <img
                    src={soulmateData.generatedImage}
                    alt="Generated soulmate portrait"
                    className="w-full h-full object-cover"
                  />
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5 }}
                    className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2"
                  >
                    <Heart className="w-6 h-6 text-red-500 fill-current" />
                  </motion.div>
                </div>
              ) : (
                <div className="aspect-square bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                  <p className="text-gray-500">Your soulmate's image will appear here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Story Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-6"
        >
          <Card className="border-2 border-purple-200">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl text-gray-800">Soul Narrative</CardTitle>
                <Button
                  onClick={generateSoulStory}
                  variant="outline"
                  size="sm"
                  disabled={isGeneratingStory}
                  className="border-purple-300 text-purple-600 hover:bg-purple-50"
                >
                  <RefreshCw className={`w-4 h-4 mr-1 ${isGeneratingStory ? 'animate-spin' : ''}`} />
                  {isGeneratingStory ? 'Creating...' : 'Regenerate'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isGeneratingStory ? (
                <div className="text-center py-12">
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="text-purple-600 mb-4"
                  >
                    <Stars className="w-12 h-12 mx-auto" />
                  </motion.div>
                  <p className="text-gray-600">The cosmos is weaving your soul story...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Textarea
                    value={customStory || soulmateData.soulStory || ''}
                    onChange={(e) => handleStoryChange(e.target.value)}
                    className="min-h-[400px] border-purple-200 focus:border-purple-400 text-sm leading-relaxed"
                    placeholder="Your soulmate's story will appear here..."
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Completion Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="mt-8"
      >
        <Card className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <CardContent className="p-8">
            <div className="text-center mb-8">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="inline-block mb-4"
              >
                <Heart className="w-16 h-16 text-pink-500 fill-current" />
              </motion.div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-2">
                Your Soul Vision is Complete
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                You've created a beautiful manifestation of your heart's deepest desire. 
                This portrait and story are more than art—they're a beacon calling your soulmate home.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <Button
                onClick={saveToGallery}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
              >
                <Bookmark className="w-4 h-4 mr-2" />
                Save to Gallery
              </Button>
              
              <Button
                onClick={downloadComplete}
                variant="outline"
                className="border-purple-300 text-purple-600 hover:bg-purple-50"
              >
                <Download className="w-4 h-4 mr-2" />
                Download All
              </Button>
              
              <Button
                onClick={shareCreation}
                variant="outline"
                className="border-purple-300 text-purple-600 hover:bg-purple-50"
              >
                <Share className="w-4 h-4 mr-2" />
                Share Vision
              </Button>
            </div>

            <div className="text-center">
              <Button
                onClick={() => window.location.href = '/soulmate-drawing'}
                variant="outline"
                className="border-purple-300 text-purple-600 hover:bg-purple-50"
              >
                <Stars className="w-4 h-4 mr-2" />
                Create Another Vision
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Navigation */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="flex justify-between mt-8"
      >
        <Button
          onClick={onPrevious}
          variant="outline"
          size="lg"
          className="px-8 py-3"
        >
          <ArrowLeft className="mr-2 w-5 h-5" />
          Back to Creation
        </Button>
        
        <Button
          onClick={() => window.location.href = '/all-courses'}
          size="lg"
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3"
        >
          Continue Soul Journey
          <Stars className="ml-2 w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  )
}