'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>les, ArrowRight, ArrowLeft, RefreshCw, Download, Share, Heart } from 'lucide-react'
import { AiService } from '@/lib/services/ai.service'

interface SoulmateData {
  soulQualities: any
  visualPreferences: any
  generatedImage?: string
}

interface ImageGenerationStepProps {
  soulmateData: SoulmateData
  onUpdate: (data: Partial<SoulmateData>) => void
  onNext: () => void
  onPrevious: () => void
  isGenerating: boolean
  setIsGenerating: (generating: boolean) => void
}

const generationStages = [
  { title: 'Connecting with your soul energy...', progress: 20 },
  { title: 'Interpreting your spiritual desires...', progress: 40 },
  { title: 'Weaving visual harmony...', progress: 60 },
  { title: 'Manifesting your soulmate\'s essence...', progress: 80 },
  { title: 'Adding the final touches of love...', progress: 95 },
  { title: 'Your soulmate awaits...', progress: 100 }
]

// Mock image URLs for demonstration
const sampleImages = [
  '/api/placeholder/512/512',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=512&h=512&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=512&h=512&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1494790108755-2616b612b1b5?w=512&h=512&fit=crop&crop=face'
]

export function ImageGenerationStep({ 
  soulmateData, 
  onUpdate, 
  onNext, 
  onPrevious, 
  isGenerating, 
  setIsGenerating 
}: ImageGenerationStepProps) {
  const [currentStage, setCurrentStage] = useState(0)
  const [generatedImages, setGeneratedImages] = useState<string[]>([])
  const [selectedImage, setSelectedImage] = useState<string>('')

  const generatePrompt = () => {
    const { soulQualities, visualPreferences } = soulmateData
    
    // Handle gender
    const genderDescription = soulQualities.gender === 'male' ? 'man' : 
                             soulQualities.gender === 'female' ? 'woman' : 
                             soulQualities.gender === 'non_binary' ? 'person' : 'person'
    
    // Handle multiple personality types
    const personalityTypes = Array.isArray(soulQualities.personalityType) 
      ? soulQualities.personalityType.map((type: string) => type.replace('_', ' ')).join(', ')
      : soulQualities.personalityType?.replace('_', ' ') || ''
    
    // Handle multiple spiritual connections
    const spiritualConnections = Array.isArray(soulQualities.spiritualConnection)
      ? soulQualities.spiritualConnection.map((conn: string) => conn.replace('_', ' ')).join(', ')
      : soulQualities.spiritualConnection?.replace('_', ' ') || ''
    
    // Handle multiple life philosophies
    const lifePhilosophies = Array.isArray(soulQualities.lifePhilosophy)
      ? soulQualities.lifePhilosophy.map((phil: string) => phil.replace('_', ' ')).join(', ')
      : soulQualities.lifePhilosophy?.replace('_', ' ') || ''
    
    // Handle artistic style with specific descriptions
    const getArtisticStyleDescription = (style: string) => {
      switch (style) {
        case 'hand_drawn_sketch':
          return 'rendered in pencil sketch style, black and white line art, hand-drawn illustration, artistic sketch, detailed facial features, soft shading, pencil strokes visible, traditional drawing technique'
        case 'ethereal_watercolor':
          return 'painted in watercolor style, soft flowing colors, dreamy ethereal effects, gentle color bleeds, artistic watercolor technique, delicate brush strokes'
        case 'mystical_oil':
          return 'painted in oil painting style, rich deep colors, classic portrait technique, detailed brushwork, timeless romantic atmosphere, fine art quality'
        case 'celestial_digital':
          return 'created in digital art style, cosmic glowing effects, otherworldly lighting, celestial atmosphere, modern digital painting technique, luminous details'
        case 'vintage_portrait':
          return 'rendered in vintage portrait style, classic elegance, nostalgic atmosphere, sepia or muted tones, timeless beauty, traditional portrait composition'
        case 'fantasy_illustration':
          return 'illustrated in fantasy art style, magical enchanting details, story-like composition, vibrant fantasy colors, imaginative artistic elements'
        default:
          return 'artistic portrait style, high quality, detailed, expressive'
      }
    }
    
    const artisticDescription = getArtisticStyleDescription(visualPreferences.artisticStyle || '')
    
    return `Portrait of a ${genderDescription} with ${personalityTypes} personality, 
    ${soulQualities.energyLevel?.replace('_', ' ')} energy, embodying ${spiritualConnections} spiritual connection,
    and ${lifePhilosophies} life philosophy,
    ${artisticDescription},
    set in a ${visualPreferences.setting?.replace('_', ' ')} during ${visualPreferences.timeOfDay?.replace('_', ' ')},
    conveying a ${visualPreferences.mood?.replace('_', ' ')} atmosphere,
    color palette: ${visualPreferences.colorPalette?.replace('_', ' ')},
    aesthetic vibes: ${visualPreferences.aestheticVibes?.join(', ')}, 
    expressive eyes, soulful expression, high quality, detailed, artistic masterpiece`
  }

  const startGeneration = async () => {
    setIsGenerating(true)
    setCurrentStage(0)
    setGeneratedImages([])

    try {
      // Show progress stages during generation
      const stageInterval = setInterval(() => {
        setCurrentStage(prev => {
          if (prev < generationStages.length - 2) {
            return prev + 1
          }
          return prev
        })
      }, 2000)

      // Generate the actual prompt
      const prompt = generatePrompt()
      
      // Call real AI service
      const response = await AiService.generateImages({
        prompt,
        count: 3
      })

      clearInterval(stageInterval)
      
      if (response.success && response.data) {
        setCurrentStage(generationStages.length - 1)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setGeneratedImages(response.data)
      } else {
        console.error('AI图片生成失败:', response.error)
        // Fallback to sample images if AI fails
        setGeneratedImages(sampleImages.slice(0, 3))
      }
    } catch (error) {
      console.error('生成过程出错:', error)
      // Fallback to sample images if there's an error
      setGeneratedImages(sampleImages.slice(0, 3))
    } finally {
      setIsGenerating(false)
    }
  }

  const regenerateImage = () => {
    // Regenerate with same parameters
    startGeneration()
  }

  const selectImage = (imageUrl: string) => {
    setSelectedImage(imageUrl)
    onUpdate({ generatedImage: imageUrl })
  }

  const downloadImage = () => {
    if (selectedImage) {
      // Create download link
      const link = document.createElement('a')
      link.href = selectedImage
      link.download = 'my-soulmate-portrait.jpg'
      link.click()
    }
  }

  const shareImage = async () => {
    if (selectedImage && navigator.share) {
      try {
        await navigator.share({
          title: 'My Soulmate Portrait',
          text: 'I just created a portrait of my soulmate using AI! 💕',
          url: selectedImage
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <Sparkles className="w-16 h-16 text-purple-500 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-800 mb-2">Sacred Creation</h2>
        <p className="text-lg text-gray-600">
          Watch as your soul's vision manifests into beautiful reality.
        </p>
      </motion.div>

      <AnimatePresence mode="wait">
        {!isGenerating && generatedImages.length === 0 && (
          <motion.div
            key="start"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="text-center"
          >
            <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 p-8">
              <CardContent className="space-y-6">
                <div className="bg-white/70 backdrop-blur-sm rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">Your Soul Portrait Parameters</h3>
                  <div className="text-left space-y-2 text-sm text-gray-600">
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-700 mb-2">Soul Qualities:</h4>
                      <div className="ml-4 space-y-1">
                        <p><strong>Gender:</strong> {soulmateData.soulQualities.gender?.replace('_', ' ')}</p>
                        <p><strong>Soul Energy:</strong> {Array.isArray(soulmateData.soulQualities.personalityType) 
                          ? soulmateData.soulQualities.personalityType.map((type: string) => type.replace('_', ' ')).join(', ')
                          : soulmateData.soulQualities.personalityType?.replace('_', ' ') || ''}</p>
                        <p><strong>Energy Frequency:</strong> {soulmateData.soulQualities.energyLevel?.replace('_', ' ')}</p>
                        <p><strong>Spiritual Connection:</strong> {Array.isArray(soulmateData.soulQualities.spiritualConnection)
                          ? soulmateData.soulQualities.spiritualConnection.map((conn: string) => conn.replace('_', ' ')).join(', ')
                          : soulmateData.soulQualities.spiritualConnection?.replace('_', ' ') || ''}</p>
                        <p><strong>Emotional Depth:</strong> {soulmateData.soulQualities.emotionalDepth?.replace('_', ' ')}</p>
                        <p><strong>Life Philosophy:</strong> {Array.isArray(soulmateData.soulQualities.lifePhilosophy)
                          ? soulmateData.soulQualities.lifePhilosophy.map((phil: string) => phil.replace('_', ' ')).join(', ')
                          : soulmateData.soulQualities.lifePhilosophy?.replace('_', ' ') || ''}</p>
                        {soulmateData.soulQualities.dreamConnection && (
                          <p><strong>Dream Connection:</strong> {soulmateData.soulQualities.dreamConnection.substring(0, 60)}...</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-700 mb-2">Visual Preferences:</h4>
                      <div className="ml-4 space-y-1">
                        <p><strong>Artistic Style:</strong> {soulmateData.visualPreferences.artisticStyle?.replace('_', ' ')}</p>
                        <p><strong>Color Palette:</strong> {soulmateData.visualPreferences.colorPalette?.replace('_', ' ')}</p>
                        <p><strong>Mood:</strong> {soulmateData.visualPreferences.mood?.replace('_', ' ')}</p>
                        <p><strong>Setting:</strong> {soulmateData.visualPreferences.setting?.replace('_', ' ')}</p>
                        <p><strong>Time of Day:</strong> {soulmateData.visualPreferences.timeOfDay?.replace('_', ' ')}</p>
                        <p><strong>Aesthetic Vibes:</strong> {soulmateData.visualPreferences.aestheticVibes?.slice(0, 3).join(', ')}
                          {soulmateData.visualPreferences.aestheticVibes?.length > 3 && '...'}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <Button
                  onClick={startGeneration}
                  size="lg"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-12 py-4 text-lg"
                >
                  <Sparkles className="mr-3 w-6 h-6" />
                  Manifest My Soulmate
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {isGenerating && (
          <motion.div
            key="generating"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="text-center"
          >
            <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 p-8">
              <CardContent className="space-y-8">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Sparkles className="w-16 h-16 text-purple-500 mx-auto" />
                </motion.div>
                
                <div className="space-y-4">
                  <h3 className="text-2xl font-semibold text-gray-800">
                    {generationStages[currentStage]?.title}
                  </h3>
                  <Progress 
                    value={generationStages[currentStage]?.progress || 0} 
                    className="w-full h-3"
                  />
                  <p className="text-gray-600">
                    The universe is aligning to bring your soulmate's image into existence...
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {generatedImages.length > 0 && (
          <motion.div
            key="results"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
          >
            <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 p-6">
              <CardContent className="space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-semibold text-gray-800 mb-2">Your Soulmate Awaits</h3>
                  <p className="text-gray-600">Choose the portrait that speaks to your heart</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {generatedImages.map((imageUrl, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className={`relative cursor-pointer group ${
                        selectedImage === imageUrl ? 'ring-4 ring-purple-400' : ''
                      }`}
                      onClick={() => selectImage(imageUrl)}
                    >
                      <div className="aspect-square rounded-lg overflow-hidden">
                        <Image
                          src={imageUrl}
                          alt={`AI generated soulmate portrait ${index + 1} - Personalized love match visualization`}
                          width={512}
                          height={512}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                          placeholder="blur"
                          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkbHw8f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                        />
                      </div>
                      {selectedImage === imageUrl && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute top-2 right-2 bg-purple-500 text-white rounded-full p-2"
                        >
                          <Heart className="w-4 h-4 fill-current" />
                        </motion.div>
                      )}
                    </motion.div>
                  ))}
                </div>

                {selectedImage && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-center space-x-4"
                  >
                    <Button
                      onClick={downloadImage}
                      variant="outline"
                      className="border-purple-300 text-purple-600 hover:bg-purple-50"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      onClick={shareImage}
                      variant="outline"
                      className="border-purple-300 text-purple-600 hover:bg-purple-50"
                    >
                      <Share className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                    <Button
                      onClick={regenerateImage}
                      variant="outline"
                      className="border-purple-300 text-purple-600 hover:bg-purple-50"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Regenerate
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="flex justify-between mt-8"
      >
        <Button
          onClick={onPrevious}
          variant="outline"
          size="lg"
          className="px-8 py-3"
          disabled={isGenerating}
        >
          <ArrowLeft className="mr-2 w-5 h-5" />
          Back to Visual Harmony
        </Button>
        
        <Button
          onClick={onNext}
          disabled={!selectedImage}
          size="lg"
          className={`${
            selectedImage
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
              : 'bg-gray-300'
          } text-white px-8 py-3`}
        >
          Discover Soul Story
          <ArrowRight className="ml-2 w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  )
}