'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { <PERSON><PERSON>, ArrowRight, ArrowLeft } from 'lucide-react'

interface VisualPreferences {
  artisticStyle: string
  colorPalette: string
  mood: string
  setting: string
  timeOfDay: string
  aestheticVibes: string[]
}

interface VisualPreferencesStepProps {
  data: VisualPreferences
  onUpdate: (data: VisualPreferences) => void
  onNext: () => void
  onPrevious: () => void
}

const visualChoices = {
  artisticStyle: [
    { value: 'hand_drawn_sketch', label: 'Hand-drawn Sketch', description: 'Pencil sketch with visible strokes and soft shading' },
    { value: 'ethereal_watercolor', label: 'Ethereal Watercolor', description: 'Soft, dreamy, and flowing like emotions' },
    { value: 'mystical_oil', label: 'Mystical Oil Painting', description: 'Rich, deep, and timelessly romantic' },
    { value: 'celestial_digital', label: 'Celestial Digital Art', description: 'Cosmic, glowing, and otherworldly' },
    { value: 'vintage_portrait', label: 'Vintage Portrait', description: 'Classic, elegant, and nostalgically beautiful' },
    { value: 'fantasy_illustration', label: 'Fantasy Illustration', description: 'Magical, enchanting, and story-like' }
  ],
  colorPalette: [
    { value: 'soft_pastels', label: 'Soft Pastels', description: 'Pink, lavender, cream, and gentle blues' },
    { value: 'warm_earth', label: 'Warm Earth Tones', description: 'Golden, amber, sage, and terracotta' },
    { value: 'cosmic_blues', label: 'Cosmic Blues', description: 'Deep blues, purples, and silver accents' },
    { value: 'sunset_glow', label: 'Sunset Glow', description: 'Orange, pink, gold, and warm purples' },
    { value: 'mystical_jewels', label: 'Mystical Jewel Tones', description: 'Emerald, sapphire, amethyst, and gold' }
  ],
  mood: [
    { value: 'serene_peaceful', label: 'Serene & Peaceful', description: 'Calm, meditative, and harmonious' },
    { value: 'romantic_dreamy', label: 'Romantic & Dreamy', description: 'Loving, soft, and heart-warming' },
    { value: 'mysterious_alluring', label: 'Mysterious & Alluring', description: 'Intriguing, deep, and captivating' },
    { value: 'joyful_radiant', label: 'Joyful & Radiant', description: 'Happy, bright, and life-affirming' },
    { value: 'wise_ancient', label: 'Wise & Ancient', description: 'Timeless, knowing, and spiritually deep' }
  ],
  setting: [
    { value: 'enchanted_garden', label: 'Enchanted Garden', description: 'Flowers, vines, and natural magic' },
    { value: 'cosmic_space', label: 'Cosmic Space', description: 'Stars, galaxies, and infinite wonder' },
    { value: 'mystical_forest', label: 'Mystical Forest', description: 'Ancient trees, dappled light, and secrets' },
    { value: 'serene_water', label: 'Serene Waterscape', description: 'Lakes, oceans, or flowing streams' },
    { value: 'ethereal_clouds', label: 'Ethereal Cloudscape', description: 'Sky, clouds, and heavenly realms' }
  ],
  timeOfDay: [
    { value: 'golden_hour', label: 'Golden Hour', description: 'Warm, magical light at sunset/sunrise' },
    { value: 'twilight_blue', label: 'Twilight Blue Hour', description: 'Soft, mysterious evening glow' },
    { value: 'starlit_night', label: 'Starlit Night', description: 'Deep, romantic darkness with stars' },
    { value: 'dawn_awakening', label: 'Dawn Awakening', description: 'Fresh, hopeful morning light' },
    { value: 'timeless_moment', label: 'Timeless Moment', description: 'Beyond time, pure spiritual presence' }
  ]
}

const aestheticVibes = [
  'Bohemian & Free-spirited',
  'Elegant & Refined',
  'Natural & Earth-connected',
  'Cosmic & Spiritual',
  'Vintage & Nostalgic',
  'Minimalist & Pure',
  'Artistic & Creative',
  'Royal & Majestic',
  'Whimsical & Playful',
  'Dark & Mysterious'
]

export function VisualPreferencesStep({ data, onUpdate, onNext, onPrevious }: VisualPreferencesStepProps) {
  const handleRadioChange = (category: string, value: string) => {
    onUpdate({
      ...data,
      [category]: value
    })
  }

  const handleVibeToggle = (vibe: string, checked: boolean) => {
    const updatedVibes = checked
      ? [...data.aestheticVibes, vibe]
      : data.aestheticVibes.filter(v => v !== vibe)
    
    onUpdate({
      ...data,
      aestheticVibes: updatedVibes
    })
  }

  const isComplete = data.artisticStyle && data.colorPalette && data.mood && 
                   data.setting && data.timeOfDay && data.aestheticVibes.length > 0

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <Palette className="w-16 h-16 text-purple-500 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-800 mb-2">Visual Harmony & Aesthetic</h2>
        <p className="text-lg text-gray-600">
          Choose the visual elements that resonate with your soul's artistic vision of love.
        </p>
      </motion.div>

      <div className="space-y-8">
        {Object.entries(visualChoices).map(([category, options], categoryIndex) => (
          <motion.div
            key={category}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: categoryIndex * 0.1 }}
          >
            <Card className="border-2 border-purple-100 hover:border-purple-200 transition-colors">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800 capitalize">
                  {category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={data[category as keyof VisualPreferences] as string}
                  onValueChange={(value) => handleRadioChange(category, value)}
                  className="space-y-3"
                >
                  {options.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-start space-x-3 p-3 rounded-lg hover:bg-purple-50 transition-colors cursor-pointer"
                    >
                      <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                      <Label
                        htmlFor={option.value}
                        className="cursor-pointer flex-1 leading-relaxed"
                      >
                        <span className="font-medium text-gray-800">{option.label}</span>
                        <span className="text-gray-600 block text-sm mt-1">{option.description}</span>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </CardContent>
            </Card>
          </motion.div>
        ))}

        {/* Aesthetic Vibes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: Object.keys(visualChoices).length * 0.1 }}
        >
          <Card className="border-2 border-purple-100 hover:border-purple-200 transition-colors">
            <CardHeader>
              <CardTitle className="text-xl text-gray-800">
                Aesthetic Vibes (Select all that resonate)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {aestheticVibes.map((vibe) => (
                  <div
                    key={vibe}
                    className="flex items-center space-x-2 p-3 rounded-lg hover:bg-purple-50 transition-colors"
                  >
                    <Checkbox
                      id={vibe}
                      checked={data.aestheticVibes.includes(vibe)}
                      onCheckedChange={(checked) => handleVibeToggle(vibe, checked as boolean)}
                    />
                    <Label
                      htmlFor={vibe}
                      className="cursor-pointer text-sm leading-relaxed"
                    >
                      {vibe}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="flex justify-between mt-8"
      >
        <Button
          onClick={onPrevious}
          variant="outline"
          size="lg"
          className="px-8 py-3"
        >
          <ArrowLeft className="mr-2 w-5 h-5" />
          Back to Soul Essence
        </Button>
        
        <Button
          onClick={onNext}
          disabled={!isComplete}
          size="lg"
          className={`${
            isComplete
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
              : 'bg-gray-300'
          } text-white px-8 py-3`}
        >
          Begin Sacred Creation
          <ArrowRight className="ml-2 w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  )
}