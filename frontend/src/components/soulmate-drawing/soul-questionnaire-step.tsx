'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Heart, ArrowRight } from 'lucide-react'

interface SoulQualities {
  gender: string
  personalityType: string[]
  energyLevel: string
  spiritualConnection: string[]
  emotionalDepth: string
  lifePhilosophy: string[]
  dreamConnection: string
}

interface SoulQuestionnaireStepProps {
  data: SoulQualities
  onUpdate: (data: SoulQualities) => void
  onNext: () => void
}

const questions = [
  {
    id: 'gender',
    title: 'What is your soulmate\'s gender identity?',
    type: 'radio',
    options: [
      { value: 'male', label: 'Male - Masculine energy and presence' },
      { value: 'female', label: 'Female - Feminine energy and presence' },
      { value: 'non_binary', label: 'Non-binary - Beyond traditional gender expressions' },
      { value: 'any', label: 'Any - Soul connection transcends gender' }
    ]
  },
  {
    id: 'personalityType',
    title: 'What kind of soul energy draws you most? (Select all that resonate)',
    type: 'checkbox',
    options: [
      { value: 'gentle_dreamer', label: 'Gentle Dreamer - Soft, contemplative, and deeply intuitive' },
      { value: 'radiant_spirit', label: 'Radiant Spirit - Bright, optimistic, and inspiring' },
      { value: 'mysterious_depth', label: 'Mysterious Depth - Complex, enigmatic, and profoundly wise' },
      { value: 'grounded_soul', label: 'Grounded Soul - Stable, nurturing, and authentically present' },
      { value: 'free_wanderer', label: 'Free Wanderer - Adventurous, spontaneous, and beautifully untamed' }
    ]
  },
  {
    id: 'energyLevel',
    title: 'What energy frequency resonates with your heart?',
    type: 'radio',
    options: [
      { value: 'calm_serenity', label: 'Calm Serenity - Like a peaceful lake at dawn' },
      { value: 'gentle_warmth', label: 'Gentle Warmth - Like sunlight filtering through trees' },
      { value: 'vibrant_flow', label: 'Vibrant Flow - Like a dancing flame or flowing river' },
      { value: 'electric_spark', label: 'Electric Spark - Like lightning illuminating the night' },
      { value: 'cosmic_depth', label: 'Cosmic Depth - Like the infinite mystery of stars' }
    ]
  },
  {
    id: 'spiritualConnection',
    title: 'How do you envision connecting on a soul level? (Select all that resonate)',
    type: 'checkbox',
    options: [
      { value: 'silent_understanding', label: 'Silent Understanding - Communication beyond words' },
      { value: 'shared_dreams', label: 'Shared Dreams - Meeting in the realm of sleep and visions' },
      { value: 'natural_harmony', label: 'Natural Harmony - Finding each other in nature\'s embrace' },
      { value: 'creative_fusion', label: 'Creative Fusion - Co-creating beauty and art together' },
      { value: 'spiritual_journey', label: 'Spiritual Journey - Growing together toward enlightenment' }
    ]
  },
  {
    id: 'emotionalDepth',
    title: 'What emotional landscape speaks to your soul?',
    type: 'radio',
    options: [
      { value: 'ocean_depths', label: 'Ocean Depths - Profound, mysterious, and endlessly deep' },
      { value: 'mountain_strength', label: 'Mountain Strength - Solid, enduring, and majestically calm' },
      { value: 'forest_wisdom', label: 'Forest Wisdom - Ancient, nurturing, and full of secrets' },
      { value: 'desert_clarity', label: 'Desert Clarity - Pure, honest, and beautifully stark' },
      { value: 'sky_freedom', label: 'Sky Freedom - Boundless, ever-changing, and eternally hopeful' }
    ]
  },
  {
    id: 'lifePhilosophy',
    title: 'What life philosophy would your soulmate embrace? (Select all that resonate)',
    type: 'checkbox',
    options: [
      { value: 'love_is_everything', label: 'Love is Everything - Living with an open, compassionate heart' },
      { value: 'beauty_seeker', label: 'Beauty Seeker - Finding magic and wonder in everyday moments' },
      { value: 'truth_finder', label: 'Truth Finder - Pursuing authentic understanding and wisdom' },
      { value: 'harmony_creator', label: 'Harmony Creator - Bringing balance and peace to the world' },
      { value: 'soul_explorer', label: 'Soul Explorer - Always growing, learning, and evolving' }
    ]
  }
]

export function SoulQuestionnaireStep({ data, onUpdate, onNext }: SoulQuestionnaireStepProps) {
  const [dreamConnection, setDreamConnection] = useState(data.dreamConnection || '')

  const handleOptionChange = (questionId: string, value: string) => {
    onUpdate({
      ...data,
      [questionId]: value
    })
  }

  const handleCheckboxChange = (questionId: string, value: string, checked: boolean) => {
    const currentValues = data[questionId as keyof SoulQualities] as string[] || []
    const updatedValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value)
    
    onUpdate({
      ...data,
      [questionId]: updatedValues
    })
  }

  const handleDreamConnectionChange = (value: string) => {
    setDreamConnection(value)
    onUpdate({
      ...data,
      dreamConnection: value
    })
  }

  const isComplete = questions.every(q => {
    const value = data[q.id as keyof SoulQualities]
    if (q.type === 'checkbox') {
      return Array.isArray(value) && value.length > 0
    }
    return value && value !== ''
  }) && dreamConnection.trim()

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <Heart className="w-16 h-16 text-pink-500 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-800 mb-2">Discover Your Soul's Desires</h2>
        <p className="text-lg text-gray-600">
          Let your heart guide you through these questions to reveal the essence of your perfect spiritual connection.
        </p>
      </motion.div>

      <div className="space-y-8">
        {questions.map((question, index) => (
          <motion.div
            key={question.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="border-2 border-purple-100 hover:border-purple-200 transition-colors">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">{question.title}</CardTitle>
              </CardHeader>
              <CardContent>
                {question.type === 'radio' ? (
                  <RadioGroup
                    value={data[question.id as keyof SoulQualities] as string}
                    onValueChange={(value) => handleOptionChange(question.id, value)}
                    className="space-y-3"
                  >
                    {question.options.map((option) => (
                      <div
                        key={option.value}
                        className="flex items-start space-x-3 p-3 rounded-lg hover:bg-purple-50 transition-colors cursor-pointer"
                      >
                        <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                        <Label
                          htmlFor={option.value}
                          className="cursor-pointer flex-1 leading-relaxed"
                        >
                          <span className="font-medium text-gray-800">{option.label.split(' - ')[0]}</span>
                          {option.label.includes(' - ') && (
                            <span className="text-gray-600"> - {option.label.split(' - ')[1]}</span>
                          )}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                ) : (
                  <div className="space-y-3">
                    {question.options.map((option) => (
                      <div
                        key={option.value}
                        className="flex items-start space-x-3 p-3 rounded-lg hover:bg-purple-50 transition-colors"
                      >
                        <Checkbox
                          id={option.value}
                          checked={(data[question.id as keyof SoulQualities] as string[] || []).includes(option.value)}
                          onCheckedChange={(checked) => handleCheckboxChange(question.id, option.value, checked as boolean)}
                          className="mt-1"
                        />
                        <Label
                          htmlFor={option.value}
                          className="cursor-pointer flex-1 leading-relaxed"
                        >
                          <span className="font-medium text-gray-800">{option.label.split(' - ')[0]}</span>
                          {option.label.includes(' - ') && (
                            <span className="text-gray-600"> - {option.label.split(' - ')[1]}</span>
                          )}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}

        {/* Dream Connection Question */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: questions.length * 0.1 }}
        >
          <Card className="border-2 border-purple-100 hover:border-purple-200 transition-colors">
            <CardHeader>
              <CardTitle className="text-xl text-gray-800">
                Describe a recurring dream or vision you've had about meeting your soulmate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={dreamConnection}
                onChange={(e) => handleDreamConnectionChange(e.target.value)}
                placeholder="Share any dreams, visions, or deep feelings you've experienced about your soulmate. This could be a specific scene, feeling, or symbolic image that comes to you..."
                className="min-h-[120px] border-purple-200 focus:border-purple-400"
              />
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="flex justify-end mt-8"
      >
        <Button
          onClick={onNext}
          disabled={!isComplete}
          size="lg"
          className={`${
            isComplete
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
              : 'bg-gray-300'
          } text-white px-8 py-3`}
        >
          Continue to Visual Harmony
          <ArrowRight className="ml-2 w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  )
}