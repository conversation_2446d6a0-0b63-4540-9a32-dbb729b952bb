import { Badge } from '@/components/ui/badge'

interface TestResultCardProps {
  status: boolean
  message?: string
  details?: any
}

export default function TestResultCard({ status, message, details }: TestResultCardProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 rounded-lg border">
        <Badge variant={status ? "default" : "destructive"} className="ml-auto">
          {status ? 'Success' : 'Failed'}
        </Badge>
      </div>
      {message && (
        <div className="p-4 rounded-lg border bg-slate-50">
          <p className="text-sm text-slate-600">{message}</p>
        </div>
      )}
      {details && (
        <div className="p-4 rounded-lg border bg-slate-50">
          <pre className="text-sm text-slate-600 whitespace-pre-wrap">
            {JSON.stringify(details, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
} 