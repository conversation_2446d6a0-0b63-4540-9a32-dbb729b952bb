'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, Sparkles, Zap, ArrowRight, X } from 'lucide-react';
import Link from 'next/link';
import { api } from '@/lib/api';

interface SubscriptionCTAProps {
  variant?: 'banner' | 'card' | 'inline' | 'popup';
  title?: string;
  description?: string;
  showDismiss?: boolean;
  className?: string;
  onDismiss?: () => void;
  context?: 'course-list' | 'course-detail' | 'purchase' | 'learning' | 'dashboard';
  courseId?: string;
  accessReason?: 'free_course' | 'purchased' | 'subscription' | 'no_access';
}

const contextConfig = {
  'course-list': {
    title: 'Unlock All Courses with Subscription',
    description: 'Get unlimited access to all courses, AI conversations, and premium features',
    icon: <Crown className="w-5 h-5" />,
    badge: 'Most Popular',
    cta: 'View Plans'
  },
  'course-detail': {
    title: 'Save More with Subscription',
    description: 'Subscribe and get this course plus hundreds more for less than the cost of 3 individual courses',
    icon: <Sparkles className="w-5 h-5" />,
    badge: 'Best Value',
    cta: 'Compare Plans'
  },
  'purchase': {
    title: 'Consider Subscription Instead',
    description: 'Get unlimited course access and AI conversations starting from $29.99/month',
    icon: <Zap className="w-5 h-5" />,
    badge: 'Recommended',
    cta: 'See Subscription'
  },
  'learning': {
    title: 'Enjoying This Course?',
    description: 'Subscribe to unlock all courses and get unlimited AI learning support',
    icon: <Crown className="w-5 h-5" />,
    badge: 'Upgrade',
    cta: 'Unlock More'
  },
  'dashboard': {
    title: 'Maximize Your Learning',
    description: 'Subscribe to access all courses and get personalized AI tutoring',
    icon: <Sparkles className="w-5 h-5" />,
    badge: 'Premium',
    cta: 'Upgrade Now'
  }
};

export default function SubscriptionCTA({
  variant = 'card',
  title,
  description,
  showDismiss = false,
  className = '',
  onDismiss,
  context = 'course-list',
  courseId,
  accessReason
}: SubscriptionCTAProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [userSubscription, setUserSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const config = contextConfig[context];
  const displayTitle = title || config.title;
  const displayDescription = description || config.description;

  useEffect(() => {
    checkSubscriptionStatus();
  }, []);

  const checkSubscriptionStatus = async () => {
    try {
      const response = await api.get('/api/subscription/current');
      setUserSubscription(response.data);
    } catch (error) {
      console.error('Failed to check subscription status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  // Don't show if user already has an active subscription
  if (loading) return null;
  if (userSubscription?.isActive) return null;
  if (!isVisible) return null;

  // Build subscription link URL
  const subscriptionUrl = courseId 
    ? `/subscription/plans?courseId=${courseId}&source=${context}`
    : '/subscription/plans';

  const BannerVariant = () => (
    <div className={`bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 ${className}`}>
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="p-2 bg-white/20 rounded-full">
            {config.icon}
          </div>
          <div>
            <h3 className="font-semibold">{displayTitle}</h3>
            <p className="text-sm opacity-90">{displayDescription}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
            {config.badge}
          </Badge>
          <Link href={subscriptionUrl}>
            <Button variant="secondary" size="sm" className="bg-white text-purple-600 hover:bg-gray-100">
              {config.cta}
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </Link>
          {showDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-white hover:bg-white/20 p-1"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  const CardVariant = () => (
    <Card className={`border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-purple-100 rounded-full text-purple-600">
              {config.icon}
            </div>
            <div>
              <CardTitle className="text-lg">{displayTitle}</CardTitle>
              <Badge variant="secondary" className="mt-1 bg-purple-100 text-purple-700">
                {config.badge}
              </Badge>
            </div>
          </div>
          {showDismiss && (
            <Button variant="ghost" size="sm" onClick={handleDismiss} className="h-8 w-8 p-0">
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription className="mb-4 text-gray-600">
          {displayDescription}
        </CardDescription>
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Starting from <span className="font-semibold text-gray-900">$29.99/month</span>
          </div>
          <Link href={subscriptionUrl}>
            <Button className="bg-purple-600 hover:bg-purple-700">
              {config.cta}
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );

  const InlineVariant = () => (
    <div className={`flex items-center gap-3 p-3 bg-purple-50 border border-purple-200 rounded-lg ${className}`}>
      <div className="p-1 bg-purple-100 rounded-full text-purple-600">
        {config.icon}
      </div>
      <div className="flex-1">
        <h4 className="font-medium text-gray-900">{displayTitle}</h4>
        <p className="text-sm text-gray-600">{displayDescription}</p>
      </div>
      <Badge variant="secondary" className="bg-purple-100 text-purple-700">
        {config.badge}
      </Badge>
      <Link href={subscriptionUrl}>
        <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
          {config.cta}
        </Button>
      </Link>
      {showDismiss && (
        <Button variant="ghost" size="sm" onClick={handleDismiss} className="h-8 w-8 p-0">
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  );

  const PopupVariant = () => (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className={`border-purple-200 bg-white shadow-lg ${className}`}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="bg-purple-100 text-purple-700">
              {config.badge}
            </Badge>
            <Button variant="ghost" size="sm" onClick={handleDismiss} className="h-6 w-6 p-0">
              <X className="w-3 h-3" />
            </Button>
          </div>
          <CardTitle className="text-sm flex items-center gap-2">
            {config.icon}
            {displayTitle}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <CardDescription className="text-xs mb-3">
            {displayDescription}
          </CardDescription>
          <Link href={subscriptionUrl}>
            <Button size="sm" className="w-full bg-purple-600 hover:bg-purple-700">
              {config.cta}
              <ArrowRight className="w-3 h-3 ml-1" />
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );

  switch (variant) {
    case 'banner':
      return <BannerVariant />;
    case 'card':
      return <CardVariant />;
    case 'inline':
      return <InlineVariant />;
    case 'popup':
      return <PopupVariant />;
    default:
      return <CardVariant />;
  }
}