'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Calendar, MessageCircle, Settings, AlertCircle, CheckCircle } from 'lucide-react';

interface SubscriptionStatus {
  id: string;
  planName: string;
  price: number;
  startDate: string;
  endDate: string;
  aiCallLimit: number;
  aiCallUsed: number;
  isActive: boolean;
  status: 'active' | 'inactive' | 'cancelled' | 'expired' | 'trial';
  autoRenew: boolean;
  trialEndDate?: string;
  cancelledAt?: string;
  nextBillingDate?: string;
  plan?: {
    id: string;
    name: string;
    description?: string;
    type: string;
    features: string[];
  };
}

interface SubscriptionUsage {
  aiCallsUsed: number;
  aiCallsLimit: number;
  aiCallsRemaining: number;
  usagePercentage: number;
  daysRemaining: number;
  isActive: boolean;
  status: string;
  renewalDate?: string;
  trialEndDate?: string;
}

interface SubscriptionStatusProps {
  onManageSubscription?: () => void;
  onUpgrade?: () => void;
}

const statusColors = {
  active: 'bg-green-500',
  trial: 'bg-blue-500',
  cancelled: 'bg-orange-500',
  expired: 'bg-red-500',
  inactive: 'bg-gray-500'
};

const statusTexts = {
  active: 'Active',
  trial: 'Trial',
  cancelled: 'Cancelled',
  expired: 'Expired',
  inactive: 'Inactive'
};

export default function SubscriptionStatus({ 
  onManageSubscription, 
  onUpgrade 
}: SubscriptionStatusProps) {
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null);
  const [usage, setUsage] = useState<SubscriptionUsage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, usageResponse] = await Promise.all([
        fetch('/api/subscription/current'),
        fetch('/api/subscription/usage')
      ]);

      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json();
        setSubscription(subscriptionData);
      }

      if (usageResponse.ok) {
        const usageData = await usageResponse.json();
        setUsage(usageData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'trial':
        return <MessageCircle className="w-5 h-5 text-blue-500" />;
      case 'cancelled':
      case 'expired':
        return <AlertCircle className="w-5 h-5 text-orange-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2].map((i) => (
          <div key={i} className="animate-pulse">
            <Card>
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">Error loading subscription status: {error}</p>
            <Button onClick={fetchSubscriptionStatus} variant="outline">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Active Subscription</h3>
            <p className="text-gray-600 mb-4">
              Subscribe to our service to unlock unlimited AI learning possibilities
            </p>
            <Button onClick={onUpgrade}>
              View Subscription Plans
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Subscription Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(subscription.status)}
                {subscription.planName}
              </CardTitle>
              <CardDescription>
                {formatPrice(subscription.price)} - {statusTexts[subscription.status]}
              </CardDescription>
            </div>
            <Badge 
              className={`${statusColors[subscription.status]} text-white`}
              variant="secondary"
            >
              {statusTexts[subscription.status]}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4" />
                <span>Start Date: {formatDate(subscription.startDate)}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4" />
                <span>End Date: {formatDate(subscription.endDate)}</span>
              </div>
              {subscription.nextBillingDate && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="w-4 h-4" />
                  <span>Next Billing: {formatDate(subscription.nextBillingDate)}</span>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Settings className="w-4 h-4" />
                <span>Auto Renew: {subscription.autoRenew ? 'On' : 'Off'}</span>
              </div>
              {subscription.trialEndDate && (
                <div className="flex items-center gap-2 text-sm">
                  <MessageCircle className="w-4 h-4" />
                  <span>Trial Ends: {formatDate(subscription.trialEndDate)}</span>
                </div>
              )}
            </div>
          </div>

          {subscription.plan?.features && subscription.plan.features.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="font-semibold mb-2">Plan Features</h4>
                <ul className="text-sm space-y-1">
                  {subscription.plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          )}

          <div className="flex gap-2">
            <Button variant="outline" onClick={onManageSubscription}>
              Manage Subscription
            </Button>
            <Button variant="outline" onClick={onUpgrade}>
              Upgrade Plan
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usage Information */}
      {usage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              AI Chat Usage
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Used</span>
                <span>
                  {usage.aiCallsUsed.toLocaleString()} / {usage.aiCallsLimit.toLocaleString()}
                </span>
              </div>
              <Progress value={usage.usagePercentage} className="h-2" />
              <div className="flex justify-between text-sm text-gray-600">
                <span>Usage: {usage.usagePercentage.toFixed(1)}%</span>
                <span>Remaining: {usage.aiCallsRemaining.toLocaleString()} calls</span>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {usage.daysRemaining}
                  </div>
                  <div className="text-sm text-gray-600">days until expiry</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {usage.aiCallsRemaining.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">calls remaining</div>
                </div>
              </div>
            </div>

            {usage.usagePercentage > 80 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-orange-800">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">Usage Alert</span>
                </div>
                <p className="text-sm text-orange-700 mt-1">
                  Your AI chat usage is running low. Consider upgrading to a higher plan or wait for the next billing cycle.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}