'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Check, Crown, Star, Zap } from 'lucide-react';
import SubscriptionPaymentDialog from '@/components/payment/subscription-payment-dialog';
import { api } from '@/lib/api';

interface SubscriptionPlan {
  id: string;
  name: string;
  description?: string;
  price: number;
  type: 'monthly' | 'quarterly' | 'yearly';
  durationDays: number;
  aiCallLimit: number;
  maxCourses?: number;
  trialDays: number;
  features: string[];
  stripePriceId?: string;
}

interface SubscriptionPlansProps {
  onSelectPlan?: (plan: SubscriptionPlan) => void;
  currentPlanId?: string;
}

const planIcons = {
  monthly: <Star className="w-6 h-6" />,
  quarterly: <Zap className="w-6 h-6" />,
  yearly: <Crown className="w-6 h-6" />
};

const planColors = {
  monthly: 'bg-blue-500',
  quarterly: 'bg-purple-500',
  yearly: 'bg-gold-500'
};

export default function SubscriptionPlans({ onSelectPlan, currentPlanId }: SubscriptionPlansProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [hoveredPlanId, setHoveredPlanId] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/subscription/plans');
      setPlans(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getPlanTypeText = (type: string) => {
    switch (type) {
      case 'monthly':
        return 'Monthly';
      case 'quarterly':
        return 'Quarterly';
      case 'yearly':
        return 'Yearly';
      default:
        return type;
    }
  };

  const getMostPopularPlan = () => {
    // Usually quarterly plan is the most popular
    return plans.find(plan => plan.type === 'quarterly');
  };

  const mostPopularPlan = getMostPopularPlan();

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setPaymentDialogOpen(true);
    onSelectPlan?.(plan);
  };

  const handlePaymentSuccess = (subscriptionId: string) => {
    console.log('Subscription created successfully:', subscriptionId);
    // Refresh plans or redirect to success page
    window.location.href = '/dashboard';
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    // Show error notification
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <Card className="h-96">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-2">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">Error loading subscription plans: {error}</p>
        <Button onClick={fetchPlans} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">Choose Your Perfect Subscription Plan</h2>
        <p className="text-gray-600">Unlock unlimited AI learning possibilities</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className={`relative transition-all duration-300 hover:shadow-lg hover:scale-105 hover:-translate-y-1 cursor-pointer ${
              currentPlanId === plan.id ? 'ring-2 ring-blue-500' : ''
            } ${
              hoveredPlanId === plan.id ? 'border-purple-500 scale-102' : ''
            } ${
              mostPopularPlan?.id === plan.id && !hoveredPlanId ? 'border-purple-500 scale-102' : ''
            }`}
            onMouseEnter={() => setHoveredPlanId(plan.id)}
            onMouseLeave={() => setHoveredPlanId(null)}
          >
            {mostPopularPlan?.id === plan.id && (
              <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white">
                Most Popular
              </Badge>
            )}
            
            <CardHeader className="text-center">
              <div className={`w-12 h-12 rounded-full ${planColors[plan.type]} flex items-center justify-center text-white mx-auto mb-3`}>
                {planIcons[plan.type]}
              </div>
              <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {plan.description || `${getPlanTypeText(plan.type)} Subscription Plan`}
              </CardDescription>
              <div className="mt-4">
                <div className="text-3xl font-bold text-gray-900">
                  {formatPrice(plan.price)}
                </div>
                <div className="text-sm text-gray-600">
                  /{getPlanTypeText(plan.type)}
                </div>
              </div>
              {plan.trialDays > 0 && (
                <Badge variant="secondary" className="mt-2">
                  {plan.trialDays} Day Free Trial
                </Badge>
              )}
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Check className="w-4 h-4 text-green-500 mr-2" />
                  <span>{plan.aiCallLimit.toLocaleString()} AI Conversations</span>
                </div>
                <div className="flex items-center text-sm">
                  <Check className="w-4 h-4 text-green-500 mr-2" />
                  <span>
                    {plan.maxCourses ? `${plan.maxCourses} Courses` : 'Unlimited Course Access'}
                  </span>
                </div>
                <div className="flex items-center text-sm">
                  <Check className="w-4 h-4 text-green-500 mr-2" />
                  <span>{plan.durationDays} Days Valid</span>
                </div>
              </div>

              {plan.features.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <Check className="w-4 h-4 text-green-500 mr-2" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </>
              )}

              <Button
                className="w-full mt-6"
                onClick={() => handlePlanSelect(plan)}
                disabled={currentPlanId === plan.id}
                variant={currentPlanId === plan.id ? "outline" : "default"}
              >
                {currentPlanId === plan.id ? 'Current Plan' : 'Choose Plan'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center text-sm text-gray-600">
        <p>Start with free trial • Cancel anytime • No long-term commitment</p>
      </div>

      {/* Payment Dialog */}
      <SubscriptionPaymentDialog
        open={paymentDialogOpen}
        onOpenChange={setPaymentDialogOpen}
        plan={selectedPlan}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />
    </div>
  );
}