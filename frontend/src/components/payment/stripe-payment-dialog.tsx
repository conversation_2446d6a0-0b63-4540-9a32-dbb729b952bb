'use client';

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { StripePaymentForm } from './stripe-payment-form';
import { useToast } from '@/components/ui/use-toast';

// Get Stripe public key
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

// Check if public key is valid
if (!stripePublishableKey) {
  console.error('Stripe publishable key is not configured. Please set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY in your .env file.');
}

const stripePromise = loadStripe(stripePublishableKey || '');

interface StripePaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clientSecret?: string;
  orderNo?: string;
  amount: number;
  isLoading?: boolean;
  onCancel?: () => void;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export function StripePaymentDialog({
  open,
  onOpenChange,
  clientSecret,
  orderNo,
  amount,
  isLoading,
  onCancel,
  onSuccess,
  onError,
}: StripePaymentDialogProps) {
  const { toast } = useToast();
  const [appearance] = useState<{
    theme: 'stripe' | 'night' | 'flat';
    variables: {
      colorPrimary: string;
    };
  }>({
    theme: 'stripe',
    variables: {
      colorPrimary: '#3b82f6',
    },
  });

  useEffect(() => {
    if (!stripePublishableKey) {
      toast({
        variant: 'destructive',
        title: 'Payment System Configuration Error',
        description: 'Please contact administrator',
      });
      onError('Payment system not properly configured');
    }
  }, [toast, onError]);

  if (!stripePublishableKey) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Payment System Error</DialogTitle>
            <DialogDescription>
              Payment system not properly configured, please contact administrator
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button variant="outline" onClick={onCancel}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!clientSecret && !isLoading) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Credit Card Payment</DialogTitle>
          <DialogDescription>
            Please fill in your payment information to complete the purchase
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-[300px]">
              <LoadingSpinner className="w-8 h-8" />
            </div>
          ) : clientSecret ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Order Number: {orderNo}</span>
                <span className="font-semibold">${Number(amount).toFixed(2)}</span>
              </div>

              <Elements
                stripe={stripePromise}
                options={{
                  clientSecret,
                  appearance,
                }}
              >
                <StripePaymentForm
                  amount={amount}
                  onSuccess={onSuccess}
                  onError={onError}
                />
              </Elements>
            </div>
          ) : null}
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onCancel}>
            Cancel Payment
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
