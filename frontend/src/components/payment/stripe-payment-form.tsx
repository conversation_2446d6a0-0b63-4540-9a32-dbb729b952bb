'use client';

import { useState } from 'react';
import { useStripe, useElements, PaymentElement, AddressElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/components/ui/use-toast';

interface StripePaymentFormProps {
  amount: number;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export function StripePaymentForm({ amount, onSuccess, onError }: StripePaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!stripe || !elements) {
      setErrorMessage('支付系统未就绪，请刷新页面重试');
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/success`,
        },
        redirect: 'if_required',
      });

      if (error) {
        const errorMessage = error.message || '支付失败，请重试';
        setErrorMessage(errorMessage);
        onError(errorMessage);
        toast({
          variant: 'destructive',
          title: '支付失败',
          description: errorMessage,
        });
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess();
        toast({
          title: '支付成功',
          description: '订单支付已完成',
        });
      } else {
        const message = '支付处理中，请稍后查看订单状态';
        setErrorMessage(message);
        toast({
          title: '支付处理中',
          description: message,
        });
      }
    } catch (err) {
      const errorMessage = '支付过程中发生错误，请重试';
      setErrorMessage(errorMessage);
      onError(errorMessage);
      toast({
        variant: 'destructive',
        title: '支付错误',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <PaymentElement />
        <AddressElement options={{ mode: 'billing' }} />
      </div>

      {errorMessage && (
        <div className="text-red-500 text-sm">{errorMessage}</div>
      )}

      <Button 
        type="submit" 
        disabled={!stripe || isLoading} 
        className="w-full bg-blue-600 hover:bg-blue-700"
      >
        {isLoading ? (
          <>
            <LoadingSpinner className="mr-2 h-4 w-4" />
            处理中...
          </>
        ) : (
          `支付 $${Number(amount).toFixed(2)}`
        )}
      </Button>
    </form>
  );
}
