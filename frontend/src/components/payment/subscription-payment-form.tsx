'use client';

import { useState } from 'react';
import { useStripe, useElements, PaymentElement, AddressElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  type: string;
  trialDays: number;
}

interface SubscriptionPaymentFormProps {
  clientSecret: string;
  subscriptionId: string;
  plan: SubscriptionPlan;
  onSuccess: (subscriptionId: string) => void;
  onError: (error: string) => void;
  isTrialSubscription?: boolean;
  isSetupIntent?: boolean;
}

export default function SubscriptionPaymentForm({
  clientSecret,
  subscriptionId,
  plan,
  onSuccess,
  onError,
  isTrialSubscription = false,
  isSetupIntent = false
}: SubscriptionPaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw new Error(submitError.message);
      }

      let confirmError;
      
      if (isSetupIntent) {
        // 对于 Setup Intent，使用 confirmSetup
        const result = await stripe.confirmSetup({
          elements,
          clientSecret,
          confirmParams: {
            return_url: `${window.location.origin}/subscription/success?subscription_id=${subscriptionId}`,
          },
          redirect: 'if_required',
        });
        confirmError = result.error;
      } else {
        // 对于 Payment Intent，使用 confirmPayment
        const result = await stripe.confirmPayment({
          elements,
          clientSecret,
          confirmParams: {
            return_url: `${window.location.origin}/subscription/success?subscription_id=${subscriptionId}`,
          },
          redirect: 'if_required',
        });
        confirmError = result.error;
      }

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      // 如果是试用期订阅，需要激活本地订阅
      if (isTrialSubscription) {
        // 调用后端激活试用期订阅
        const response = await fetch('/api/subscription/activate-trial', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          },
          body: JSON.stringify({ subscriptionId }),
        });

        if (!response.ok) {
          throw new Error('Failed to activate trial subscription');
        }
      }

      setSuccess(true);
      onSuccess(subscriptionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              {isTrialSubscription ? 'Trial Subscription Started!' : 'Subscription Activated!'}
            </h3>
            <p className="text-green-700">
              {isTrialSubscription 
                ? `Your trial subscription to ${plan.name} has been started. You'll be charged ${formatPrice(plan.price)} when the trial ends.`
                : `Your subscription to ${plan.name} has been successfully activated.`
              }
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Subscription Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Subscription Summary</CardTitle>
          <CardDescription>
            {plan.name} - {formatPrice(plan.price)}
            {plan.trialDays > 0 && (
              <span className="ml-2 text-blue-600">
                (includes {plan.trialDays}-day free trial)
              </span>
            )}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Payment Method */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Payment Method</CardTitle>
          <CardDescription>
            {isTrialSubscription 
              ? 'Set up your payment method for when the trial ends. You won\'t be charged now.'
              : 'Choose your payment method for the subscription'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentElement
            options={{
              layout: 'tabs',
              wallets: {
                applePay: 'auto',
                googlePay: 'auto',
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Billing Address</CardTitle>
          <CardDescription>
            Enter your billing address information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AddressElement
            options={{
              mode: 'billing',
              allowedCountries: ['US'],
            }}
          />
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Payment Error</span>
          </div>
          <p className="text-sm text-red-700 mt-1">{error}</p>
        </div>
      )}

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={!stripe || loading}
        className="w-full"
        size="lg"
      >
        {loading ? (
          'Processing...'
        ) : isTrialSubscription ? (
          `Set up payment & start ${plan.trialDays}-day trial`
        ) : (
          `Subscribe for ${formatPrice(plan.price)}`
        )}
      </Button>

      {/* Terms */}
      <div className="text-xs text-gray-500 text-center">
        {plan.trialDays > 0 ? (
          <>
            Your free trial starts today. You'll be charged {formatPrice(plan.price)} after {plan.trialDays} days.
            Cancel anytime during the trial period at no charge.
          </>
        ) : (
          <>
            You'll be charged {formatPrice(plan.price)} today and then automatically renewed.
            Cancel anytime in your account settings.
          </>
        )}
      </div>
    </form>
  );
}