'use client';

import { useState } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Check, Crown, Star, Zap, AlertCircle } from 'lucide-react';
import SubscriptionPaymentForm from './subscription-payment-form';
import { api } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

// Load Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

interface SubscriptionPlan {
  id: string;
  name: string;
  description?: string;
  price: number;
  type: 'monthly' | 'quarterly' | 'yearly';
  durationDays: number;
  aiCallLimit: number;
  maxCourses?: number;
  trialDays: number;
  features: string[];
  stripePriceId?: string;
}

interface SubscriptionPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plan: SubscriptionPlan | null;
  onPaymentSuccess?: (subscriptionId: string) => void;
  onPaymentError?: (error: string) => void;
}

const planIcons = {
  monthly: <Star className="w-6 h-6" />,
  quarterly: <Zap className="w-6 h-6" />,
  yearly: <Crown className="w-6 h-6" />
};

const planColors = {
  monthly: 'bg-blue-500',
  quarterly: 'bg-purple-500',
  yearly: 'bg-gold-500'
};

export default function SubscriptionPaymentDialog({
  open,
  onOpenChange,
  plan,
  onPaymentSuccess,
  onPaymentError
}: SubscriptionPaymentDialogProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTrialSubscription, setIsTrialSubscription] = useState(false);
  const [isSetupIntent, setIsSetupIntent] = useState(false);
  const { toast } = useToast();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getPlanTypeText = (type: string) => {
    switch (type) {
      case 'monthly':
        return 'Monthly';
      case 'quarterly':
        return 'Quarterly';
      case 'yearly':
        return 'Yearly';
      default:
        return type;
    }
  };

  const handleCreateSubscription = async () => {
    if (!plan) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.post('/api/payment/subscriptions/create', {
        planId: plan.id,
        autoRenew: true,
      });

      const data = response.data;
      
      // 所有订阅都需要支付方式设置（包括试用期）
      
      if (data.clientSecret) {
        setClientSecret(data.clientSecret);
        setSubscriptionId(data.subscriptionId);
        setIsTrialSubscription(data.isTrialSubscription || false);
        setIsSetupIntent(data.isSetupIntent || false);
      } else {
        throw new Error('No client secret received');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to create subscription';
      setError(errorMessage);
      onPaymentError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (subscriptionId: string) => {
    setClientSecret(null);
    setSubscriptionId(null);
    onPaymentSuccess?.(subscriptionId);
    onOpenChange(false);
  };

  const handlePaymentError = (error: string) => {
    setError(error);
    onPaymentError?.(error);
  };

  const handleClose = () => {
    setClientSecret(null);
    setSubscriptionId(null);
    setError(null);
    onOpenChange(false);
  };

  if (!plan) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full ${planColors[plan.type]} flex items-center justify-center text-white`}>
              {planIcons[plan.type]}
            </div>
            Subscribe to {plan.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Plan Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">{plan.name}</CardTitle>
              <CardDescription>
                {plan.description || `${getPlanTypeText(plan.type)} Subscription Plan`}
              </CardDescription>
              <div className="flex items-center gap-2 mt-2">
                <span className="text-2xl font-bold">{formatPrice(plan.price)}</span>
                <span className="text-gray-600">/{getPlanTypeText(plan.type)}</span>
                {plan.trialDays > 0 && (
                  <Badge variant="secondary">
                    {plan.trialDays} Day Free Trial
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-green-500 mr-2" />
                    <span>{plan.aiCallLimit.toLocaleString()} AI Conversations</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-green-500 mr-2" />
                    <span>
                      {plan.maxCourses ? `${plan.maxCourses} Courses` : 'Unlimited Course Access'}
                    </span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-green-500 mr-2" />
                    <span>{plan.durationDays} Days Valid</span>
                  </div>
                </div>
                <div className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">Payment Error</span>
              </div>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          )}

          {/* Payment Section */}
          {!clientSecret ? (
            <div className="space-y-4">
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  Total: {formatPrice(plan.price)}
                </span>
                <Button
                  onClick={handleCreateSubscription}
                  disabled={loading}
                  className="min-w-[120px]"
                >
                  {loading ? 'Creating...' : 'Continue to Payment'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Separator />
              <div className="text-sm text-gray-600 mb-4">
                {isTrialSubscription 
                  ? 'Set up your payment method to start your trial'
                  : 'Complete your payment to activate your subscription'
                }
              </div>
              <Elements stripe={stripePromise}>
                <SubscriptionPaymentForm
                  clientSecret={clientSecret}
                  subscriptionId={subscriptionId!}
                  plan={plan}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  isTrialSubscription={isTrialSubscription}
                  isSetupIntent={isSetupIntent}
                />
              </Elements>
            </div>
          )}

          {/* Terms */}
          <div className="text-xs text-gray-500 text-center">
            By subscribing, you agree to our Terms of Service and Privacy Policy.
            You can cancel your subscription at any time.
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}