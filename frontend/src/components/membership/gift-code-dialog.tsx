'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Loader2 } from 'lucide-react'

interface GiftCodeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (code: string) => Promise<void>
}

export function GiftCodeDialog({ open, onOpenChange, onSubmit }: GiftCodeDialogProps) {
  const [code, setCode] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!code.trim() || isSubmitting) return

    setIsSubmitting(true)
    try {
      await onSubmit(code.trim())
      setCode('')
      onOpenChange(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>使用礼品券</DialogTitle>
          <DialogDescription>
            请输入您的礼品券兑换码，兑换成功后将立即生效
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Input
              placeholder="请输入兑换码"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              disabled={isSubmitting}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={!code.trim() || isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Redeeming...
                </>
              ) : (
                'Redeem Now'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 