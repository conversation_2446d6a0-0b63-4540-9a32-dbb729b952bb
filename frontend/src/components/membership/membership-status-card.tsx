'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Crown, Calendar, RefreshCw } from 'lucide-react'
import { MembershipLevel } from '@/lib/services/user.service'
import { format } from 'date-fns'

interface MembershipStatusCardProps {
  membershipLevel: MembershipLevel
  expiryDate?: string
  autoRenew?: boolean
}

export function MembershipStatusCard({ membershipLevel, expiryDate, autoRenew }: MembershipStatusCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-2">
          <Crown className="w-6 h-6 text-primary" />
          Membership Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Current Level</span>
          <Badge variant={membershipLevel === MembershipLevel.PREMIUM ? 'default' : 'secondary'}>
            {membershipLevel === MembershipLevel.PREMIUM ? 'Premium Member' : 'Regular User'}
          </Badge>
        </div>
        
        {expiryDate && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Expiry Date</span>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span>{format(new Date(expiryDate), 'yyyy-MM-dd')}</span>
            </div>
          </div>
        )}

        {membershipLevel === MembershipLevel.PREMIUM && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Auto Renewal</span>
            <div className="flex items-center gap-2">
              <RefreshCw className={`w-4 h-4 ${autoRenew ? 'text-green-500' : 'text-gray-500'}`} />
              <span>{autoRenew ? 'Enabled' : 'Disabled'}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 