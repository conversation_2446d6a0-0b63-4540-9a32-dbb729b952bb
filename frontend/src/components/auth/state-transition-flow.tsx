'use client'

import { useCallback } from 'react'
import ReactFlow, {
  Node,
  Edge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
} from 'reactflow'
import 'reactflow/dist/style.css'

// 定义状态节点的位置
const nodePositions = {
  UNINITIALIZED: { x: 0, y: 150 },
  INITIALIZING: { x: 250, y: 150 },
  PUBLIC: { x: 500, y: 0 },
  AUTHENTICATED: { x: 500, y: 150 },
  UNAUTHENTICATED: { x: 500, y: 300 },
  ERROR: { x: 750, y: 150 },
}

// 创建状态节点
const initialNodes: Node[] = Object.entries(nodePositions).map(([id, position]) => ({
  id,
  position,
  data: { label: id },
  style: {
    background: '#fff',
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: '10px',
    fontSize: '12px',
    width: 150,
    textAlign: 'center' as const,
  },
}))

// 创建状态转换边
const createEdges = (transitions: Record<string, string[]>): Edge[] => {
  const edges: Edge[] = []
  let edgeId = 1

  Object.entries(transitions).forEach(([from, toStates]) => {
    toStates.forEach(to => {
      edges.push({
        id: `edge-${edgeId++}`,
        source: from,
        target: to,
        animated: true,
        style: { stroke: '#888' },
        type: 'smoothstep',
      })
    })
  })

  return edges
}

interface StateTransitionFlowProps {
  transitions: Record<string, string[]>
}

export function StateTransitionFlow({ transitions }: StateTransitionFlowProps) {
  const [nodes, , onNodesChange] = useNodesState(initialNodes)
  const [edges, , onEdgesChange] = useEdgesState(createEdges(transitions))

  const onInit = useCallback(() => {
    console.log('Flow initialized')
  }, [])

  return (
    <div style={{ width: '100%', height: '500px' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onInit={onInit}
        fitView
        attributionPosition="bottom-left"
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  )
} 