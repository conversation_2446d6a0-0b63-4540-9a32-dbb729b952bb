'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Loader2 } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { GoogleLoginButton } from './google-login-button'

// Login form validation schema
const loginSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address'),
  password: z.string()
    .min(1, 'Please enter password'),
})

type FormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSubmit: (data: FormData) => Promise<void>
}

export function LoginForm({ onSubmit }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const handleSubmit = async (data: FormData) => {
    try {
      console.log('[LoginForm] Form submission started', {
        data,
        timestamp: new Date().toISOString()
      })
      setIsLoading(true)
      await onSubmit(data)
      console.log('[LoginForm] Form submission successful', {
        timestamp: new Date().toISOString()
      })
    } catch (error: any) {
      // Display different error messages based on error type
      let errorMessage = 'Login failed, please try again later'

      console.error('[LoginForm] Form submission failed', {
        error,
        errorCode: error?.code,
        errorMessage: error?.message,
        timestamp: new Date().toISOString()
      })

      if (error?.code === 'INVALID_CREDENTIALS') {
        errorMessage = error.message || 'Email or password incorrect'
      } else if (error?.code === 'NETWORK_ERROR') {
        errorMessage = 'Network connection failed, please check network and try again'
      }

      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn(
      "transition-opacity duration-200",
      isClient ? "opacity-100" : "opacity-0"
    )}>
      <div className="space-y-6">
        {/* Prominent Google Login Section */}
        <div className="space-y-3">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 mb-3">
              Quick & Easy Login
            </p>
          </div>
          <GoogleLoginButton disabled={isLoading} />
        </div>
        
        {/* Alternative Email Login Section - Less Prominent */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-3 text-gray-400">
              Or use email instead
            </span>
          </div>
        </div>

        {/* Email Login Form - Less Prominent */}
        <div className="opacity-75">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-gray-600">Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Please enter email address"
                      disabled={isLoading}
                      className="text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm text-gray-600">Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Please enter password"
                      disabled={isLoading}
                      className="text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={isLoading}
              variant="outline"
              className="w-full mt-4 text-sm"
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Login with Email
            </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  )
}