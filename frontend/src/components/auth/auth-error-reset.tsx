'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, RefreshCw } from 'lucide-react'

interface AuthErrorResetProps {
  onReset: () => void
}

export function AuthErrorReset({ onReset }: AuthErrorResetProps) {
  const [isResetting, setIsResetting] = useState(false)

  const handleReset = async () => {
    setIsResetting(true)
    try {
      // 清除本地存储
      localStorage.removeItem('__AUTH_STATE_MACHINE_STATE__')
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth.user')
      
      // 调用传入的重置回调
      onReset()
      
      // 提示刷新页面
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('[AuthErrorReset] 重置过程发生错误', error)
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <Alert variant="destructive" className="mb-6">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>认证系统状态异常</AlertTitle>
      <AlertDescription className="mt-2">
        <p className="mb-3">系统当前处于错误状态，可能是由于：</p>
        <ul className="list-disc ml-5 mb-3 text-sm">
          <li>网络连接问题</li>
          <li>会话过期或无效</li>
          <li>浏览器存储数据损坏</li>
        </ul>
        <div className="flex justify-end">
          <Button 
            variant="outline" 
            size="sm"
            disabled={isResetting}
            onClick={handleReset}
            className="bg-white hover:bg-white/90"
          >
            {isResetting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            重置认证状态
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  )
} 