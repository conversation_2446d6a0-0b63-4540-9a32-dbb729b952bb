'use client'

import { useState } from 'react'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { StateTransitionRule, AuthState } from '@/types/auth-rules'

// 模拟初始数据
const initialRules: StateTransitionRule[] = [
  {
    id: '1',
    fromState: 'UNAUTHENTICATED',
    toStates: ['AUTHENTICATING', 'PUBLIC'],
    description: '未认证状态可以转换到认证中或公开状态'
  },
  {
    id: '2',
    fromState: 'AUTHENTICATING',
    toStates: ['AUTHENTICATED', 'ERROR'],
    description: '认证过程可以成功或失败'
  },
  {
    id: '3',
    fromState: 'AUTHENTICATED',
    toStates: ['UNAUTHENTICATED', 'ERROR'],
    description: '已认证状态可以登出或遇到错误'
  }
]

// 编辑规则对话框组件
const EditRuleDialog = ({
  rule,
  onSave
}: {
  rule?: StateTransitionRule
  onSave: (rule: StateTransitionRule) => void
}) => {
  const [editedRule, setEditedRule] = useState<StateTransitionRule>(
    rule || {
      id: String(Date.now()),
      fromState: '',
      toStates: [],
      description: ''
    }
  )

  const handleSave = () => {
    onSave(editedRule)
  }

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{rule ? '编辑规则' : '新增规则'}</DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <label>起始状态</label>
          <Input
            value={editedRule.fromState}
            onChange={(e) => setEditedRule({ ...editedRule, fromState: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <label>目标状态（用逗号分隔）</label>
          <Input
            value={editedRule.toStates.join(',')}
            onChange={(e) => setEditedRule({ 
              ...editedRule, 
              toStates: e.target.value.split(',').map(s => s.trim())
            })}
          />
        </div>
        <div className="space-y-2">
          <label>描述</label>
          <Input
            value={editedRule.description || ''}
            onChange={(e) => setEditedRule({ 
              ...editedRule, 
              description: e.target.value
            })}
          />
        </div>
        <Button onClick={handleSave}>保存</Button>
      </div>
    </DialogContent>
  )
}

// 状态机规则管理组件
export function StateTransitionRules() {
  const [rules, setRules] = useState<StateTransitionRule[]>(initialRules)
  const [selectedRule, setSelectedRule] = useState<StateTransitionRule | undefined>()

  const handleSaveRule = (rule: StateTransitionRule) => {
    if (selectedRule) {
      // 更新现有规则
      setRules(rules.map(r => r.id === rule.id ? rule : r))
    } else {
      // 添加新规则
      setRules([...rules, rule])
    }
    setSelectedRule(undefined)
  }

  const handleDeleteRule = (id: string) => {
    setRules(rules.filter(r => r.id !== id))
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">状态转换规则</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedRule(undefined)}>
              添加规则
            </Button>
          </DialogTrigger>
          <EditRuleDialog onSave={handleSaveRule} />
        </Dialog>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>起始状态</TableHead>
            <TableHead>目标状态</TableHead>
            <TableHead>描述</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rules.map((rule) => (
            <TableRow key={rule.id}>
              <TableCell>{rule.fromState}</TableCell>
              <TableCell>{rule.toStates.join(', ')}</TableCell>
              <TableCell>{rule.description}</TableCell>
              <TableCell className="text-right">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="mr-2"
                      onClick={() => setSelectedRule(rule)}
                    >
                      编辑
                    </Button>
                  </DialogTrigger>
                  <EditRuleDialog rule={rule} onSave={handleSaveRule} />
                </Dialog>
                <Button
                  variant="destructive"
                  onClick={() => handleDeleteRule(rule.id)}
                >
                  删除
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 