'use client'

import { useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { usePermissions } from '@/auth/hooks/simplified-permissions'
import { isLoginPath, isRegisterPath, getDashboardPath } from '@/config/simplified-routes'
import { Loader2 } from 'lucide-react'

interface RouteGuardProps {
  children: React.ReactNode
}

export function RouteGuard({ children }: RouteGuardProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const { canAccessRoute } = usePermissions()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // 如果正在加载，等待加载完成
    if (isLoading) return

    // 检查路由访问权限
    const hasAccess = canAccessRoute(pathname)

    if (!hasAccess) {
      // 如果未认证，重定向到登录页
      if (!isAuthenticated) {
        const returnUrl = encodeURIComponent(pathname)
        router.push(`/login?returnUrl=${returnUrl}`)
        return
      }

      // 如果已认证但没有权限，重定向到403页面
      router.push('/403')
      return
    }

    // 如果已认证且在登录页，重定向到仪表盘
    if (isAuthenticated && (isLoginPath(pathname) || isRegisterPath(pathname))) {
      const dashboardPath = getDashboardPath(user?.role || 'user')
      router.push(dashboardPath)
    }
  }, [pathname, isAuthenticated, isLoading, canAccessRoute, router])

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // 渲染子组件
  return <>{children}</>
}
