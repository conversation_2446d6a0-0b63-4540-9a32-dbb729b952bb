'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useAuthNavigation } from '@/auth/hooks/use-auth-navigation'

interface AuthGuardProps {
  children: React.ReactNode
  requireAdmin?: boolean
}

export function AuthGuard({ children, requireAdmin = false }: AuthGuardProps) {
  const pathname = usePathname()
  const { isAuthenticated, isLoading: authIsLoading, user } = useAuth()
  const { navigateToLogin } = useAuthNavigation()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // 如果未认证且不是加载中，重定向到登录页
  useEffect(() => {
    if (!isClient || authIsLoading) return

    if (!isAuthenticated) {
      console.log('[AuthGuard] 用户未认证，重定向到登录页', {
        pathname,
        timestamp: new Date().toISOString()
      })
      navigateToLogin()
      return
    }

    // 如果需要管理员权限但用户不是管理员
    if (requireAdmin && user?.role !== 'admin') {
      console.warn('[AuthGuard] 用户没有管理员权限', {
        pathname,
        userRole: user?.role,
        timestamp: new Date().toISOString()
      })
      navigateToLogin()
      return
    }
  }, [isClient, isAuthenticated, authIsLoading, user, pathname, requireAdmin, navigateToLogin])

  // 显示加载状态
  if (!isClient || authIsLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
      </div>
    )
  }

  // 如果已认证且权限检查通过，显示子组件
  if (isAuthenticated && (!requireAdmin || user?.role === 'admin')) {
    return <>{children}</>
  }

  // 其他情况显示加载状态
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
    </div>
  )
} 