'use client'

import { useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { protectedPaths, isPublicPath } from '@/config/routes'

// 认证状态提供者 - 只负责提供认证上下文
export function AuthProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}

// 路由保护组件 - 负责处理路由访问控制
export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    if (isLoading) return

    const isProtectedRoute = protectedPaths.some(path => pathname.startsWith(path))
    const isAuthRoute = pathname.startsWith('/auth')

    if (!isAuthenticated && isProtectedRoute) {
      router.push('/auth/login')
    } else if (isAuthenticated && isAuthRoute) {
      router.push('/')
    }
  }, [isAuthenticated, isLoading, pathname, router])

  return <>{children}</>
} 