'use client'

import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { Loader2 } from 'lucide-react'
import { useEffect, useState } from 'react'

interface LoadingWrapperProps {
  children: React.ReactNode
}

export function LoadingWrapper({ children }: LoadingWrapperProps) {
  const { isLoading } = useAuth()
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 在服务器端或组件首次挂载前，显示加载状态
  if (!isMounted) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="mt-4 text-muted-foreground">
            正在初始化...
          </p>
        </div>
      </div>
    )
  }

  // 客户端渲染后，根据实际状态显示
  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="mt-4 text-muted-foreground">
            正在验证...
          </p>
        </div>
      </div>
    )
  }

  return children
} 