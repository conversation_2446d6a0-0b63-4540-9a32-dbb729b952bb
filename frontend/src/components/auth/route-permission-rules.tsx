'use client'

import { useState } from 'react'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { RoutePermissionRule, UserRole } from '@/types/auth-rules'

// 模拟初始数据
const initialRules: RoutePermissionRule[] = [
  {
    id: '1',
    path: '/admin/*',
    roles: ['admin'],
    isPublic: false,
    redirectTo: '/admin/login',
    description: '管理员路由权限'
  },
  {
    id: '2',
    path: '/dashboard/*',
    roles: ['user', 'admin'],
    isPublic: false,
    redirectTo: '/login',
    description: '用户仪表盘权限'
  },
  {
    id: '3',
    path: '/login',
    roles: [],
    isPublic: true,
    description: '登录页面'
  }
]

// 可用的用户角色
const availableRoles: UserRole[] = ['admin', 'user', 'guest']

// 编辑规则对话框组件
const EditRuleDialog = ({
  rule,
  onSave
}: {
  rule?: RoutePermissionRule
  onSave: (rule: RoutePermissionRule) => void
}) => {
  const [editedRule, setEditedRule] = useState<RoutePermissionRule>(
    rule || {
      id: String(Date.now()),
      path: '',
      roles: [],
      isPublic: false,
      description: ''
    }
  )

  const handleRoleToggle = (role: UserRole) => {
    const newRoles = editedRule.roles.includes(role)
      ? editedRule.roles.filter(r => r !== role)
      : [...editedRule.roles, role]
    setEditedRule({ ...editedRule, roles: newRoles })
  }

  const handleSave = () => {
    onSave(editedRule)
  }

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{rule ? '编辑规则' : '新增规则'}</DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <label>路由路径</label>
          <Input
            value={editedRule.path}
            onChange={(e) => setEditedRule({ ...editedRule, path: e.target.value })}
            placeholder="/path/* 或 /specific-path"
          />
        </div>
        
        <div className="space-y-2">
          <label>允许的角色</label>
          <div className="flex flex-col space-y-2">
            {availableRoles.map(role => (
              <div key={role} className="flex items-center space-x-2">
                <Checkbox
                  id={`role-${role}`}
                  checked={editedRule.roles.includes(role)}
                  onCheckedChange={() => handleRoleToggle(role)}
                />
                <label htmlFor={`role-${role}`}>{role}</label>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is-public"
              checked={editedRule.isPublic}
              onCheckedChange={(checked) => 
                setEditedRule({ ...editedRule, isPublic: !!checked })
              }
            />
            <label htmlFor="is-public">公开访问</label>
          </div>
        </div>

        <div className="space-y-2">
          <label>重定向路径（未授权时）</label>
          <Input
            value={editedRule.redirectTo || ''}
            onChange={(e) => setEditedRule({ 
              ...editedRule, 
              redirectTo: e.target.value 
            })}
            placeholder="/login 或 /admin/login"
          />
        </div>

        <div className="space-y-2">
          <label>描述</label>
          <Input
            value={editedRule.description || ''}
            onChange={(e) => setEditedRule({ 
              ...editedRule, 
              description: e.target.value 
            })}
          />
        </div>

        <Button onClick={handleSave}>保存</Button>
      </div>
    </DialogContent>
  )
}

// 路由权限规则管理组件
export function RoutePermissionRules() {
  const [rules, setRules] = useState<RoutePermissionRule[]>(initialRules)
  const [selectedRule, setSelectedRule] = useState<RoutePermissionRule | undefined>()

  const handleSaveRule = (rule: RoutePermissionRule) => {
    if (selectedRule) {
      // 更新现有规则
      setRules(rules.map(r => r.id === rule.id ? rule : r))
    } else {
      // 添加新规则
      setRules([...rules, rule])
    }
    setSelectedRule(undefined)
  }

  const handleDeleteRule = (id: string) => {
    setRules(rules.filter(r => r.id !== id))
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">路由权限规则</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedRule(undefined)}>
              添加规则
            </Button>
          </DialogTrigger>
          <EditRuleDialog onSave={handleSaveRule} />
        </Dialog>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>路由路径</TableHead>
            <TableHead>允许的角色</TableHead>
            <TableHead>公开访问</TableHead>
            <TableHead>重定向路径</TableHead>
            <TableHead>描述</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rules.map((rule) => (
            <TableRow key={rule.id}>
              <TableCell>{rule.path}</TableCell>
              <TableCell>{rule.roles.join(', ') || '无'}</TableCell>
              <TableCell>{rule.isPublic ? '是' : '否'}</TableCell>
              <TableCell>{rule.redirectTo || '-'}</TableCell>
              <TableCell>{rule.description}</TableCell>
              <TableCell className="text-right">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="mr-2"
                      onClick={() => setSelectedRule(rule)}
                    >
                      编辑
                    </Button>
                  </DialogTrigger>
                  <EditRuleDialog rule={rule} onSave={handleSaveRule} />
                </Dialog>
                <Button
                  variant="destructive"
                  onClick={() => handleDeleteRule(rule.id)}
                >
                  删除
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 