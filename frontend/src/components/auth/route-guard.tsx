'use client'

import { useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { Loader2 } from 'lucide-react'
import { isPublicPath } from '@/config/routes'

interface RouteGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
}

export function RouteGuard({ children, requireAuth = false }: RouteGuardProps) {
  const { isAuthenticated, isLoading, error } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    console.log('[RouteGuard] 路由检查', {
      pathname,
      requireAuth,
      isAuthenticated,
      isLoading,
      error,
      timestamp: new Date().toISOString()
    })

    // 如果是公开路由，直接显示内容
    if (isPublicPath(pathname)) {
      setIsChecking(false)
      return
    }

    // 如果正在加载，等待
    if (isLoading) {
      setIsChecking(true)
      return
    }

    // 如果需要认证但未认证，重定向到登录页
    if (requireAuth && !isAuthenticated) {
      console.log('[RouteGuard] 未认证，重定向到登录页', {
        from: pathname,
        requireAuth,
        isAuthenticated,
        timestamp: new Date().toISOString()
      })
      
      const returnUrl = encodeURIComponent(pathname)
      router.push(`/login?returnUrl=${returnUrl}`)
      return
    }

    // 如果已认证且在登录页，重定向到仪表盘
    const isLoginPage = pathname === '/login' || pathname === '/register'
    if (isAuthenticated && isLoginPage) {
      console.log('[RouteGuard] 已认证，重定向到仪表盘', {
        from: pathname,
        timestamp: new Date().toISOString()
      })
      router.push('/dashboard')
      return
    }

    // 完成检查
    setIsChecking(false)
  }, [pathname, isAuthenticated, isLoading, error, requireAuth, router])

  // 如果正在检查或加载，显示加载状态
  if (isChecking || isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="mt-4 text-muted-foreground">
            正在验证...
          </p>
        </div>
      </div>
    )
  }

  // 渲染子组件
  return children
} 