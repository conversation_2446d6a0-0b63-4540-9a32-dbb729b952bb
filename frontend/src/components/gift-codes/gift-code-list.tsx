'use client'

import { useEffect, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { format } from 'date-fns'
import { Badge } from '@/components/ui/badge'
import { api } from '@/lib/api/index'
import { toast } from 'sonner'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Download, Trash2 } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface GiftCode {
  id: string
  code: string
  planType: string
  durationDays: number
  expiresAt: string
  isUsed: boolean
  usedAt: string | null
  usedBy: {
    id: string
    email: string
  } | null
  createdAt: string
  course?: {
    title: string
  }
  validDays: number
}

export function GiftCodeList() {
  const [codes, setCodes] = useState<GiftCode[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [selectedCodes, setSelectedCodes] = useState<string[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [filter, setFilter] = useState<{
    status: string;
  }>({
    status: 'all'
  });

  useEffect(() => {
    fetchCodes()
  }, [currentPage, pageSize])

  const fetchCodes = async () => {
    setLoading(true)
    try {
      const response = await api.get<{data: GiftCode[], total: number}>('/api/gift-codes', {
        params: {
          page: currentPage,
          pageSize
        }
      })
      setCodes(response.data.data)
      setTotal(response.data.total)
    } catch (error) {
      console.error('获取礼品码列表失败:', error)
      toast.error('获取礼品码列表失败')
    } finally {
      setLoading(false)
    }
  }

  const totalPages = Math.ceil(total / pageSize)

  const filteredCodes = codes.filter(code => {
    if (filter.status === 'used' && !code.isUsed) {
      return false;
    }
    if (filter.status === 'unused' && code.isUsed) {
      return false;
    }
    if (filter.status === 'expired' && (!code.expiresAt || new Date(code.expiresAt) >= new Date())) {
      return false;
    }
    
    return true;
  });

  const handleExport = async (type: 'all' | 'selected') => {
    try {
      const params: { ids?: string } = {};
      
      if (type === 'selected') {
        params.ids = selectedCodes.join(',');
      }
      
      const response = await api.get('/api/gift-codes/export', {
        params,
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `gift-codes-${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('导出失败:', error);
      toast.error('导出失败');
    }
  };

  const handleDelete = async () => {
    try {
      await api.delete('/api/gift-codes/batch', {
        data: { ids: selectedCodes }
      })
      toast.success('删除成功')
      setSelectedCodes([])
      fetchCodes()
    } catch (error) {
      console.error('删除失败:', error)
      toast.error('删除失败')
    } finally {
      setShowDeleteDialog(false)
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCodes(codes.map(code => code.id))
    } else {
      setSelectedCodes([])
    }
  }

  const handleSelectCode = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedCodes(prev => [...prev, id])
    } else {
      setSelectedCodes(prev => prev.filter(codeId => codeId !== id))
    }
  }

  if (loading) {
    return <div>加载中...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <div className="space-x-2">
          <Button
            variant="outline"
            onClick={() => handleExport('all')}
            disabled={loading}
          >
            <Download className="w-4 h-4 mr-2" />
            导出全部
          </Button>
          {selectedCodes.length > 0 && (
            <Button
              variant="outline"
              onClick={() => handleExport('selected')}
              disabled={loading}
            >
              <Download className="w-4 h-4 mr-2" />
              导出已选 ({selectedCodes.length})
            </Button>
          )}
          {selectedCodes.length > 0 && (
            <Button
              variant="destructive"
              onClick={() => setShowDeleteDialog(true)}
              disabled={loading}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              删除选中 ({selectedCodes.length})
            </Button>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <Select 
            value={filter.status} 
            onValueChange={(value) => setFilter(prev => ({ ...prev, status: value }))}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="使用状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="used">已使用</SelectItem>
              <SelectItem value="unused">未使用</SelectItem>
              <SelectItem value="expired">已过期</SelectItem>
            </SelectContent>
          </Select>

          <Select value={String(pageSize)} onValueChange={(value) => setPageSize(Number(value))}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="每页显示数量" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">每页 10 条</SelectItem>
              <SelectItem value="20">每页 20 条</SelectItem>
              <SelectItem value="50">每页 50 条</SelectItem>
              <SelectItem value="100">每页 100 条</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={selectedCodes.length === filteredCodes.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead className="w-[120px]">礼品码</TableHead>
              <TableHead>课程名称</TableHead>
              <TableHead>有效天数</TableHead>
              <TableHead>过期时间</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>使用者</TableHead>
              <TableHead>创建时间</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCodes.map((code) => (
              <TableRow key={code.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedCodes.includes(code.id)}
                    onCheckedChange={(checked) => {
                      if (typeof checked === 'boolean') {
                        handleSelectCode(code.id, checked)
                      }
                    }}
                    disabled={code.isUsed}
                  />
                </TableCell>
                <TableCell className="font-mono">{code.code}</TableCell>
                <TableCell>{code.course?.title || '未知课程'}</TableCell>
                <TableCell>{code.validDays}天</TableCell>
                <TableCell>{format(new Date(code.expiresAt), 'yyyy-MM-dd HH:mm')}</TableCell>
                <TableCell>
                  {code.isUsed ? (
                    <Badge variant="secondary">已使用</Badge>
                  ) : new Date(code.expiresAt) < new Date() ? (
                    <Badge variant="destructive">已过期</Badge>
                  ) : (
                    <Badge variant="default">未使用</Badge>
                  )}
                </TableCell>
                <TableCell>{code.usedBy?.email || '-'}</TableCell>
                <TableCell>{format(new Date(code.createdAt), 'yyyy-MM-dd HH:mm')}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          共 {total} 条记录
        </div>
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
              />
            </PaginationItem>
            {[...Array(totalPages)].map((_, i) => (
              <PaginationItem key={i + 1}>
                <PaginationLink
                  onClick={() => setCurrentPage(i + 1)}
                  isActive={currentPage === i + 1}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            )).slice(Math.max(0, currentPage - 3), Math.min(totalPages, currentPage + 2))}
            {currentPage + 2 < totalPages && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            <PaginationItem>
              <PaginationNext
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除选中的 {selectedCodes.length} 个礼品码吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>确认删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 