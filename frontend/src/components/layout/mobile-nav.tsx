'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Menu, ChevronDown, ChevronRight } from 'lucide-react';
import { Logo } from './logo';

interface NavItem {
  title: string;
  href: string;
  children?: NavItem[];
}

interface MobileNavProps {
  items: NavItem[];
}

export function MobileNav({ items }: MobileNavProps) {
  const pathname = usePathname();
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden text-gray-600 hover:text-primary"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">打开菜单</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 bg-white border-r border-border">
        <div className="p-6 border-b border-border bg-secondary/50">
          <Logo />
        </div>
        <div className="py-4">
          {items.map((item) => {
            const isActive = pathname?.startsWith(item.href);
            const isChildActive = item.children?.some(child => 
              pathname?.startsWith(child.href)
            );

            if (item.children) {
              return (
                <div key={item.href} className="px-2">
                  <details className="group">
                    <summary className={cn(
                      "flex items-center justify-between px-4 py-2 rounded-md cursor-pointer transition-colors",
                      (isActive || isChildActive)
                        ? "bg-primary text-white"
                        : "text-gray-600 hover:text-primary hover:bg-secondary"
                    )}>
                      <span className="text-sm font-medium">{item.title}</span>
                      <ChevronDown className="h-4 w-4 transition-transform group-open:rotate-180" />
                    </summary>
                    <div className="pl-4 mt-2 space-y-1">
                      {item.children.map((child) => {
                        const isChildItemActive = pathname?.startsWith(child.href);
                        return (
                          <Link
                            key={child.href}
                            href={child.href}
                            onClick={() => setOpen(false)}
                            className={cn(
                              "flex items-center gap-2 px-4 py-2 text-sm rounded-md transition-colors",
                              isChildItemActive
                                ? "bg-primary text-white"
                                : "text-gray-600 hover:text-primary hover:bg-secondary"
                            )}
                          >
                            <ChevronRight className="h-4 w-4" />
                            {child.title}
                          </Link>
                        );
                      })}
                    </div>
                  </details>
                </div>
              );
            }

            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setOpen(false)}
                className={cn(
                  "flex items-center px-6 py-2 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-white"
                    : "text-gray-600 hover:text-primary hover:bg-secondary"
                )}
              >
                {item.title}
              </Link>
            );
          })}
        </div>
      </SheetContent>
    </Sheet>
  );
} 