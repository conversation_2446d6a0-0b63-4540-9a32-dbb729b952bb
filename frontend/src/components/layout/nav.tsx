'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ChevronDown } from 'lucide-react'

interface NavItem {
  href: string
  title: string
  children?: NavItem[]
}

interface NavProps {
  items: NavItem[]
}

export function Nav({ items }: NavProps) {
  const pathname = usePathname()

  return (
    <nav className="flex gap-4">
      {items.map((item) => {
        const isActive = pathname?.startsWith(item.href)
        
        if (item.children) {
          const isChildActive = item.children.some(child => 
            pathname?.startsWith(child.href)
          )
          
          return (
            <DropdownMenu key={item.href}>
              <DropdownMenuTrigger
                className={cn(
                  "flex items-center gap-1 px-4 py-2 text-sm font-medium transition-colors rounded-lg",
                  (isActive || isChildActive)
                    ? "bg-primary text-white"
                    : "text-gray-600 hover:text-primary hover:bg-secondary"
                )}
              >
                {item.title}
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start"
                className="w-48 bg-white border-border shadow-lg rounded-lg"
              >
                {item.children.map((child) => {
                  const isChildItemActive = pathname?.startsWith(child.href)
                  return (
                    <DropdownMenuItem key={child.href} asChild>
                      <Link
                        href={child.href}
                        className={cn(
                          "w-full px-3 py-2 text-sm transition-colors rounded-md",
                          isChildItemActive 
                            ? "bg-primary text-white"
                            : "text-gray-600 hover:text-primary hover:bg-secondary"
                        )}
                      >
                        {child.title}
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )
        }

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "px-4 py-2 text-sm font-medium transition-colors rounded-lg",
              isActive
                ? "bg-primary text-white"
                : "text-gray-600 hover:text-primary hover:bg-secondary"
            )}
          >
            {item.title}
          </Link>
        )
      })}
    </nav>
  )
} 