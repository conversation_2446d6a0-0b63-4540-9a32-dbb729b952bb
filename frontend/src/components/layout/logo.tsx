import Link from 'next/link'
import { <PERSON>rk<PERSON> } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LogoProps {
  className?: string
}

export function Logo({ className }: LogoProps) {
  return (
    <Link href="/" className="flex items-center space-x-2">
      <Sparkles className={cn("h-6 w-6", className)} />
      <span className={cn("font-bold text-xl", className)}>SoulTalkAI</span>
    </Link>
  )
} 