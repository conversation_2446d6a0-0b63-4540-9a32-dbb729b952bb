'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  Bot,
  Menu,
  Gift,
  BookOpen,
  Shield,
  Tags,
  Wrench,
  CreditCard,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { useState } from 'react'

// 管理员导航项配置
const adminNavItems = [
  {
    title: '仪表盘',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: '用户管理',
    href: '/admin/users',
    icon: Users,
  },
  {
    title: '课程管理',
    href: '/admin/courses',
    icon: BookOpen,
  },
  {
    title: '建站工具',
    href: '/admin/building-tools',
    icon: Wrench,
  },
  {
    title: '分类管理',
    href: '/admin/categories',
    icon: Tags,
  },
  {
    title: '订单管理',
    href: '/admin/orders',
    icon: FileText,
  },
  {
    title: '订阅管理',
    href: '/admin/subscriptions',
    icon: CreditCard,
  },
  {
    title: 'AI服务商',
    href: '/admin/ai-providers',
    icon: Bot,
  },
  {
    title: '礼品码管理',
    href: '/admin/gift-codes',
    icon: Gift,
  },
  {
    title: '系统设置',
    href: '/admin/settings',
    icon: Settings,
  },
]

interface AdminNavProps {
  className?: string
}

export function AdminNav({ className }: AdminNavProps) {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)

  return (
    <nav className={className}>
      {/* 移动端菜单按钮 */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">打开菜单</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="pl-1 pr-0">
          <div className="px-7 py-4">
            <h2 className="text-lg font-semibold">管理后台</h2>
          </div>
          <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
            <div className="space-y-4">
              {adminNavItems.map((item) => (
                <div key={item.href} className="mr-4">
                  <Link
                    href={item.href}
                    onClick={() => setOpen(false)}
                    className={cn(
                      'group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground',
                      pathname === item.href ? 'bg-accent' : 'transparent'
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                </div>
              ))}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* 桌面端侧边栏 */}
      <div className="hidden lg:flex lg:flex-col">
        <ScrollArea className="py-6">
          <div className="px-3 py-2">
            <h2 className="mb-6 px-4 text-lg font-semibold">
              管理后台
            </h2>
            <div className="space-y-1">
              {adminNavItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground',
                    pathname === item.href ? 'bg-accent text-accent-foreground' : 'transparent'
                  )}
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              ))}
            </div>
          </div>
        </ScrollArea>
      </div>
    </nav>
  )
} 