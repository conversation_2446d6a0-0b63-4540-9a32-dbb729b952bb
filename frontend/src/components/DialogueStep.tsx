import React from 'react';
import { cn } from '@/lib/utils';

interface DialogueStepProps {
  speaker: string;
  content: string;
  className?: string;
}

export function DialogueStep({ speaker, content, className }: DialogueStepProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="font-semibold text-primary">
        {speaker}
      </div>
      <div className="text-gray-700 whitespace-pre-wrap">
        {content}
      </div>
    </div>
  );
} 