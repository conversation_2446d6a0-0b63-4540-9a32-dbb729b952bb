'use client';

import * as React from 'react';
import { format, isValid } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Locale } from 'date-fns';

interface DatePickerProps {
  date?: Date | { from: Date; to: Date };
  onSelect?: (date: Date | { from: Date; to: Date } | undefined) => void;
  placeholder?: string;
  mode?: 'single' | 'range';
  locale?: Locale;
}

export function DatePicker({
  date,
  onSelect,
  placeholder = '选择日期',
  mode = 'single',
  locale,
}: DatePickerProps) {
  const formatDate = (date: Date) => {
    if (!isValid(date)) return '';
    return format(date, 'PPP', { locale });
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-[280px] justify-start text-left font-normal',
            !date && 'text-muted-foreground'
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? (
            mode === 'range' && 'from' in date ? (
              <>
                {formatDate(date.from)}
                {' - '}
                {formatDate(date.to)}
              </>
            ) : (
              formatDate(date as Date)
            )
          ) : (
            <span>{placeholder}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode={mode}
          selected={date}
          onSelect={onSelect}
          initialFocus
          locale={locale}
        />
      </PopoverContent>
    </Popover>
  );
} 