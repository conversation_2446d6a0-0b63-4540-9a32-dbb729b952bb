import { motion } from 'framer-motion';
import { theme } from '@/styles/theme';

export const MotionDiv = motion.div;
export const MotionButton = motion.button;

export const FadeIn = ({ children, delay = 0, ...props }: any) => (
  <MotionDiv
    initial="initial"
    animate="animate"
    exit="exit"
    variants={{
      ...theme.animations.fadeIn,
      animate: {
        ...theme.animations.fadeIn.animate,
        transition: { delay, ...theme.animations.fadeIn.transition },
      },
    }}
    {...props}
  >
    {children}
  </MotionDiv>
);

export const SlideUp = ({ children, delay = 0, ...props }: any) => (
  <MotionDiv
    initial="initial"
    animate="animate"
    exit="exit"
    variants={{
      ...theme.animations.slideUp,
      animate: {
        ...theme.animations.slideUp.animate,
        transition: { delay, ...theme.animations.slideUp.transition },
      },
    }}
    {...props}
  >
    {children}
  </MotionDiv>
);

export const SlideIn = ({ children, delay = 0, ...props }: any) => (
  <MotionDiv
    initial="initial"
    animate="animate"
    exit="exit"
    variants={{
      ...theme.animations.slideIn,
      animate: {
        ...theme.animations.slideIn.animate,
        transition: { delay, ...theme.animations.slideIn.transition },
      },
    }}
    {...props}
  >
    {children}
  </MotionDiv>
);

export const Scale = ({ children, delay = 0, ...props }: any) => (
  <MotionDiv
    initial="initial"
    animate="animate"
    exit="exit"
    variants={{
      ...theme.animations.scale,
      animate: {
        ...theme.animations.scale.animate,
        transition: { delay, ...theme.animations.scale.transition },
      },
    }}
    {...props}
  >
    {children}
  </MotionDiv>
);

export const AnimatedCard = ({ children, ...props }: any) => (
  <Scale>
    <div
      className="rounded-lg bg-gradient-to-r from-violet-500 to-violet-300 p-[2px]"
      {...props}
    >
      <div className="rounded-[6px] bg-white p-4 dark:bg-gray-900">
        {children}
      </div>
    </div>
  </Scale>
);

export const AnimatedButton = ({ children, ...props }: any) => (
  <MotionButton
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className="inline-flex items-center justify-center rounded-md bg-gradient-to-r from-violet-500 to-violet-300 px-4 py-2 text-sm font-medium text-white shadow-sm hover:from-violet-600 hover:to-violet-400 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
    {...props}
  >
    {children}
  </MotionButton>
); 