'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { AiProvider, ProviderType, providerDefaults, ProviderStatus, ProviderDefaultType } from '@/types/ai';
import { createAiProvider, updateAiProvider } from '@/lib/api/admin';

// 表单验证 Schema
const formSchema = z.object({
  name: z.string().min(1, '请输入服务商名称'),
  type: z.nativeEnum(ProviderType),
  status: z.nativeEnum(ProviderStatus).default(ProviderStatus.TESTING),
  defaultType: z.nativeEnum(ProviderDefaultType).nullable().optional(),
  config: z.object({
    apiKey: z.string().min(1, '请输入 API Key'),
    apiEndpoint: z.string().optional(),
    providerConfig: z.object({
      flux: z.object({
        model: z.enum(['flux-schnell', 'flux-dev', 'wanx2.1-t2i-turbo', 'wanx2.1-t2i-plus', 'wanx2.0-t2i-turbo']).optional(),
        size: z.string().optional(),
        steps: z.number().optional(),
        guidance: z.number().optional(),
        offload: z.boolean().optional(),
        add_sampling_metadata: z.boolean().optional(),
      }).optional(),
      siliconflow: z.object({
        model: z.string().optional(),
      }).optional(),
    }).optional(),
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  provider?: AiProvider | null;
}

// 服务商显示名称
const providerNames = {
  [ProviderType.OPENAI]: 'OpenAI',
  [ProviderType.AZURE_OPENAI]: 'Azure OpenAI',
  [ProviderType.ANTHROPIC]: 'Anthropic',
  [ProviderType.MOONSHOT]: '豆包',
  [ProviderType.GEMINI]: 'Google Gemini',
  [ProviderType.VOLCENGINE]: '火山引擎',
  [ProviderType.MISTRAL]: 'Mistral AI',
  [ProviderType.DEEPSEEK]: 'Deepseek',
  [ProviderType.FLUX]: 'Flux',
  [ProviderType.SILICONFLOW]: 'SiliconFlow',
  [ProviderType.CUSTOM]: '自定义',
};

const typeLabels: Record<ProviderType, string> = {
  [ProviderType.OPENAI]: 'OpenAI',
  [ProviderType.AZURE_OPENAI]: 'Azure OpenAI',
  [ProviderType.ANTHROPIC]: 'Anthropic',
  [ProviderType.MOONSHOT]: '豆包',
  [ProviderType.GEMINI]: 'Google Gemini',
  [ProviderType.VOLCENGINE]: '火山引擎',
  [ProviderType.MISTRAL]: 'Mistral AI',
  [ProviderType.DEEPSEEK]: 'Deepseek',
  [ProviderType.FLUX]: 'Flux',
  [ProviderType.SILICONFLOW]: 'SiliconFlow',
  [ProviderType.CUSTOM]: '自定义',
};

// 模型选项配置
const modelOptions = {
  [ProviderType.FLUX]: [
    { value: 'flux-schnell', label: 'Flux Schnell' },
    { value: 'flux-dev', label: 'Flux Dev' },
    { value: 'wanx2.1-t2i-turbo', label: '通义万相2.1-Turbo' },
    { value: 'wanx2.1-t2i-plus', label: '通义万相2.1-Plus' },
    { value: 'wanx2.0-t2i-turbo', label: '通义万相2.0-Turbo' },
  ],
  [ProviderType.SILICONFLOW]: [
    { value: 'deepseek-ai/DeepSeek-R1', label: 'DeepSeek R1' },
  ],
};

export function AiProviderDialog({ open, onOpenChange, provider }: Props) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      type: ProviderType.OPENAI,
      status: ProviderStatus.TESTING,
      config: {
        apiKey: '',
        apiEndpoint: '',
      },
    },
  });

  // 当编辑模式下，填充表单数据
  useEffect(() => {
    if (provider) {
      form.reset({
        name: provider.name,
        type: provider.type,
        status: provider.status,
        config: {
          apiKey: '********', // 不显示实际的 API Key
          apiEndpoint: provider.config.apiEndpoint || '',
        },
      });
    } else {
      form.reset({
        name: '',
        type: ProviderType.OPENAI,
        status: ProviderStatus.TESTING,
        config: {
          apiKey: '',
          apiEndpoint: '',
        },
      });
    }
  }, [provider, form]);

  // 创建服务商
  const createMutation = useMutation({
    mutationFn: (data: Partial<AiProvider>) => createAiProvider(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
      toast({
        title: '添加成功',
        description: '已成功添加新的 AI 服务商',
      });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({
        title: '添加失败',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // 更新服务商
  const updateMutation = useMutation({
    mutationFn: (params: { id: string; data: Partial<AiProvider> }) =>
      updateAiProvider(params.id, params.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'ai-providers'] });
      toast({
        title: '更新成功',
        description: '已成功更新 AI 服务商配置',
      });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({
        title: '更新失败',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // 表单提交
  const onSubmit = (values: FormValues) => {
    console.log('Form submitted with values:', values);

    const data = {
      ...values,
      config: {
        apiKey: values.config.apiKey,
        apiEndpoint: values.config.apiEndpoint || providerDefaults[values.type].apiEndpoint,
        providerConfig: values.config.providerConfig || providerDefaults[values.type].providerConfig,
      },
    };

    console.log('Data to be sent:', data);

    if (provider) {
      console.log('Updating provider:', provider.id);
      updateMutation.mutate({ id: provider.id, data });
    } else {
      console.log('Creating new provider');
      createMutation.mutate(data);
    }
  };

  // 获取当前选中的服务商类型
  const selectedType = form.watch('type');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{provider ? '编辑服务商' : '添加服务商'}</DialogTitle>
          <DialogDescription>
            {provider ? '修改 AI 服务商配置信息' : '添加新的 AI 服务商，请填写以下信息'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            id="ai-provider-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>服务商名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入服务商名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>类型</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择服务商类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(providerNames).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.apiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Key</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={provider ? '留空表示不修改' : '输入 API Key'}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.apiEndpoint"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API 地址（可选）</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="可选，留空使用默认地址"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    如果需要自定义 API 地址，请在此填写
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 根据服务商类型显示对应的模型选择 */}
            {(selectedType === ProviderType.FLUX || selectedType === ProviderType.SILICONFLOW) && (
              <FormField
                control={form.control}
                name={`config.providerConfig.${selectedType}.model`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>模型</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value || (selectedType === ProviderType.SILICONFLOW ? 'deepseek-ai/DeepSeek-R1' : 'flux-schnell')}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择模型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {modelOptions[selectedType]?.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      选择要使用的模型
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Flux 特有的配置项 */}
            {selectedType === ProviderType.FLUX && (
              <>
                <FormField
                  control={form.control}
                  name="config.providerConfig.flux.size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>图片尺寸</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="格式为 width*height，如 1024*1024"
                          {...field}
                          defaultValue={field.value || '1024*1024'}
                        />
                      </FormControl>
                      <FormDescription>
                        对于通义万相模型，支持宽高范围为[512, 1440]，总像素不超过200万。常用尺寸：1024*1024、1200*800、768*1024等。
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">启用状态</FormLabel>
                    <FormDescription>
                      设置服务商是否可用
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value === ProviderStatus.ACTIVE}
                      onCheckedChange={(checked) =>
                        field.onChange(checked ? ProviderStatus.ACTIVE : ProviderStatus.INACTIVE)
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>

        <DialogFooter className="mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            取消
          </Button>
          <Button
            type="submit"
            form="ai-provider-form"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {createMutation.isPending || updateMutation.isPending
              ? '保存中...'
              : provider
              ? '更新'
              : '添加'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}