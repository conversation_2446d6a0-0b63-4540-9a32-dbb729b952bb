'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Wrench } from 'lucide-react';
import { BuildingToolService, BuildingTool } from '@/lib/services/building-tool.service';
import { CourseService } from '@/lib/services/course.service';

interface CourseToolsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  courseId: string;
  courseName: string;
}

export function CourseToolsDialog({
  open,
  onOpenChange,
  courseId,
  courseName,
}: CourseToolsDialogProps) {
  const [selectedToolIds, setSelectedToolIds] = useState<string[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // 获取所有可用工具
  const { data: allTools = [], isLoading: isLoadingAllTools } = useQuery({
    queryKey: ['all-building-tools'],
    queryFn: () => BuildingToolService.getAll(),
    enabled: open,
  });

  // 获取课程已分配的工具
  const { data: courseTools = [], isLoading: isLoadingCourseTools } = useQuery({
    queryKey: ['course-building-tools', courseId],
    queryFn: () => CourseService.getCourseTools(courseId),
    enabled: open && !!courseId,
  });

  // 更新课程工具的mutation
  const updateCourseToolsMutation = useMutation({
    mutationFn: (toolIds: string[]) => 
      CourseService.updateCourseTools(courseId, toolIds),
    onSuccess: () => {
      toast({
        title: '更新成功',
        description: '课程工具配置已更新',
      });
      queryClient.invalidateQueries({ queryKey: ['course-building-tools', courseId] });
      onOpenChange(false);
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: error instanceof Error ? error.message : '无法更新课程工具配置',
      });
    },
  });

  // 当课程工具数据加载完成时，设置选中状态
  useEffect(() => {
    if (courseTools.length > 0) {
      setSelectedToolIds(courseTools.map(tool => tool.id));
    } else {
      setSelectedToolIds([]);
    }
  }, [courseTools]);

  const handleToolToggle = (toolId: string) => {
    setSelectedToolIds(prev => 
      prev.includes(toolId)
        ? prev.filter(id => id !== toolId)
        : [...prev, toolId]
    );
  };

  const handleSave = () => {
    updateCourseToolsMutation.mutate(selectedToolIds);
  };

  const isLoading = isLoadingAllTools || isLoadingCourseTools;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            管理课程工具
          </DialogTitle>
          <DialogDescription>
            为课程 "{courseName}" 配置可用的建站工具
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-sm font-medium">选择可用工具：</div>
              {allTools.length === 0 ? (
                <div className="text-sm text-slate-500 py-4 text-center">
                  暂无可用工具，请先在建站工具管理页面创建工具
                </div>
              ) : (
                allTools.map((tool) => (
                  <div
                    key={tool.id}
                    className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-slate-50"
                  >
                    <Checkbox
                      id={tool.id}
                      checked={selectedToolIds.includes(tool.id)}
                      onCheckedChange={() => handleToolToggle(tool.id)}
                      disabled={!tool.isActive}
                    />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <label
                          htmlFor={tool.id}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {tool.title}
                        </label>
                        {!tool.isActive && (
                          <Badge variant="secondary" className="text-xs">
                            未启用
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {tool.category}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {tool.difficulty}
                        </Badge>
                      </div>
                      <p className="text-xs text-slate-600">
                        {tool.description}
                      </p>
                      <div className="text-xs text-slate-500">
                        预估时间：{tool.estimatedTime}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleSave}
            disabled={updateCourseToolsMutation.isPending}
          >
            {updateCourseToolsMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                保存中...
              </>
            ) : (
              '保存'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 