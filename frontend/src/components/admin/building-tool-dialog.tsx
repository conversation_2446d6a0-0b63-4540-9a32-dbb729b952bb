'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import type { BuildingTool } from '@/lib/services/course.service';

const buildingToolSchema = z.object({
  code: z.string().min(1, '工具代码不能为空'),
  title: z.string().min(1, '工具名称不能为空'),
  description: z.string().min(1, '工具描述不能为空'),
  icon: z.string().min(1, '图标不能为空'),
  url: z.string().min(1, '链接不能为空'),
  category: z.string().min(1, '分类不能为空'),
  difficulty: z.string().min(1, '难度不能为空'),
  estimatedTime: z.string().min(1, '预估时间不能为空'),
  isActive: z.boolean().default(true),
  sortOrder: z.number().min(0).default(0),
});

type BuildingToolFormData = z.infer<typeof buildingToolSchema>;

interface BuildingToolDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: BuildingToolFormData) => Promise<void>;
  initialData?: BuildingTool;
  mode: 'create' | 'edit';
}

const categories = [
  { value: '页面制作', label: '页面制作' },
  { value: 'SEO优化', label: 'SEO优化' },
  { value: '设计优化', label: '设计优化' },
  { value: '数据分析', label: '数据分析' },
  { value: '开发工具', label: '开发工具' },
];

const difficulties = [
  { value: '初级', label: '初级' },
  { value: '中级', label: '中级' },
  { value: '高级', label: '高级' },
];

const icons = [
  { value: 'Zap', label: '闪电 (Zap)' },
  { value: 'Wrench', label: '扳手 (Wrench)' },
];

export function BuildingToolDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode,
}: BuildingToolDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<BuildingToolFormData>({
    resolver: zodResolver(buildingToolSchema),
    defaultValues: {
      code: '',
      title: '',
      description: '',
      icon: 'Wrench',
      url: '',
      category: '页面制作',
      difficulty: '初级',
      estimatedTime: '',
      isActive: true,
      sortOrder: 0,
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        code: initialData.code,
        title: initialData.title,
        description: initialData.description,
        icon: initialData.icon,
        url: initialData.url,
        category: initialData.category,
        difficulty: initialData.difficulty,
        estimatedTime: initialData.estimatedTime,
        isActive: initialData.isActive,
        sortOrder: initialData.sortOrder,
      });
    } else {
      form.reset({
        code: '',
        title: '',
        description: '',
        icon: 'Wrench',
        url: '',
        category: '页面制作',
        difficulty: '初级',
        estimatedTime: '',
        isActive: true,
        sortOrder: 0,
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (data: BuildingToolFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to submit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
      if (!newOpen) {
        form.reset();
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? '创建建站工具' : '编辑建站工具'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>工具代码</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="例如: landing-page-builder" 
                        {...field} 
                        disabled={mode === 'edit'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>工具名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如: AI落地页生成器" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>工具描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="描述这个工具的功能和用途..."
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="icon"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>图标</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择图标" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {icons.map((icon) => (
                          <SelectItem key={icon.value} value={icon.value}>
                            {icon.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>工具链接</FormLabel>
                    <FormControl>
                      <Input placeholder="例如: /interactive-course" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分类</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择分类" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="difficulty"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>难度</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择难度" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {difficulties.map((difficulty) => (
                          <SelectItem key={difficulty.value} value={difficulty.value}>
                            {difficulty.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>预估时间</FormLabel>
                    <FormControl>
                      <Input placeholder="例如: 5分钟" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="sortOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>排序权重</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="数字越小排序越靠前"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>启用状态</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        是否启用这个工具
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? '保存中...' : mode === 'create' ? '创建' : '保存'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 