'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface MarkdownProps {
  content: string;
}

export function Markdown({ content }: MarkdownProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // 处理特殊标记
  const processedContent = content
    // 处理图片练习
    .replace(/\[practice:image[^\]]*\]([\s\S]*?)\[\/practice\]/g, (_match, content) => {
      return `\n::: practice-image\n${content.trim()}\n:::\n`;
    })
    // 处理对话练习
    .replace(/\[practice:chat[^\]]*\]([\s\S]*?)\[\/practice\]/g, (_match, content) => {
      return `\n::: practice-chat\n${content.trim()}\n:::\n`;
    })
    // 处理等待
    .replace(/\[等待([^\]]*)\]/g, '---\n**等待$1**\n---')
    // 处理对话
    .replace(/\[([^\]]+)\]:\s*/g, '**$1**: ')
    // 处理媒体标签 - 图片
    .replace(/\[media:image\s+([^\]]*)\]\s*([^\s][^\n]*)/g, (_match, attributes, url) => {
      const titleMatch = attributes.match(/title="([^"]*)"/i);
      const title = titleMatch ? titleMatch[1] : '';
      const imageUrl = url.trim();
      // 为上传图片添加API基础URL
      if (imageUrl.startsWith('/uploads/')) {
        return `![${title}](${process.env.NEXT_PUBLIC_API_URL}${imageUrl})`;
      }
      return `![${title}](${imageUrl})`;
    })
    // 处理媒体标签 - 视频
    .replace(/\[media:video\s+([^\]]*)\]\s*([^\s][^\n]*)/g, (_match, attributes, url) => {
      const titleMatch = attributes.match(/title="([^"]*)"/i);
      const title = titleMatch ? titleMatch[1] : '';
      const videoUrl = url.trim();
      // 为上传视频添加API基础URL
      if (videoUrl.startsWith('/uploads/')) {
        return `<video src="${process.env.NEXT_PUBLIC_API_URL}${videoUrl}" controls title="${title}" style="max-width:100%"></video>`;
      }
      return `<video src="${videoUrl}" controls title="${title}" style="max-width:100%"></video>`;
    })
    // 处理媒体标签 - 音频
    .replace(/\[media:audio\s+([^\]]*)\]\s*([^\s][^\n]*)/g, (_match, attributes, url) => {
      const titleMatch = attributes.match(/title="([^"]*)"/i);
      const title = titleMatch ? titleMatch[1] : '';
      const audioUrl = url.trim();
      // 为上传音频添加API基础URL
      if (audioUrl.startsWith('/uploads/')) {
        return `<audio src="${process.env.NEXT_PUBLIC_API_URL}${audioUrl}" controls title="${title}" style="width:100%"></audio>`;
      }
      return `<audio src="${audioUrl}" controls title="${title}" style="width:100%"></audio>`;
    });

  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        code(props) {
          const { node, className, children, ...rest } = props;
          const match = /language-(\w+)/.exec(className || '');
          const code = String(children).replace(/\n$/, '');

          return match ? (
            <div className="relative group">
              <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 bg-slate-800 hover:bg-slate-700"
                  onClick={() => handleCopyCode(code)}
                >
                  {copiedCode === code ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4 text-slate-400" />
                  )}
                </Button>
              </div>
              {/* @ts-ignore */}
              <SyntaxHighlighter
                style={vscDarkPlus as any}
                language={match[1]}
                PreTag="div"
                customStyle={{
                  margin: 0,
                  padding: '1rem',
                  borderRadius: '0.375rem',
                }}
              >
                {code}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code className={className} {...rest}>
              {children}
            </code>
          );
        },
        // 允许渲染 HTML 内容
        img(props) {
          const { node, ...rest } = props;
          return <img style={{ maxWidth: '100%' }} {...rest} />;
        },
        // 允许渲染 HTML 视频和音频标签
        video(props) {
          const { node, ...rest } = props;
          return <video {...rest} />;
        },
        audio(props) {
          const { node, ...rest } = props;
          return <audio {...rest} />;
        },
      }}
      rehypePlugins={[rehypeRaw]}
    >
      {processedContent}
    </ReactMarkdown>
  );
}