'use client';

import { useCallback, useRef, KeyboardEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send } from 'lucide-react';

interface ChatInputProps {
  message: string;
  onChange: (value: string) => void;
  onSend: () => void;
  sending: boolean;
}

export function ChatInput({ message, onChange, onSend, sending }: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (message.trim() && !sending) {
          onSend();
        }
      }
    },
    [message, sending, onSend]
  );

  return (
    <div className="flex items-end gap-2 p-4 bg-background border-t">
      <Textarea
        ref={textareaRef}
        value={message}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Type a message..."
        className="min-h-[60px] max-h-[200px]"
        disabled={sending}
      />
      <Button
        size="icon"
        disabled={!message.trim() || sending}
        onClick={onSend}
      >
        <Send className="h-4 w-4" />
      </Button>
    </div>
  );
} 