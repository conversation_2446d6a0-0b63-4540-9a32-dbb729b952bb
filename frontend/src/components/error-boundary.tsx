'use client'

import { Component, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
          <h2 className="text-2xl font-bold text-slate-900 mb-4">
            抱歉，页面出现了一些问题
          </h2>
          <p className="text-slate-600 mb-8">
            我们已经记录了这个错误，并将尽快修复
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-blue-600 via-indigo-500 to-violet-500 text-white hover:opacity-90"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新页面
          </Button>
        </div>
      )
    }

    return this.props.children
  }
} 