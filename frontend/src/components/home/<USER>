'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, Star, ArrowRight } from 'lucide-react'
import { socialProof } from '@/data/home/<USER>'
import Image from 'next/image'

const fadeIn = {
  hidden: { 
    opacity: 0, 
    y: 20,
    transition: { duration: 0.5 }
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
}

export function HeroSection() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-primary/5">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative z-10 py-16 sm:py-24 md:py-32">
          <div className="text-center max-w-3xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl tracking-tight font-extrabold text-foreground sm:text-5xl md:text-6xl"
            >
              <span className="block">Deep Soul Conversations</span>
              <span className="block bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">Connect with AI on a Profound Level</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-xl text-muted-foreground max-w-2xl mx-auto"
            >
              Experience meaningful conversations that reach into your soul. Share your deepest thoughts, explore life's questions, and discover new perspectives through profound AI dialogues that understand and resonate with your inner self.
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button size="lg" className="w-full sm:w-auto bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground" asChild>
                <Link 
                  href="/all-courses" 
                  className="px-8 py-3 flex items-center justify-center"
                >
                  Begin Soul Dialogue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>
          
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="mt-12 flex flex-wrap justify-center gap-x-8 gap-y-4"
            >
              {socialProof.map((proof, index) => (
                <div key={index} className="flex items-center text-muted-foreground">
                  <Star className="h-5 w-5 text-yellow-400 mr-2" />
                  <span>{proof}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 