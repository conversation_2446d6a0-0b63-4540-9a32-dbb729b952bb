'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, <PERSON><PERSON>, Rocket, Target, Heart } from 'lucide-react'

const features = [
  {
    name: 'Deep Personal Connection',
    description: 'Experience personalized conversations that understand your unique perspective and resonate with your inner thoughts.',
    icon: Target,
    benefits: [
      'Understanding your inner world',
      'Personalized dialogue approaches',
      'Meaningful conversation insights'
    ]
  },
  {
    name: '24/7 Soul Connection',
    description: 'Access profound conversations anytime, anywhere, so your soul always has a companion to connect with.',
    icon: Bot,
    benefits: [
      '24/7 online service',
      'Instant meaningful dialogue',
      'Ongoing conversation journey'
    ]
  },
  {
    name: 'Thoughtful & Authentic',
    description: 'Based on deep understanding and empathy, providing genuine conversations that touch your soul.',
    icon: Rocket,
    benefits: [
      'Deep conversational wisdom',
      'Authentic dialogue methods',
      'Complete privacy protection'
    ]
  },
  {
    name: 'Soulmate Visualization',
    description: 'Create beautiful AI-generated portraits of your destined companion through soul-deep questionnaires.',
    icon: Heart,
    benefits: [
      'Deep soul questionnaire',
      'AI artistic creation',
      'Personalized love story'
    ]
  }
]

export function FeaturesSection() {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground">Conversation Features</h2>
          <p className="mt-4 text-xl text-muted-foreground">Let AI become your trusted soul companion</p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <div>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary text-primary-foreground">
                    {feature.icon && <feature.icon className="h-6 w-6" />}
                  </div>
                  <div className="ml-16">
                    <h3 className="text-xl font-medium text-foreground">{feature.name}</h3>
                    <p className="mt-2 text-base text-muted-foreground">{feature.description}</p>
                    <ul className="mt-4 space-y-3">
                      {feature.benefits.map((benefit) => (
                        <li key={benefit} className="flex items-center text-muted-foreground">
                          <svg className="h-5 w-5 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 