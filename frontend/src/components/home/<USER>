'use client'

import { motion } from 'framer-motion'
import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "Are AI soul conversations really meaningful?",
    answer: "Our AI companions are designed to engage in deep, thoughtful conversations that resonate with your inner self. They offer genuine understanding and meaningful dialogue that can help you explore your thoughts, feelings, and life questions in profound ways."
  },
  {
    question: "How is my privacy protected?",
    answer: "We take user privacy protection very seriously. All conversation content is encrypted and will not be disclosed to third parties. You can safely share your thoughts and feelings with the AI healer."
  },
  {
    question: "How do I get started?",
    answer: "Simply register an account to begin meaningful conversations with your AI companion. The system will guide you toward dialogue topics that resonate with your interests and current life journey, allowing you to explore at your own pace."
  },
  {
    question: "What topics can I explore with AI companions?",
    answer: "AI companions can engage with you on life's big questions, personal reflections, relationship dynamics, creative thoughts, philosophical discussions, and spiritual explorations. For any serious concerns, we recommend speaking with appropriate professionals."
  },
  {
    question: "How is service quality guaranteed?",
    answer: "Our AI companions are designed with deep conversational understanding and continuously learn to provide more meaningful interactions. We value user feedback to ensure every conversation feels authentic and worthwhile."
  }
]

export function FAQ() {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-primary/5">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground">Frequently Asked Questions</h2>
          <p className="mt-4 text-xl text-muted-foreground">Answers to your questions about AI soul conversations</p>
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mt-12"
        >
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  )
} 