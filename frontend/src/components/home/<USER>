'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'

export function LandingNav() {
  const pathname = usePathname()
  const { isAuthenticated, isLoading, logout } = useAuth()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleLogout = async () => {
    try {
      await logout()
      console.log('[LandingNav] Logout successful', {
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('[LandingNav] Logout failed:', error)
    }
  }

  // Base styles
  const linkBaseStyles = "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 h-10 px-4 py-2"
  const linkActiveStyles = "bg-primary text-white hover:bg-primary/90"
  const linkInactiveStyles = "text-gray-600 hover:text-primary hover:bg-secondary"

  // Unified navigation structure
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/80 backdrop-blur-sm shadow-sm">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex-shrink-0 flex items-center">
          <Link href="/" className="flex items-center">
            <BookOpen className="mr-2 h-6 w-6 text-primary" />
            <span className="text-2xl font-bold text-primary">
              SoulTalkAI
            </span>
          </Link>
        </div>
        
        {/* Navigation links - only show when client loaded and not loading */}
        {isClient && !isLoading && (
          <div className="hidden sm:flex sm:items-center sm:space-x-4">
            <Link
              href="/all-courses"
              className={cn(
                linkBaseStyles,
                pathname?.startsWith('/all-courses') ? linkActiveStyles : linkInactiveStyles
              )}
            >
              Soul Dialogues
            </Link>
            
            <Link
              href="/soulmate-drawing"
              className={cn(
                linkBaseStyles,
                pathname?.startsWith('/soulmate-drawing') ? linkActiveStyles : linkInactiveStyles
              )}
            >
              Soulmate Drawing
            </Link>
            
            {/* Authenticated state navigation */}
            {isAuthenticated && (
              <div className="flex items-center space-x-4">
                <Link
                  href="/dashboard"
                  className={cn(
                    linkBaseStyles,
                    pathname?.startsWith('/dashboard') ? linkActiveStyles : linkInactiveStyles
                  )}
                >
                  Dashboard
                </Link>
                <button
                  onClick={handleLogout}
                  className={cn(linkBaseStyles, linkInactiveStyles)}
                >
                  Logout
                </button>
              </div>
            )}

            {/* Unauthenticated state navigation */}
            {!isAuthenticated && (
              <div className="flex items-center space-x-4">
                <Link
                  href="/login"
                  className={cn(
                    linkBaseStyles,
                    pathname === '/login' ? linkActiveStyles : linkInactiveStyles
                  )}
                >
                  Login
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  )
} 