'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Briefcase, Palette, Lightbulb } from 'lucide-react'

const scenarios = [
  {
    title: 'Inner Exploration',
    icon: Briefcase,
    description: 'Dive deep into your emotions and discover profound insights about your inner world',
    features: [
      'Exploring feelings of uncertainty',
      'Finding inner peace and clarity',
      'Discovering personal strength'
    ],
    image: '/images/scenarios/emotion.png'
  },
  {
    title: 'Connection Insights',
    icon: Palette,
    description: 'Explore the depths of human connection and understand relationship dynamics',
    features: [
      'Understanding love and intimacy',
      'Exploring family bonds and dynamics',
      'Building meaningful connections'
    ],
    image: '/images/scenarios/relationship.png'
  },
  {
    title: 'Soul Discovery',
    icon: Lightbulb,
    description: 'Embark on a journey of self-discovery and spiritual growth',
    features: [
      'Deep self-understanding',
      'Finding life purpose and meaning',
      'Discovering inner wisdom'
    ],
    image: '/images/scenarios/growth.png'
  }
]

export function ScenarioSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/5 via-background to-primary/5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-foreground">Conversation Journeys</h2>
          <p className="mt-4 text-xl text-muted-foreground">AI companion can guide you through profound life explorations</p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {scenarios.map((scenario, index) => (
            <motion.div
              key={scenario.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-card rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="inline-flex items-center justify-center p-3 bg-gradient-to-br from-primary/10 to-accent/10 rounded-xl">
                {scenario.icon && <scenario.icon className="h-6 w-6 text-primary" />}
              </div>
              <h3 className="mt-6 text-2xl font-semibold text-foreground">{scenario.title}</h3>
              <p className="mt-2 text-muted-foreground">{scenario.description}</p>
              <ul className="mt-6 space-y-4">
                {scenario.features.map((feature) => (
                  <li key={feature} className="flex items-center text-muted-foreground">
                    <svg className="h-5 w-5 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-lg">{feature}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
} 