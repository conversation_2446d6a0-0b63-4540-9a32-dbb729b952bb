'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { PopularCourses } from './popular-courses'

export function AuthContentWrapper() {
  const { isAuthenticated, isLoading } = useAuth()
  const [isVisible, setIsVisible] = useState(false)

  // 使用useEffect来处理状态变化，避免水合不匹配
  useEffect(() => {
    if (!isLoading) {
      setIsVisible(true)
    }
  }, [isLoading])

  return (
    <div
      className="transition-opacity duration-300"
      style={{ opacity: isVisible ? 1 : 0 }}
    >
      {/* 骨架屏 */}
      {!isVisible && (
        <div className="w-full py-12">
          <div className="container">
            <div className="animate-pulse space-y-4">
              <div className="h-8 w-1/3 bg-gray-200 rounded"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-3">
                    <div className="h-48 bg-gray-200 rounded"></div>
                    <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
                    <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 实际内容 */}
      <div className={!isVisible ? 'hidden' : undefined}>
        <PopularCourses />
      </div>
    </div>
  )
} 