'use client'

import { useAuth, useUserRole } from '../contexts/simplified-auth-context'
import { routePermissions, isPublicPath, isAdminPath } from '@/config/simplified-routes'

// 权限配置
const PERMISSIONS = {
  admin: ['all'],
  user: ['read', 'create', 'update'],
  guest: ['read']
} as const

export function usePermissions() {
  const { isAuthenticated } = useAuth()
  const { role } = useUserRole()

  // 检查用户是否有特定角色
  const hasRole = (requiredRole: string): boolean => {
    if (!isAuthenticated || !role) return false
    if (role === 'admin') return true // 管理员拥有所有角色权限
    return role === requiredRole
  }

  // 检查用户是否有特定权限
  const hasPermission = (permission: string): boolean => {
    if (!isAuthenticated || !role) return false

    const userPermissions = PERMISSIONS[role as keyof typeof PERMISSIONS] || []
    if (userPermissions.includes('all')) return true

    return userPermissions.includes(permission)
  }

  // 检查路由访问权限
  const canAccessRoute = (path: string): boolean => {
    // 公开路由总是可访问的
    if (isPublicPath(path)) {
      return true
    }

    // 未认证用户只能访问公开路由
    if (!isAuthenticated || !role) return false

    // 管理员可以访问所有路由
    if (role === 'admin') return true

    // 普通用户不能访问管理员路由
    if (isAdminPath(path)) return false

    // 用户可以访问用户路由
    if (role === 'user') {
      return routePermissions.user.some(route =>
        path === route || path.startsWith(`${route}/`)
      )
    }

    return false
  }

  return {
    hasRole,
    hasPermission,
    canAccessRoute,
    role,
    permissions: role ? PERMISSIONS[role as keyof typeof PERMISSIONS] : []
  }
}
