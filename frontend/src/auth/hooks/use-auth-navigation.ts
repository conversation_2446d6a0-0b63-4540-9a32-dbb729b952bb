'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '../contexts/simplified-auth-context'

export function useAuthNavigation() {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, isLoading: authIsLoading, user } = useAuth()
  const navigationInProgressRef = useRef(false)
  const navigationTimeoutRef = useRef<NodeJS.Timeout>()
  const [isLoading, setIsLoading] = useState(false)

  // 重置导航状态
  const resetNavigation = useCallback(() => {
    navigationInProgressRef.current = false
    setIsLoading(false)
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current)
      navigationTimeoutRef.current = undefined
    }
  }, [])

  // 清理函数
  useEffect(() => {
    return () => {
      resetNavigation()
    }
  }, [resetNavigation])

  // 认证后导航
  const navigateAfterAuth = useCallback(async (targetPath: string) => {
    if (navigationInProgressRef.current) {
      console.log('[useAuthNavigation] 导航正在进行中，跳过', {
        targetPath,
        timestamp: new Date().toISOString()
      })
      return
    }

    try {
      navigationInProgressRef.current = true
      setIsLoading(true)

      console.log('[useAuthNavigation] 开始导航', {
        targetPath,
        isAuthenticated,
        user: user?.role,
        timestamp: new Date().toISOString()
      })

      // 等待认证状态稳定
      if (authIsLoading) {
        console.log('[useAuthNavigation] 等待认证状态稳定', {
          timestamp: new Date().toISOString()
        })
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 只有在访问管理员页面时才检查管理员权限
      if (targetPath.startsWith('/admin') && user?.role !== 'admin') {
        console.warn('[useAuthNavigation] 用户没有管理员权限，重定向到管理员登录页', {
          userRole: user?.role,
          targetPath,
          timestamp: new Date().toISOString()
        })
        router.push('/admin/login')
        return
      }

      // 执行导航
      console.log('[useAuthNavigation] 执行导航到', {
        targetPath,
        userRole: user?.role,
        timestamp: new Date().toISOString()
      })
      router.push(targetPath)
    } catch (error) {
      console.error('[useAuthNavigation] 导航失败', {
        error,
        targetPath,
        timestamp: new Date().toISOString()
      })
      // 根据目标路径决定错误时的重定向
      if (targetPath.startsWith('/admin')) {
        router.push('/admin/login')
      } else {
        router.push('/login')
      }
    } finally {
      navigationInProgressRef.current = false
      setIsLoading(false)
    }
  }, [router, isAuthenticated, authIsLoading, user])

  // 导航到登录页
  const navigateToLogin = useCallback(() => {
    router.push('/admin/login')
  }, [router])

  return {
    navigateAfterAuth,
    navigateToLogin,
    isLoading
  }
}