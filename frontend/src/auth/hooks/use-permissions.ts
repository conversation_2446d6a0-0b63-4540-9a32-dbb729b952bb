'use client'

import { useUserRole } from './use-auth'
import { useAuth } from './use-auth'

// 定义权限配置类型
type PermissionConfig = {
  [role: string]: string[]
}

// 权限配置表
const PERMISSIONS: PermissionConfig = {
  admin: ['all'],
  user: ['read', 'create', 'update'],
  guest: ['read']
}

export function usePermissions() {
  const { role: userRole } = useUserRole()
  const { isAuthenticated } = useAuth()

  // 检查角色
  const hasRole = (role: string): boolean => {
    return userRole === role
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!isAuthenticated || !userRole) return false
    
    const userPermissions = PERMISSIONS[userRole]
    if (!userPermissions) return false

    // admin 有所有权限
    if (userPermissions.includes('all')) return true

    return userPermissions.includes(permission)
  }

  // 综合检查访问权限
  const checkAccess = (requirements: { role?: string; permission?: string; requireAll?: boolean }): boolean => {
    const { role, permission, requireAll = false } = requirements

    // 未认证直接返回 false
    if (!isAuthenticated) return false

    // 如果需要同时满足角色和权限
    if (requireAll) {
      if (role && !hasRole(role)) return false
      if (permission && !hasPermission(permission)) return false
      return true
    }

    // 只需满足其中之一
    if (role && hasRole(role)) return true
    if (permission && hasPermission(permission)) return true
    
    // 如果没有指定任何要求，但用户已认证，则允许访问
    return !role && !permission
  }

  return {
    hasRole,
    hasPermission,
    checkAccess,
    userRole,
    permissions: userRole ? PERMISSIONS[userRole] : []
  }
} 