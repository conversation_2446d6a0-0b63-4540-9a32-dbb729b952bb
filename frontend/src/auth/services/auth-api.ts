import { User } from '@/lib/services/user.service'
import { apiClient } from './api-client'
import { AuthError } from '../types/auth.types'

// 定义错误处理规则
const ERROR_HANDLING_RULES = {
  NETWORK: {
    TIMEOUT: {
      maxRetries: 3,
      retryDelay: 1000 // 1秒
    }
  }
}

interface LoginResponse {
  user: User
  token: string
}

interface RegisterResponse {
  user: User
  token: string
}

class AuthApi {
  private async handleRequest<T>(
    requestFn: () => Promise<T>,
    errorMessage: string
  ): Promise<T> {
    const { maxRetries, retryDelay } = ERROR_HANDLING_RULES.NETWORK.TIMEOUT

    let lastError: Error | null = null
    let retryCount = 0

    while (retryCount < maxRetries) {
      try {
        return await requestFn()
      } catch (error: any) {
        lastError = error

        // 如果是认证错误（401），直接抛出，不需要重试
        if (error?.response?.status === 401) {
          console.error(`[AuthApi] 认证失败`, {
            error,
            message: error?.response?.data?.message,
            timestamp: new Date().toISOString()
          })
          throw new AuthError(
            'INVALID_CREDENTIALS',
            error?.response?.data?.message || '认证失败',
            { originalError: error }
          )
        }

        // 其他错误继续重试逻辑
        console.error(`[AuthApi] ${errorMessage}`, {
          error,
          retryCount,
          maxRetries,
          timestamp: new Date().toISOString()
        })

        retryCount++
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay))
        }
      }
    }

    throw new AuthError(
      'NETWORK_ERROR',
      errorMessage,
      { originalError: lastError }
    )
  }

  async getCurrentUser(): Promise<User> {
    return this.handleRequest(
      async () => {
        const response = await apiClient.get<User>('/api/auth/me')
        return response.data
      },
      '获取当前用户信息失败'
    )
  }

  async login(credentials: { email: string; password: string }): Promise<LoginResponse> {
    return this.handleRequest(
      async () => {
        const response = await apiClient.post<LoginResponse>('/api/auth/login', credentials)
        return response.data
      },
      '登录失败'
    )
  }

  async adminLogin(credentials: { email: string; password: string }): Promise<LoginResponse> {
    return this.handleRequest(
      async () => {
        const response = await apiClient.post<LoginResponse>('/api/auth/admin/login', credentials)
        return response.data
      },
      '管理员登录失败'
    )
  }

  async logout(): Promise<void> {
    return this.handleRequest(
      async () => {
        await apiClient.post('/api/auth/logout')
      },
      '登出失败'
    )
  }

  async register(data: {
    email: string
    password: string
    name: string
    verificationCode: string
  }): Promise<LoginResponse> {
    return this.handleRequest(
      async () => {
        const response = await apiClient.post<LoginResponse>('/api/auth/register', data)
        return response.data
      },
      '注册失败'
    )
  }
}

export const authApi = new AuthApi()