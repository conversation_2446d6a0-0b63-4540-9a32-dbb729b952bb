import axios from 'axios'
import { tokenManager } from '../core/token-manager'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 获取 token
    const token = tokenManager.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 如果是 401 错误且不是刷新 token 的请求
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 清除 token
        tokenManager.clearToken()
        
        // 只有在非登录页面时才重定向
        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname
          const loginPaths = ['/login', '/admin/login']
          if (!loginPaths.some(path => currentPath.startsWith(path))) {
            window.location.href = '/login'
          }
        }
      } catch (refreshError) {
        console.error('[ApiClient] Token 清理失败:', refreshError)
      }
    }

    return Promise.reject(error)
  }
)

export { apiClient } 