import { User } from '@/lib/services/user.service'

// 认证状态定义
export type AuthStatus =
  | { type: 'UNINITIALIZED' }                    // 初始状态，未开始认证检查
  | { type: 'INITIALIZING' }                     // 正在进行认证检查
  | { type: 'PUBLIC' }                           // 公开页面，不需要认证
  | { type: 'AUTHENTICATED', user: User }         // 已认证状态
  | { type: 'UNAUTHENTICATED' }                  // 未认证状态
  | { type: 'ERROR', error: AuthError }          // 错误状态

// 认证错误类型
export type AuthErrorCode =
  | 'INITIALIZATION_FAILED'
  | 'INVALID_CREDENTIALS'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR'
  | 'INVALID_STATE'                              // 无效的状态转换
  | 'UNAUTHORIZED'                               // 无权限访问

export class AuthError extends Error {
  constructor(
    public code: AuthErrorCode,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'AuthError'
  }

  static fromError(error: unknown): AuthError {
    if (error instanceof AuthError) {
      return error
    }

    if (error instanceof Error) {
      return new AuthError(
        'UNKNOWN_ERROR',
        error.message,
        { originalError: error }
      )
    }

    return new AuthError(
      'UNKNOWN_ERROR',
      'An unexpected error occurred',
      { originalError: error }
    )
  }
}

// 登录凭证类型
export interface LoginCredentials {
  email: string
  password: string
}

// 注册数据类型
export interface RegisterData {
  email: string;
  password: string;
  name: string;
  confirmPassword: string;
}

// 认证响应类型
export interface AuthResponse {
  user: User;
} 