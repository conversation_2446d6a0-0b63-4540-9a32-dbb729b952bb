import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { AuthProvider, useAuth } from '../contexts/simplified-auth-context'
import { usePermissions } from '../hooks/simplified-permissions'
import { PermissionGate } from '../components/simplified-permission-gate'
import { authApi } from '../services/simplified-auth-api'
import { tokenManager } from '../core/simplified-token-manager'

// 模拟依赖
jest.mock('../services/simplified-auth-api')
jest.mock('../core/simplified-token-manager')

// 测试组件
function TestAuthComponent() {
  const { user, isAuthenticated, isLoading, login, logout } = useAuth()
  
  return (
    <div>
      <div data-testid="loading-state">{isLoading ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="auth-state">{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</div>
      {user && <div data-testid="user-info">{user.name}</div>}
      
      <button 
        data-testid="login-button" 
        onClick={() => login('<EMAIL>', 'password')}
      >
        Login
      </button>
      
      <button 
        data-testid="logout-button" 
        onClick={() => logout()}
      >
        Logout
      </button>
    </div>
  )
}

function TestPermissionComponent() {
  const { hasPermission, hasRole, canAccessRoute } = usePermissions()
  
  return (
    <div>
      <div data-testid="has-admin-role">{hasRole('admin') ? 'Yes' : 'No'}</div>
      <div data-testid="has-read-permission">{hasPermission('read') ? 'Yes' : 'No'}</div>
      <div data-testid="can-access-dashboard">{canAccessRoute('/dashboard') ? 'Yes' : 'No'}</div>
      
      <PermissionGate permission="read">
        <div data-testid="read-content">Read Content</div>
      </PermissionGate>
      
      <PermissionGate role="admin">
        <div data-testid="admin-content">Admin Content</div>
      </PermissionGate>
    </div>
  )
}

describe('Simplified Auth System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  describe('AuthProvider and useAuth', () => {
    test('初始状态应该是未认证和加载中', () => {
      // 模拟API返回
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(null)
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )
      
      expect(screen.getByTestId('loading-state')).toHaveTextContent('Loading')
      expect(screen.getByTestId('auth-state')).toHaveTextContent('Not Authenticated')
    })
    
    test('成功登录后应该更新状态', async () => {
      // 模拟API返回
      const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>', role: 'user' }
      ;(authApi.login as jest.Mock).mockResolvedValue({ user: mockUser })
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(null)
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )
      
      // 点击登录按钮
      fireEvent.click(screen.getByTestId('login-button'))
      
      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-state')).toHaveTextContent('Authenticated')
        expect(screen.getByTestId('user-info')).toHaveTextContent('Test User')
      })
      
      // 验证API调用
      expect(authApi.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      })
      
      // 验证token存储
      expect(tokenManager.setUser).toHaveBeenCalledWith(mockUser)
    })
    
    test('登出后应该清除状态', async () => {
      // 模拟已登录状态
      const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>', role: 'user' }
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)
      ;(authApi.logout as jest.Mock).mockResolvedValue(undefined)
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )
      
      // 等待初始化完成
      await waitFor(() => {
        expect(screen.getByTestId('auth-state')).toHaveTextContent('Authenticated')
      })
      
      // 点击登出按钮
      fireEvent.click(screen.getByTestId('logout-button'))
      
      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-state')).toHaveTextContent('Not Authenticated')
      })
      
      // 验证API调用
      expect(authApi.logout).toHaveBeenCalled()
      
      // 验证token清除
      expect(tokenManager.clear).toHaveBeenCalled()
    })
  })
  
  describe('usePermissions and PermissionGate', () => {
    test('未登录用户应该没有任何权限', async () => {
      // 模拟未登录状态
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(null)
      
      render(
        <AuthProvider>
          <TestPermissionComponent />
        </AuthProvider>
      )
      
      await waitFor(() => {
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('No')
        expect(screen.getByTestId('has-read-permission')).toHaveTextContent('No')
        expect(screen.getByTestId('can-access-dashboard')).toHaveTextContent('No')
      })
      
      // 权限内容不应该显示
      expect(screen.queryByTestId('read-content')).not.toBeInTheDocument()
      expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument()
    })
    
    test('普通用户应该有相应的权限', async () => {
      // 模拟普通用户
      const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>', role: 'user' }
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)
      
      render(
        <AuthProvider>
          <TestPermissionComponent />
        </AuthProvider>
      )
      
      await waitFor(() => {
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('No')
        expect(screen.getByTestId('has-read-permission')).toHaveTextContent('Yes')
        expect(screen.getByTestId('can-access-dashboard')).toHaveTextContent('Yes')
      })
      
      // 只有读取内容应该显示
      expect(screen.getByTestId('read-content')).toBeInTheDocument()
      expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument()
    })
    
    test('管理员应该有所有权限', async () => {
      // 模拟管理员
      const mockUser = { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'admin' }
      ;(authApi.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)
      
      render(
        <AuthProvider>
          <TestPermissionComponent />
        </AuthProvider>
      )
      
      await waitFor(() => {
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('Yes')
        expect(screen.getByTestId('has-read-permission')).toHaveTextContent('Yes')
        expect(screen.getByTestId('can-access-dashboard')).toHaveTextContent('Yes')
      })
      
      // 所有内容都应该显示
      expect(screen.getByTestId('read-content')).toBeInTheDocument()
      expect(screen.getByTestId('admin-content')).toBeInTheDocument()
    })
  })
})
