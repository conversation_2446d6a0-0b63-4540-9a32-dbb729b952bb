'use client'

import { useAuth } from '../hooks/use-auth'
import { usePermissions } from '../hooks/use-permissions'

interface PermissionGateProps {
  children: React.ReactNode
  permission?: string
  role?: string
  fallback?: React.ReactNode
  requireAll?: boolean
}

export function PermissionGate({
  children,
  permission,
  role,
  fallback,
  requireAll = false,
}: PermissionGateProps) {
  const { isAuthenticated, isLoading, isInitialized } = useAuth()
  const { checkAccess } = usePermissions()

  // 显示加载状态
  if (!isInitialized || isLoading) {
    return fallback || null
  }

  // 未认证时不显示内容
  if (!isAuthenticated) {
    return fallback || null
  }

  // 检查访问权限
  if (!checkAccess({ role, permission, requireAll })) {
    return fallback || null
  }

  return <>{children}</>
}

// 导出特定角色的权限控制组件
export function AdminOnly({ 
  children, 
  fallback 
}: Omit<PermissionGateProps, 'role'>) {
  return (
    <PermissionGate role="admin" fallback={fallback}>
      {children}
    </PermissionGate>
  )
}

export function UserOnly({ 
  children, 
  fallback 
}: Omit<PermissionGateProps, 'role'>) {
  return (
    <PermissionGate role="user" fallback={fallback}>
      {children}
    </PermissionGate>
  )
} 