'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '../hooks/use-auth'
import { AuthRules } from '../config/auth-rules'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  requireRole?: string
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireRole
}: ProtectedRouteProps) {
  const router = useRouter()
  const { isAuthenticated, user, isLoading } = useAuth()

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return <div>Loading...</div>
  }

  // 如果需要认证但未认证，重定向到登录页
  if (requireAuth && !isAuthenticated) {
    const returnUrl = encodeURIComponent(window.location.pathname)
    router.push(`/login?returnUrl=${returnUrl}`)
    return null
  }

  // 如果需要管理员权限但没有，重定向到 403 页面
  if (requireAdmin && user?.role !== 'admin') {
    router.push('/403')
    return null
  }

  // 如果需要特定角色但没有，重定向到 403 页面
  if (requireRole && user?.role !== requireRole) {
    router.push('/403')
    return null
  }

  // 如果所有检查都通过，渲染子组件
  return <>{children}</>
} 