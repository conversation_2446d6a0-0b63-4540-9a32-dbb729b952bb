'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

/**
 * TokenCleaner组件
 * 用于在登录页面加载时自动清除所有认证相关的数据
 * 这是解决无限刷新问题的最直接方法
 */
export function TokenCleaner() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    // 只在登录页面且有刷新参数时清除数据
    const isLoginPage = pathname?.includes('/login')
    const hasRefreshParam = searchParams?.has('t')

    if (isLoginPage && hasRefreshParam) {
      clearAllAuthData()
    }
  }, [pathname, searchParams])

  // 不渲染任何内容
  return null
}

/**
 * 清除所有认证相关的数据
 */
function clearAllAuthData() {
  try {
    // 清除auth_token cookie
    document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

    // 清除状态机状态
    localStorage.removeItem('__AUTH_STATE_MACHINE_STATE__')
    sessionStorage.removeItem('__AUTH_STATE_MACHINE_STATE__')

    // 清除localStorage中的用户信息
    localStorage.removeItem('auth.user')
    sessionStorage.removeItem('auth.user')

    // 清除所有可能的认证相关存储
    try {
      // 清除所有以auth开头或包含AUTH的localStorage项
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('auth') || key.includes('AUTH') || key.includes('Auth')) {
          localStorage.removeItem(key)
        }
      })

      // 清除所有以auth开头或包含AUTH的sessionStorage项
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('auth') || key.includes('AUTH') || key.includes('Auth')) {
          sessionStorage.removeItem(key)
        }
      })
    } catch (e) {
      console.error('[TokenCleaner] 清除存储项失败:', e)
    }

    console.log('[TokenCleaner] 已清除所有认证数据和状态')
  } catch (error) {
    console.error('[TokenCleaner] 清除认证数据失败:', error)
  }
}

export { clearAllAuthData }
