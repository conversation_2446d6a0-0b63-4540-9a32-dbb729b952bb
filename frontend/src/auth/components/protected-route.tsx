'use client'

import { useEffect } from 'react'
import { useAuthNavigation } from '../hooks/use-auth-navigation'
import { useAuth } from '../hooks/use-auth'
import { usePermissions } from '../hooks/use-permissions'

interface ProtectedRouteProps {
  children: React.ReactNode
  role?: string
  permission?: string
  redirectTo?: string
  fallback?: React.ReactNode
  requireAll?: boolean
}

export function ProtectedRoute({
  children,
  role,
  permission,
  redirectTo = '/login',
  fallback,
  requireAll = false,
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, isInitialized } = useAuth()
  const { checkAccess } = usePermissions()

  const {
    navigateToLogin,
    navigateToForbidden,
  } = useAuthNavigation()

  // 认证状态检查
  useEffect(() => {
    if (!isInitialized || isLoading) return

    // 未认证时重定向到登录页
    if (!isAuthenticated) {
      navigateToLogin()
      return
    }

    // 权限检查失败时重定向到禁止访问页面
    if (!checkAccess({ role, permission, requireAll })) {
      navigateToForbidden()
      return
    }
  }, [
    isInitialized,
    isLoading,
    isAuthenticated,
    role,
    permission,
    requireAll,
    checkAccess,
    navigateToLogin,
    navigateToForbidden
  ])

  // 显示加载状态
  if (!isInitialized || isLoading) {
    return fallback || (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">
            {!isInitialized ? '正在初始化...' : '正在验证...'}
          </p>
        </div>
      </div>
    )
  }

  // 验证失败返回 null
  if (!isAuthenticated || !checkAccess({ role, permission, requireAll })) {
    return null
  }

  // 验证通过显示内容
  return <>{children}</>
}

// 导出特定角色的路由保护组件
export function AdminRoute({ children, ...props }: Omit<ProtectedRouteProps, 'role'>) {
  return (
    <ProtectedRoute role="admin" {...props}>
      {children}
    </ProtectedRoute>
  )
}

export function UserRoute({ children, ...props }: Omit<ProtectedRouteProps, 'role'>) {
  return (
    <ProtectedRoute role="user" {...props}>
      {children}
    </ProtectedRoute>
  )
} 