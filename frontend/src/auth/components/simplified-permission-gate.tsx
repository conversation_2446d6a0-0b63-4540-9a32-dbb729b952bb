'use client'

import { useAuth } from '../contexts/simplified-auth-context'
import { usePermissions } from '../hooks/simplified-permissions'

interface PermissionGateProps {
  children: React.ReactNode
  permission?: string
  role?: string
  fallback?: React.ReactNode
}

export function PermissionGate({
  children,
  permission,
  role,
  fallback = null,
}: PermissionGateProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const { hasRole, hasPermission } = usePermissions()

  // 加载中显示fallback
  if (isLoading) {
    return fallback
  }

  // 未认证不显示内容
  if (!isAuthenticated) {
    return fallback
  }

  // 如果指定了角色但用户没有该角色
  if (role && !hasRole(role)) {
    return fallback
  }

  // 如果指定了权限但用户没有该权限
  if (permission && !hasPermission(permission)) {
    return fallback
  }

  // 通过所有检查，显示内容
  return <>{children}</>
}

// 管理员专用组件
export function AdminOnly({ 
  children, 
  fallback 
}: Omit<PermissionGateProps, 'role'>) {
  return (
    <PermissionGate role="admin" fallback={fallback}>
      {children}
    </PermissionGate>
  )
}

// 普通用户专用组件
export function UserOnly({ 
  children, 
  fallback 
}: Omit<PermissionGateProps, 'role'>) {
  return (
    <PermissionGate role="user" fallback={fallback}>
      {children}
    </PermissionGate>
  )
}
