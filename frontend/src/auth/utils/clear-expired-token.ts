'use client'

/**
 * 清除过期的认证token
 * 在登录页面加载时调用此函数
 */
export function clearExpiredToken(): void {
  if (typeof document === 'undefined') return

  // 检查是否在登录页面
  const isLoginPage = window.location.pathname === '/login' || 
                      window.location.pathname === '/admin/login'
  
  if (!isLoginPage) return
  
  // 删除auth_token cookie
  document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
  
  // 清除localStorage中的用户信息
  localStorage.removeItem('auth.user')
  
  console.log('[Auth] 已清除可能过期的认证信息')
}
