# 认证模块文档

## 目录结构
```
frontend/src/auth/
├── components/     # 认证相关的UI组件
│   └── login-form.tsx    # 登录表单组件
├── core/          # 核心认证功能
│   ├── token-manager.ts  # Token管理（存储、获取、刷新）
│   └── auth-core.ts      # 核心认证逻辑
├── hooks/         # React Hooks
│   ├── use-auth.ts       # 主要的认证Hook
│   └── use-auth-navigation.ts  # 认证导航控制
├── services/      # API服务
│   └── auth-api.ts       # 认证相关的API调用
├── store/         # 状态管理
│   └── auth-state-machine.ts     # 使用状态机管理认证状态
└── types/         # 类型定义
    └── auth.types.ts     # 认证相关的类型定义
```

## 核心功能

### 1. 状态管理 (auth-state-machine.ts)

使用 Zustand store 管理认证状态，提供以下状态和方法：

- 状态：
  - UNINITIALIZED: 未初始化
  - INITIALIZING: 初始化中
  - AUTHENTICATED: 已认证
  - UNAUTHENTICATED: 未认证
  - PUBLIC: 公开状态
  - ERROR: 错误状态

- 方法：
  - initialize: 初始化认证状态
  - login: 用户登录
  - adminLogin: 管理员登录
  - logout: 登出
  - register: 注册
  - resetFromError: 从错误状态重置
  - setCurrentRoute: 设置当前路由
  - getStatus: 获取当前状态
  - transition: 状态转换
  - clearPersistedState: 清理持久化状态

状态持久化：
- 使用 localStorage 存储状态
- 支持状态恢复
- 支持状态清理

状态转换规则：
- 遵循预定义的状态转换规则
- 支持状态历史记录
- 支持状态验证

### 2. 认证 Hook (use-auth.ts)

主要的认证 Hook，提供认证状态和用户信息管理：

```typescript
const {
  // 状态
  user,            // 当前用户信息
  isAuthenticated, // 是否已认证
  isLoading,       // 是否正在加载
  isInitialized,   // 是否已初始化
  error,           // 错误信息
  token,           // 访问令牌

  // 方法
  initialize,      // 初始化
  login,          // 普通用户登录
  adminLogin,      // 管理员登录
  logout,         // 登出
  register,       // 注册

  // 角色检查
  hasRole,        // 检查单个角色
  hasAnyRole,     // 检查多个角色（任一）
  hasAllRoles,    // 检查多个角色（所有）
} = useAuth();
```

### 3. 导航控制 (use-auth-navigation.ts)

处理认证相关的导航逻辑：

```typescript
const {
  isAuthenticated,
  isLoading,
  navigateAfterAuth,  // 认证后导航
  navigateToLogin,    // 导航到登录页
} = useAuthNavigation();
```

### 4. Token 管理 (token-manager.ts)

管理认证令牌的存储和刷新：

```typescript
const tokenManager = {
  setTokens: (tokens: { accessToken: string; refreshToken: string | null }) => Promise<void>;
  getAccessToken: () => Promise<string | null>;
  getRefreshToken: () => Promise<string | null>;
  removeTokens: () => Promise<void>;
  isTokenValid: () => Promise<boolean>;
  parseToken: (token: string) => { exp: number } | null;
};
```

## 类型定义

### 用户相关类型

```typescript
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

enum MembershipLevel {
  FREE = 'free',
  PREMIUM = 'premium',
}

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  membershipLevel: MembershipLevel;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 认证相关类型

```typescript
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData extends LoginCredentials {
  name: string;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}
```

## 使用示例

### 1. 登录页面

```typescript
function LoginPage() {
  const { login } = useAuth();
  const { navigateAfterAuth } = useAuthNavigation();

  const handleLogin = async (credentials: LoginCredentials) => {
    try {
      await login(credentials);
      navigateAfterAuth('/dashboard');
    } catch (error) {
      // 处理错误
    }
  };

  return <LoginForm onSubmit={handleLogin} />;
}
```

### 2. 受保护的路由

```typescript
function ProtectedPage() {
  const { isAuthenticated, isInitialized } = useAuth();
  const { navigateToLogin } = useAuthNavigation();

  useEffect(() => {
    if (isInitialized && !isAuthenticated) {
      navigateToLogin();
    }
  }, [isAuthenticated, isInitialized]);

  if (!isInitialized) return <LoadingSpinner />;
  if (!isAuthenticated) return null;

  return <ProtectedContent />;
}
```

### 3. 角色检查

```typescript
function AdminPanel() {
  const { hasRole } = useAuth();
  
  if (!hasRole(UserRole.ADMIN)) {
    return <AccessDenied />;
  }

  return <AdminDashboard />;
}
``` 

   用户访问页面
   ↓
   检查页面类型
   ↓
   如果是公开页面：
     → 直接显示内容
     → 在后台异步检查认证状态（用于导航栏显示）
   ↓
   如果是受保护页面：
     → 检查认证状态
     → 未认证则重定向到登录页
     → 已认证则显示内容
   ↓
   如果是特殊页面（如登录页）：
     → 检查认证状态
     → 已认证则重定向到仪表盘
     → 未认证则显示页面

        导航栏根据认证状态显示不同内容：
   ↓
   未认证状态：
     → 显示"登录"和"注册"按钮
     → 显示公开功能
   ↓
   已认证状态：
     → 显示用户信息
     → 显示"进入仪表盘"按钮
     → 显示"退出登录"选项

        接收请求
   ↓
   如果是 API 请求：
     → 直接放行
   ↓
   如果是公开页面：
     → 直接放行
   ↓
   如果是受保护页面：
     → 检查 token
     → 无 token 则重定向到登录页
     → 有 token 则放行
   ↓
   如果是特殊页面（登录/注册）：
     → 检查 token
     → 有 token 则重定向到仪表盘
     → 无 token 则放行


        Token 获取流程：
   → 登录成功获取 token
   → 存储在 cookie 中
   → 后续请求自动携带
   
   Token 刷新流程：
   → 检测 token 即将过期
   → 使用 refresh token 获取新 token
   → 更新存储的 token
   
   Token 清除流程：
   → 用户登出时清除
   → token 无效时清除


      认证错误：
   → token 过期
   → token 无效
   → 服务器错误
   
   处理方式：
   → 尝试刷新 token
   → 刷新失败则清除认证状态
   → 重定向到登录页


   中间件 (middleware.ts)：
这是第一道防线，在服务器端运行
在请求到达页面组件之前进行处理

AuthProvider 组件：
这是客户端的防线
处理认证状态和导航逻辑
提供认证上下文给其他组件使

useAuth
const {
  // 状态
  user,            // 用户信息
  isAuthenticated, // 是否已认证
  isLoading,       // 是否加载中
  isInitialized,   // 是否已初始化
  error,           // 错误信息
  token,           // 访问令牌

  // 认证方法
  initialize,      // 初始化认证
  login,          // 登录
  logout,         // 登出
  register,       // 注册

  // 权限检查
  hasRole,        // 角色检查
  hasAnyRole,     // 多角色检查
  hasAllRoles     // 全部角色检查
} = useAuth()

useAuthNavigation 的职责
const {
  navigateAfterAuth,    // 认证后导航
  navigateToLogin,      // 导航到登录页
  navigateToForbidden,  // 导航到禁止访问页
  currentPath          // 当前路径
} = useAuthNavigation()

登录页面加载
↓
初始化状态 (isInitialized = false, isLoading = true)
↓
检查认证状态
  → 未初始化：显示加载中
  → 初始化完成：继续检查
↓
检查是否已认证
  → 已认证：重定向到目标页面
  → 未认证：显示登录表单
↓
用户提交登录表单
↓
执行登录操作
  → 成功：状态更新，触发重定向
  → 失败：显示错误信息

  // 状态流转过程
初始状态
↓
isInitialized = false  // 未初始化
isLoading = true      // 加载中
isAuthenticated = false
↓
初始化完成
↓
isInitialized = true   // 已初始化
isLoading = false      // 加载完成
isAuthenticated = true/false  // 根据实际认证状态

// 根级别
<Providers>                    // requireAuth={false}
  <AuthProvider>              // 管理全局认证状态
    <App>
      // 公开页面
      <HomePage />            // 不需要认证
      
      // 认证页面
      <LoginPage />           // 特殊处理
      <RegisterPage />        // 特殊处理
      
      // 受保护页面
      <DashboardLayout>       // 需要认证
        <DashboardPage />     // 继承父级认证要求
      </DashboardLayout>
    </App>
  </AuthProvider>
</Providers>

<Providers>                    // 全局Provider
  <AuthProvider>              // 全局认证状态管理
    <App>
      // 公开页面
      <HomePage />            // 不需要认证
      
      // 认证页面（登录/注册）
      <LoginPage />           // 特殊处理：已登录则跳转dashboard
      <RegisterPage />        // 特殊处理：已登录则跳转dashboard
      
      // 受保护页面
      <RouteGuard requireAuth={true}>  // 路由级别保护
        <DashboardLayout>     
          <DashboardPage />   
        </DashboardLayout>
      </RouteGuard>
    </App>
  </AuthProvider>
</Providers>

用户访问页面
↓
中间件检查 (middleware.ts)
  → 检查路由类型
  → 检查 token 状态
  → 根据结果决定是否重定向
↓
AuthProvider 初始化
  → 初始化认证状态
  → 订阅状态变化
  → 提供全局认证上下文
↓
RouteGuard 保护
  → 检查认证要求
  → 验证用户权限
  → 控制内容显示/重定向

  初始状态：UNINITIALIZED
↓
开始初始化：INITIALIZING
↓
根据情况转换：
  → PUBLIC（公开页面）
  → AUTHENTICATED（已认证）
  → UNAUTHENTICATED（未认证）
  → ERROR（发生错误）

  // 公开路由
'/'                 // 首页
'/terms'           // 条款
'/privacy'         // 隐私政策
'/ip-analysis/potential-test'  // IP分析测试

// 认证路由（特殊处理）
'/login'           // 登录页
'/register'        // 注册页
'/admin/login'     // 管理员登录

// 受保护路由
'/dashboard/**'    // 仪表盘及子页面
'/ip-analysis/**'  // IP分析功能
'/settings/**'     // 设置页面
'/admin/**'        // 管理员页面

访问受保护页面
↓
检查认证状态
  → 未初始化：显示加载中
  → 初始化完成：继续检查
↓
检查是否已认证
  → 未认证：重定向到登录页
  → 已认证：继续检查
↓
检查角色权限（如果需要）
  → 权限不足：重定向到 403
  → 权限满足：显示内容

  Token 获取：
  → 登录成功时获取
  → 存储在 cookie 中
  → 设置过期时间

Token 使用：
  → API 请求自动携带
  → 路由保护检查
  → 状态验证

Token 刷新：
  → 检测即将过期
  → 使用 refresh token 获取新 token
  → 更新存储

Token 清除：
  → 登出时
  → 过期时
  → 验证失败时

  认证错误类型：
  → INITIALIZATION_FAILED  // 初始化失败
  → INVALID_CREDENTIALS   // 凭证无效
  → TOKEN_EXPIRED        // Token过期
  → REFRESH_FAILED      // 刷新失败
  → NETWORK_ERROR       // 网络错误
  → UNKNOWN_ERROR       // 未知错误

错误处理流程：
  → 捕获错误
  → 转换为统一格式
  → 更新状态机状态
  → 显示错误信息
  → 必要时重定向

  