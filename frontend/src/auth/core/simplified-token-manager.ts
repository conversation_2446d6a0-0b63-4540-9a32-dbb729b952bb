import { User } from '@/lib/services/user.service'

/**
 * 简化的Token管理器
 * 负责用户信息的存储和检索
 */
export class TokenManager {
  private readonly USER_KEY = 'auth.user'
  private readonly TOKEN_KEY = 'auth.token'
  private storage: Storage | null = null
  private static instance: TokenManager | null = null

  private constructor() {
    // 监听重新认证事件
    if (typeof window !== 'undefined') {
      window.addEventListener('auth:reauthenticate', this.handleReauthenticate)
    }
  }

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager()
    }
    return TokenManager.instance
  }

  public initialize() {
    if (typeof window !== 'undefined') {
      this.storage = window.localStorage
    }
  }

  /**
   * 获取存储的用户信息
   */
  getUser(): User | null {
    this.initialize()
    if (!this.storage) return null

    try {
      const userJson = this.storage.getItem(this.USER_KEY)
      if (!userJson) return null

      // 检查token是否存在
      const hasToken = this.getToken()
      if (!hasToken) {
        // 如果token不存在，清除本地存储的用户信息
        this.clear()
        return null
      }

      return JSON.parse(userJson)
    } catch (error) {
      console.error('解析用户信息失败:', error)
      this.clear() // 出错时清除数据
      return null
    }
  }

  /**
   * 存储用户信息
   */
  setUser(user: User): void {
    this.initialize()
    if (!this.storage) return

    this.storage.setItem(this.USER_KEY, JSON.stringify(user))
  }

  /**
   * 获取token
   */
  getToken(): string | null {
    this.initialize()
    if (!this.storage) return null
    return this.storage.getItem(this.TOKEN_KEY)
  }

  /**
   * 设置token
   */
  setToken(token: string): void {
    this.initialize()
    if (!this.storage) return
    this.storage.setItem(this.TOKEN_KEY, token)
  }

  /**
   * 清除用户信息和认证数据
   */
  clear(): void {
    this.initialize()
    if (!this.storage) return

    // 清除本地存储
    this.storage.removeItem(this.USER_KEY)
    this.storage.removeItem(this.TOKEN_KEY)

    console.log('已清除所有认证数据')
  }

  /**
   * 检查认证状态
   */
  checkAuth(): boolean {
    if (typeof window === 'undefined') return false

    // 检查token是否存在
    const token = this.getToken()
    if (!token) return false

    // 如果在登录页面，始终返回false以清除token
    const isLoginPage = window.location.pathname === '/login' ||
                      window.location.pathname === '/admin/login'
    if (isLoginPage) {
      this.clear()
      return false
    }

    return true
  }

  private handleReauthenticate = async () => {
    try {
      // 尝试重新获取用户信息
      const user = this.getUser()
      if (user) {
        console.log('[TokenManager] 重新认证成功', {
          userId: user.id,
          timestamp: new Date().toISOString()
        })
      } else {
        console.warn('[TokenManager] 重新认证失败：用户信息不存在')
      }
    } catch (error) {
      console.error('[TokenManager] 重新认证失败', error)
    }
  }

  // 在组件卸载时移除事件监听
  cleanup() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('auth:reauthenticate', this.handleReauthenticate)
    }
  }
}

// 导出单例实例
export const tokenManager = TokenManager.getInstance()
