import { User } from '@/lib/services/user.service'
import { AuthRules } from '../config/auth-rules'
import { jwtDecode } from 'jwt-decode'

interface TokenPayload {
  exp: number
  iat: number
  sub: string
  role?: string
}

class TokenManager {
  private storage: Storage
  private initialized = false
  private readonly userKey = 'auth.user'
  private readonly tokenKey = 'auth_token'

  constructor() {
    this.storage = null as any
  }

  // 初始化方法，只在客户端执行
  initialize() {
    if (typeof window !== 'undefined' && !this.initialized) {
      this.storage = window.localStorage
      this.initialized = true
      console.log('[TokenManager] 初始化完成', {
        timestamp: new Date().toISOString()
      })
    }
  }

  // 获取token
  getToken(): string | null {
    this.initialize()
    if (typeof document === 'undefined') return null

    // 首先检查 cookie
    const cookies = document.cookie.split(';')
    const authCookie = cookies.find(cookie => cookie.trim().startsWith(`${this.tokenKey}=`))
    if (authCookie) {
      return authCookie.split('=')[1]?.trim() || null
    }

    // 如果 cookie 中没有，检查 localStorage
    const storedToken = this.storage.getItem(this.tokenKey)
    if (storedToken) {
      // 如果 localStorage 中有 token，同步到 cookie
      this.setToken(storedToken)
      return storedToken
    }

    return null
  }

  // 设置token
  setToken(token: string): void {
    this.initialize()
    if (typeof document === 'undefined') return

    // 设置cookie
    const expires = new Date()
    expires.setDate(expires.getDate() + 7) // 7天过期
    document.cookie = `${this.tokenKey}=${token}; expires=${expires.toUTCString()}; path=/; SameSite=Strict`

    // 同时存储到 localStorage 作为备份
    this.storage.setItem(this.tokenKey, token)

    console.log('[TokenManager] Token已设置', {
      timestamp: new Date().toISOString()
    })
  }

  // 清除token
  clearToken(): void {
    this.initialize()
    if (typeof document === 'undefined') return

    document.cookie = `${this.tokenKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    console.log('[TokenManager] Token已清除', {
      timestamp: new Date().toISOString()
    })
  }

  // 获取用户信息
  getUser(): User | null {
    this.initialize()
    const userJson = this.storage.getItem(this.userKey)
    if (!userJson) return null
    try {
      return JSON.parse(userJson)
    } catch (error) {
      console.error('[TokenManager] 解析用户信息失败:', error)
      return null
    }
  }

  // 设置用户信息
  setUser(user: User): void {
    this.initialize()
    this.storage.setItem(this.userKey, JSON.stringify(user))
    console.log('[TokenManager] 用户信息已设置', {
      userId: user.id,
      userRole: user.role,
      timestamp: new Date().toISOString()
    })
  }

  // 检查认证 cookie 是否存在
  checkAuthCookie(): boolean {
    if (typeof document === 'undefined') return false

    const cookies = document.cookie.split(';')
    const authCookie = cookies.find(cookie => cookie.trim().startsWith(`${this.tokenKey}=`))

    // 如果 cookie 中没有，检查 localStorage
    if (!authCookie) {
      const storedToken = this.storage.getItem(this.tokenKey)
      if (storedToken) {
        // 如果 localStorage 中有 token，同步到 cookie
        this.setToken(storedToken)
        return true
      }
    }

    console.log('[TokenManager] 检查认证Cookie', {
      hasCookie: !!authCookie,
      allCookies: document.cookie,
      timestamp: new Date().toISOString()
    })

    return !!authCookie
  }

  // 清除所有认证相关数据
  clear(): void {
    this.initialize()
    this.storage.removeItem(this.userKey)
    this.clearToken()
    console.log('[TokenManager] 所有认证数据已清除', {
      timestamp: new Date().toISOString()
    })
  }
}

export const tokenManager = new TokenManager()