import { violet, mauve, blackA, green, red, blue } from '@radix-ui/colors';

export const theme = {
  colors: {
    ...violet,
    ...mauve,
    ...blackA,
    ...green,
    ...red,
    ...blue,
    background: 'hsl(270 20% 98%)',
    foreground: 'hsl(270 20% 20%)',
    card: 'hsl(270 20% 100%)',
    cardForeground: 'hsl(270 20% 20%)',
    popover: 'hsl(270 20% 100%)',
    popoverForeground: 'hsl(270 20% 20%)',
    primary: '#8B5CF6',
    primaryForeground: '#FFFFFF',
    secondary: '#F3E8FF',
    secondaryForeground: '#6B21A8',
    muted: '#F5F3FF',
    mutedForeground: '#6B21A8',
    accent: '#C084FC',
    accentForeground: '#FFFFFF',
    destructive: '#FCA5A5',
    destructiveForeground: '#7F1D1D',
    border: '#E9D5FF',
    input: '#F5F3FF',
    ring: '#8B5CF6',
  },
  animations: {
    // Framer Motion 动画预设
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.2 },
    },
    slideUp: {
      initial: { y: 20, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: 20, opacity: 0 },
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    slideIn: {
      initial: { x: -20, opacity: 0 },
      animate: { x: 0, opacity: 1 },
      exit: { x: -20, opacity: 0 },
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    scale: {
      initial: { scale: 0.95, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.95, opacity: 0 },
      transition: { duration: 0.2 },
    },
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  gradients: {
    primary: 'linear-gradient(to right, #8B5CF6, #C084FC)',
    secondary: 'linear-gradient(to right, #F3E8FF, #E9D5FF)',
    accent: 'linear-gradient(to right, #C084FC, #D8B4FE)',
  },
  blur: {
    sm: 'blur(4px)',
    DEFAULT: 'blur(8px)',
    md: 'blur(12px)',
    lg: 'blur(16px)',
    xl: 'blur(24px)',
  },
  borderRadius: {
    sm: '0.375rem',
    DEFAULT: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.5rem',
    full: '9999px',
  },
}; 