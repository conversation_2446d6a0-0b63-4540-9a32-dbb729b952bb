/**
 * 路由配置
 */
export const routes = {
  // 基础路由
  home: '/',
  login: '/login',
  register: '/register',
  dashboard: '/dashboard',
  profile: '/profile',
  membership: '/membership',
  settings: '/settings',
  
  // 管理员路由
  admin: {
    root: '/admin',
    login: '/admin/login',
    dashboard: '/admin/dashboard',
    users: '/admin/users',
    settings: '/admin/settings',
  },
  
  // 课程相关路由
  courses: {
    all: '/all-courses',
    my: '/my-courses',
    classroom: '/classroom',
  },
  
  // 错误页面
  error: {
    forbidden: '/403',
    notFound: '/404',
  }
} as const

/**
 * 路由权限配置
 */
export const routePermissions = {
  // 公开路由：无需认证即可访问
  public: [
    routes.home,
    routes.login,
    routes.register,
    routes.admin.login,
    routes.error.forbidden,
    routes.error.notFound,
    routes.courses.all,
    '/images',
  ],
  
  // 用户路由：需要用户权限
  user: [
    routes.dashboard,
    routes.profile,
    routes.membership,
    routes.settings,
    routes.courses.my,
    routes.courses.classroom,
  ],
  
  // 管理员路由：需要管理员权限
  admin: [
    routes.admin.dashboard,
    routes.admin.users,
    routes.admin.settings,
  ]
} as const

/**
 * 检查路径是否为公开路径
 */
export function isPublicPath(pathname: string): boolean {
  return (
    routePermissions.public.some(route => 
      pathname === route || 
      (route.endsWith('*') && pathname.startsWith(route.slice(0, -1)))
    ) ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.')
  )
}

/**
 * 检查路径是否为管理员路径
 */
export function isAdminPath(pathname: string): boolean {
  return (
    routePermissions.admin.some(route => 
      pathname === route || 
      pathname.startsWith(`${route}/`)
    ) ||
    (pathname.startsWith('/admin') && pathname !== routes.admin.login)
  )
}

/**
 * 检查路径是否为登录路径
 */
export function isLoginPath(pathname: string): boolean {
  return pathname === routes.login || pathname === routes.admin.login
}

/**
 * 检查路径是否为注册路径
 */
export function isRegisterPath(pathname: string): boolean {
  return pathname === routes.register
}

/**
 * 获取用户对应的仪表盘路径
 */
export function getDashboardPath(role: string): string {
  return role === 'admin' ? routes.admin.dashboard : routes.dashboard
}
