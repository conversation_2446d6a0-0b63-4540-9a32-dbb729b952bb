import { z } from 'zod';

// 环境配置验证 schema
const envSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().optional(),
  NEXT_PUBLIC_FRONTEND_URL: z.string().optional(),
  NODE_ENV: z.enum(['development', 'production']).default('development'),
});

// 验证环境变量
const validateEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('环境变量验证失败:', error);
    throw error;
  }
};

const env = validateEnv();

// 获取当前环境的基础URL
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return env.NODE_ENV === 'production'
    ? (env.NEXT_PUBLIC_FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL)
    : process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000';
};

export const ENV_CONFIG = {
  isDevelopment: env.NODE_ENV !== 'production',
  
  api: {
    development: {
      baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
    },
    production: {
      baseUrl: env.NEXT_PUBLIC_API_URL,
    }
  },
  
  frontend: {
    development: {
      baseUrl: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
    },
    production: {
      baseUrl: env.NEXT_PUBLIC_FRONTEND_URL,
    },
    current: getBaseUrl(),
  },
  
  auth: {
    cookieName: 'auth_token',
    storagePrefix: 'auth_',
    storage: {
      user: 'auth_user',
      state: 'auth_state',
    }
  },
  
  // 路由配置
  routes: {
    auth: {
      login: '/login',
      register: '/register',
      logout: '/logout',
      callback: '/auth/callback',
    },
    app: {
      home: '/',
      dashboard: '/dashboard',
      profile: '/profile',
    }
  }
} as const;

// 类型导出
export type EnvConfig = typeof ENV_CONFIG; 