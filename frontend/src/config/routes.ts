export const routes = {
  home: '/',
  login: '/login',
  register: '/register',
  dashboard: '/dashboard',
  admin: {
    root: '/admin',
    users: '/admin/users',
    aiProviders: '/admin/ai-providers',
    stats: '/admin/stats',
  },
  profile: '/profile',
  membership: '/membership',
} as const;

export const publicPaths = [
  '/',
  '/login',
  '/register',
  '/403',
  '/404',
] as const;

export const protectedPaths = [
  '/dashboard',
  '/admin',
  '/admin/:path*',
  '/profile',
  '/membership',
] as const;

export function isPublicPath(pathname: string) {
  return (
    publicPaths.includes(pathname as any) ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.')
  );
}

export function isLoginPath(pathname: string) {
  return pathname === '/login';
}

export function isRegisterPath(pathname: string) {
  return pathname === '/register';
}

export function isAdminPath(pathname: string) {
  return pathname.startsWith('/admin');
} 