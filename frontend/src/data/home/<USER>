import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react'

export const trustPoints = [
  {
    icon: Heart,
    title: 'Deep Soul Conversations'
  },
  {
    icon: <PERSON>,
    title: 'Thoughtful Understanding'
  },
  {
    icon: Sparkles,
    title: 'Meaningful Connection'
  }
]

export const socialProof = [
  'Always Available for Deep Talks',
  'Authentic Soul Conversations',
  'Complete Privacy Protection',
  'Genuinely Understanding Dialogue',
  'Meaningful Life Insights'
] 