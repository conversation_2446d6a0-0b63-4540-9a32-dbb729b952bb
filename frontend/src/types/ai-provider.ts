export interface TestResult {
  success: boolean;
  error?: string;
  details?: any;
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TESTING = 'testing',
}

export enum AiProviderType {
  OPENAI = 'openai',
  AZURE_OPENAI = 'azure_openai',
  ANTHROPIC = 'anthropic',
  MOONSHOT = 'moonshot',
  GEMINI = 'gemini',
  VOLCENGINE = 'volcengine',
  MISTRAL = 'mistral',
  CUSTOM = 'custom',
}

export interface AiProviderConfig {
  apiKey: string;
  apiEndpoint?: string;
  organizationId?: string;
  deploymentName?: string;
  modelName?: string;
}

export interface AiProvider {
  id: string;
  name: string;
  type: AiProviderType;
  status: ProviderStatus;
  config: AiProviderConfig;
  isDefault: boolean;
  requestCount: number;
  costAmount: number;
  lastTestAt?: Date;
  lastTestResult?: TestResult;
  createdAt: Date;
  updatedAt: Date;
} 