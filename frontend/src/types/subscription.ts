export interface Subscription {
    id: string
    userId: string
    planName: string
    price: number
    startDate: string
    endDate: string
    aiCallLimit: number
    aiCallUsed: number
    createdAt: string
    updatedAt: string
    isActive?: boolean
  }
  
  export interface SubscriptionHistory {
    items: Subscription[]
    total: number
  }
  
  export interface SubscriptionState {
    subscription: Subscription | null
    history: Subscription[]
    loading: boolean
    error: string | null
  }
  
  export interface UseSubscriptionReturn extends SubscriptionState {
    reload: () => Promise<void>
    formatHistory: (items: Subscription[]) => Subscription[]
  }


export interface Plan {
  id: 'MONTHLY' | 'QUARTERLY' | 'SEMIANNUAL'
  name: string
  price: number
  period: string
  description: string
  features: string[]
  buttonText: string
  recommended?: boolean
}

export interface PaymentResponse {
  orderNo: string
  payment: {
    appId: string
    timeStamp: string
    nonceStr: string
    package: string
    signType: string
    paySign: string
  }
}

export interface OrderStatus {
  paid: boolean
  expired: boolean
  orderNo: string
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'REFUNDED' | 'EXPIRED'
  createdAt: string
  updatedAt: string
}

export type MembershipType = 'MONTHLY' | 'QUARTERLY' | 'SEMIANNUAL'


export interface CreateOrderParams {
  membershipType: MembershipType
}