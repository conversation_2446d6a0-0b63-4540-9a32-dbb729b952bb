import { User, UserRole, MembershipLevel } from './user';

// 成功响应直接返回数据，不需要包装
export type ApiResponse<T> = T;

// 错误响应结构
export interface ApiError {
  message: string;
  error: string;
  statusCode: number;
  details?: Record<string, any>;
}

// 认证相关接口
export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto extends LoginDto {
  name: string;
  avatar?: string;
}

// 认证响应数据
export interface AuthResponse {
  token: string;
  user: User;
}

// API 认证响应
export type ApiAuthResponse = ApiResponse<AuthResponse>;

// 用户相关接口
export interface UpdateUserDto {
  email?: string;
  name?: string;
  avatar?: string;
  role?: UserRole;
  membershipLevel?: MembershipLevel;
  isActive?: boolean;
} 