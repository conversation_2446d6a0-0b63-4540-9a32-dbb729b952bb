declare module 'react-quill' {
  import React from 'react';

  export interface ReactQuillProps {
    id?: string;
    className?: string;
    theme?: string;
    value?: string;
    defaultValue?: string;
    placeholder?: string;
    readOnly?: boolean;
    modules?: any;
    formats?: string[];
    style?: React.CSSProperties;
    onChange?: (content: string) => void;
  }

  const ReactQuill: React.ForwardRefExoticComponent<ReactQuillProps>;
  export default ReactQuill;
} 