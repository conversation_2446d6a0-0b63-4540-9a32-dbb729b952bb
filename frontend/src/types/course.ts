export interface CourseContent {
  title: string;
  sections: CourseSection[];
}

export interface CourseSection {
  title: string;
  steps: CourseStep[];
}

export interface CourseMedia {
  type: 'video' | 'image' | 'audio';
  url: string;
  title?: string;
  description?: string;
  poster?: string;
  width?: number;
  height?: number;
}

export interface AIPractice {
  type: 'chat' | 'image';
  mode: string;
  character?: string;
  temperature?: number;
  max_tokens?: number;
  system_prompt?: string;
  model?: string;
  size?: string;
  style?: string;
  quality?: string;
  initial_prompt?: string;
}

export type CourseStep = {
  type: 'dialogue' | 'media' | 'wait' | 'ai_practice' | 'image_practice';
  speaker?: string;
  content?: string;
  media?: {
    type: 'image' | 'video' | 'audio';
    url: string;
    title?: string;
  };
  practice?: {
    type: string;
    mode: string;
    character?: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
    model?: string;
  };
  image_practice?: {
    mode: string;
    size?: string;
  };
}; 