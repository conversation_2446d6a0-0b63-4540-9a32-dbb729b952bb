export enum ProviderType {
  OPENAI = 'openai',
  AZURE_OPENAI = 'azure_openai',
  ANTHROPIC = 'anthropic',
  MOONSHOT = 'moonshot',
  GEMINI = 'gemini',
  VOLCENGINE = 'volcengine',
  MISTRAL = 'mistral',
  DEEPSEEK = 'deepseek',
  FLUX = 'flux',
  SILICONFLOW = 'siliconflow',
  CUSTOM = 'custom',
}

export enum ProviderDefaultType {
  TEXT = 'text',
  IMAGE = 'image',
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TESTING = 'testing',
}

export interface ProviderConfig {
  apiKey: string;
  apiEndpoint?: string;
  modelName?: string;
  organizationId?: string;
  deploymentName?: string;
  providerConfig?: {
    flux?: {
      model?: 'flux-schnell' | 'flux-dev' | 'wanx2.1-t2i-turbo' | 'wanx2.1-t2i-plus' | 'wanx2.0-t2i-turbo';
      size?: '512*1024' | '768*512' | '768*1024' | '1024*576' | '576*1024' | '1024*1024' | string;
      steps?: number;
      guidance?: number;
      offload?: boolean;
      add_sampling_metadata?: boolean;
    };
    siliconflow?: {
      model?: string;
      apiEndpoint?: string;
    };
  };
}

export interface AiProvider {
  id: string;
  name: string;
  type: ProviderType;
  status: ProviderStatus;
  config: ProviderConfig;
  defaultType: ProviderDefaultType | null;
  createdAt: string;
  updatedAt: string;
  requestCount: number;
  costAmount: number;
  lastTestAt?: string;
  lastTestResult?: {
    success: boolean;
    details?: string;
  };
}

export interface FluxConfig extends ProviderConfig {
  model?: 'flux-schnell' | 'flux-dev' | 'wanx2.1-t2i-turbo' | 'wanx2.1-t2i-plus' | 'wanx2.0-t2i-turbo';
  size?: '512*1024' | '768*512' | '768*1024' | '1024*576' | '576*1024' | '1024*1024' | string;
  steps?: number;
  guidance?: number;
  offload?: boolean;
  add_sampling_metadata?: boolean;
}

export interface ImageGenerateParams {
  prompt: string;
  negativePrompt?: string;
  model?: 'flux-schnell' | 'flux-dev' | 'wanx2.1-t2i-turbo' | 'wanx2.1-t2i-plus' | 'wanx2.0-t2i-turbo';
  size?: '512*1024' | '768*512' | '768*1024' | '1024*576' | '576*1024' | '1024*1024' | string;
  steps?: number;
  seed?: number;
  count?: number;
  guidance?: number;
  offload?: boolean;
  add_sampling_metadata?: boolean;
  prompt_extend?: boolean;
  watermark?: boolean;
}

export interface AiSceneGenerateParams {
  theme: string;
  sceneCount: number;
  difficulty: 'easy' | 'medium' | 'hard';
  ageGroup: string;
  description: string;
  keyWords?: string;
  subjectKnowledge?: string;
  vocabularyLevel: string;
}

// 服务商默认配置
export const providerDefaults: Record<ProviderType, Partial<ProviderConfig>> = {
  [ProviderType.OPENAI]: {
    apiEndpoint: 'https://api.openai.com/v1',
  },
  [ProviderType.AZURE_OPENAI]: {
    apiEndpoint: 'https://{resource}.openai.azure.com',
  },
  [ProviderType.ANTHROPIC]: {
    apiEndpoint: 'https://api.anthropic.com/v1',
  },
  [ProviderType.MOONSHOT]: {
    apiEndpoint: 'https://api.moonshot.cn/v1',
  },
  [ProviderType.GEMINI]: {
    apiEndpoint: 'https://generativelanguage.googleapis.com/v1',
  },
  [ProviderType.VOLCENGINE]: {
    apiEndpoint: 'https://open.volcengineapi.com',
  },
  [ProviderType.MISTRAL]: {
    apiEndpoint: 'https://api.mistral.ai/v1',
  },
  [ProviderType.DEEPSEEK]: {
    apiEndpoint: 'https://api.deepseek.com/v1',
  },
  [ProviderType.FLUX]: {
    apiEndpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis',
    providerConfig: {
      flux: {
        model: 'wanx2.1-t2i-plus',
        size: '1024*1024',
        steps: 4,
        guidance: 3.5,
        offload: false,
        add_sampling_metadata: true,
      }
    }
  },
  [ProviderType.SILICONFLOW]: {
    apiEndpoint: 'https://api.siliconflow.cn/v1',
    providerConfig: {
      siliconflow: {
        model: 'deepseek-ai/DeepSeek-R1',
      }
    }
  },
  [ProviderType.CUSTOM]: {
    apiEndpoint: '',
  },
};