import { toast } from '@/components/ui/use-toast';

export interface CourseAccessError {
  reason?: 'free_course' | 'purchased' | 'subscription' | 'no_access';
  message?: string;
  requiresSubscription?: boolean;
}

export interface CourseAccessHandlerOptions {
  showToast?: boolean;
  redirectToSubscription?: boolean;
  redirectToPurchase?: boolean;
  courseId?: string;
  courseName?: string;
}

export class CourseAccessHandler {
  static handleAccessError(
    error: any,
    options: CourseAccessHandlerOptions = {}
  ): {
    shouldRedirectToSubscription: boolean;
    shouldRedirectToPurchase: boolean;
    errorMessage: string;
  } {
    const {
      showToast = true,
      redirectToSubscription = false,
      redirectToPurchase = false,
      courseId,
      courseName
    } = options;

    // Extract error information
    const errorData: CourseAccessError = error?.response?.data || {};
    const { reason, message, requiresSubscription } = errorData;

    let errorMessage = '';
    let shouldRedirectToSubscription = false;
    let shouldRedirectToPurchase = false;

    // Handle different access scenarios
    switch (reason) {
      case 'no_access':
        if (requiresSubscription) {
          errorMessage = 'This course requires an active subscription or purchase to access.';
          shouldRedirectToSubscription = redirectToSubscription;
          shouldRedirectToPurchase = redirectToPurchase;
        } else {
          errorMessage = 'You need to purchase this course to access it.';
          shouldRedirectToPurchase = redirectToPurchase;
        }
        break;

      case 'free_course':
        errorMessage = 'This free course should be accessible. Please try again.';
        break;

      case 'purchased':
        errorMessage = 'You already have access to this course.';
        break;

      case 'subscription':
        errorMessage = 'You have access to this course through your subscription.';
        break;

      default:
        errorMessage = message || 'Unable to access this course. Please check your subscription or purchase status.';
        shouldRedirectToSubscription = redirectToSubscription && requiresSubscription;
        shouldRedirectToPurchase = redirectToPurchase && !requiresSubscription;
    }

    // Show toast notification if requested
    if (showToast) {
      toast({
        variant: 'destructive',
        title: 'Course Access Required',
        description: errorMessage
      });
    }

    return {
      shouldRedirectToSubscription,
      shouldRedirectToPurchase,
      errorMessage
    };
  }

  static getAccessStatusDisplay(accessReason?: string): {
    label: string;
    color: string;
    description: string;
  } {
    switch (accessReason) {
      case 'free_course':
        return {
          label: 'Free Access',
          color: 'text-green-600 bg-green-100',
          description: 'This course is free for everyone'
        };

      case 'purchased':
        return {
          label: 'Purchased',
          color: 'text-blue-600 bg-blue-100',
          description: 'You own this course permanently'
        };

      case 'subscription':
        return {
          label: 'Subscription Access',
          color: 'text-purple-600 bg-purple-100',
          description: 'Access through your active subscription'
        };

      case 'no_access':
      default:
        return {
          label: 'Purchase Required',
          color: 'text-gray-600 bg-gray-100',
          description: 'Purchase or subscribe to access this course'
        };
    }
  }

  static buildSubscriptionRedirectUrl(courseId?: string): string {
    const baseUrl = '/subscription/plans';
    if (courseId) {
      return `${baseUrl}?courseId=${courseId}&source=course_access`;
    }
    return baseUrl;
  }

  static buildPurchaseRedirectUrl(courseId: string): string {
    return `/all-courses/purchase/${courseId}?source=course_access`;
  }
}