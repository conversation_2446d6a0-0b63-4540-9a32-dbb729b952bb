/**
 * 确保值为 Date 类型
 */
export const ensureDate = (value: string | Date | undefined): Date | undefined => {
  if (!value) return undefined;
  if (value instanceof Date) return value;
  const date = new Date(value);
  return isNaN(date.getTime()) ? undefined : date;
};

/**
 * 检查是否为有效的 Date 对象
 */
export const isValidDate = (value: any): value is Date => {
  return value instanceof Date && !isNaN(value.getTime());
};

/**
 * 解析日期字符串或 Date 对象
 */
export const parseDate = (value: string | Date | undefined): Date | undefined => {
  try {
    if (!value) return undefined;
    const date = value instanceof Date ? value : new Date(value);
    return isValidDate(date) ? date : undefined;
  } catch {
    console.warn('Invalid date value:', value);
    return undefined;
  }
};

/**
 * 格式化日期为 ISO 字符串
 */
export const formatDateToISO = (date: Date | undefined): string | undefined => {
  return date?.toISOString();
}; 