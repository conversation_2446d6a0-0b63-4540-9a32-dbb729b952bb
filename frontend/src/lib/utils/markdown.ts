import { CourseStep } from '@/types/course';

// Get backend API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export function parseMarkdownToSteps(markdown: string): CourseStep[] {
  console.log('Starting to parse course content:', markdown);
  const steps: CourseStep[] = [];
  const lines = markdown.split('\n');
  let currentContent = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Skip empty lines
    if (!line) continue;

    // Handle image practice
    if (line.startsWith('[practice:image')) {
      console.log('Found image practice start');
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }

      let practiceContent = line;
      let practiceEndFound = false;
      
      while (i < lines.length - 1) {
        i++;
        practiceContent += '\n' + lines[i];
        if (lines[i].includes('[/practice]')) {
          practiceEndFound = true;
          break;
        }
      }

      if (practiceEndFound) {
        console.log('Complete image practice content:', practiceContent);
        steps.push(parseImagePractice(practiceContent));
      }
      continue;
    }

    // Handle chat practice
    if (line.startsWith('[practice:chat')) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }

      let practiceContent = line;
      let practiceEndFound = false;
      
      while (i < lines.length - 1) {
        i++;
        practiceContent += '\n' + lines[i];
        if (lines[i].includes('[/practice]')) {
          practiceEndFound = true;
          break;
        }
      }

      if (practiceEndFound) {
        steps.push(parseChatPractice(practiceContent));
      }
      continue;
    }

    // Handle wait
    if (line.startsWith('[wait') || line.startsWith('[等待')) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }
      steps.push({
        type: 'wait',
        content: line.replace(/\[(wait|等待)([^\]]*)\]/, '$2').trim()
      });
      continue;
    }

    // Handle dialogue
    if (line.startsWith('[') && (line.includes(']:') || line.includes(':'))) {
      if (currentContent) {
        steps.push(parseDialogue(currentContent));
        currentContent = '';
      }
      currentContent = line;
      continue;
    }

    currentContent += (currentContent ? '\n' : '') + line;
  }

  if (currentContent) {
    steps.push(parseDialogue(currentContent));
  }

  console.log('Parsing completed, generated steps count:', steps.length);
  return steps;
}

function parseDialogue(content: string): CourseStep {
  // Try to match [speaker:xxx] format
  const speakerMatch = content.match(/\[([^:]+):([^\]]+)\]/);
  if (speakerMatch) {
    const speaker = speakerMatch[2].trim();
    const contentText = content.replace(/\[[^\]]+\]\n?/, '').trim();
    return {
      type: 'dialogue',
      speaker: speaker === '系统' ? 'System' : speaker === '导师' ? 'AI Assistant' : speaker,
      content: contentText
    };
  }

  // Try to match [speaker]: content format (backward compatibility)
  const oldFormatMatch = content.match(/\[([^\]]+)\]:\s*([\s\S]*)/);
  if (oldFormatMatch) {
    return {
      type: 'dialogue',
      speaker: oldFormatMatch[1] === '系统' ? 'System' : oldFormatMatch[1] === '导师' ? 'AI Assistant' : oldFormatMatch[1],
      content: oldFormatMatch[2].trim()
    };
  }

  // Default handling
  return {
    type: 'dialogue',
    speaker: 'AI Assistant',
    content: content.trim()
  };
}

function parseChatPractice(content: string): CourseStep {
  const modeMatch = content.match(/mode="([^"]+)"/);
  const practiceContent = content
    .replace(/\[practice:chat[^\]]*\]/, '')
    .replace(/\[\/practice\]/, '')
    .trim();

  return {
    type: 'ai_practice',
    content: practiceContent,
    practice: {
      type: 'chat',
      mode: modeMatch ? modeMatch[1] : 'interactive'
    }
  };
}

function parseImagePractice(content: string): CourseStep {
  console.log('Parsing image practice content:', content);
  
  const modeMatch = content.match(/mode="([^"]+)"/);
  const sizeMatch = content.match(/size="([^"]+)"/);
  
  const practiceContent = content
    .replace(/\[practice:image[^\]]*\]/, '')
    .replace(/\[\/practice\]/, '')
    .trim();

  const result: CourseStep = {
    type: 'image_practice',
    content: practiceContent,
    image_practice: {
      mode: modeMatch ? modeMatch[1] : 'create',
      size: sizeMatch ? sizeMatch[1] : '1024x1024'
    }
  };

  console.log('Parsed image practice step:', result);
  return result;
} 