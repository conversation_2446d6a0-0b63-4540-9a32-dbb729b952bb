import { api } from './index';
import { User } from '@/lib/services/user.service';
import { AiProvider } from '@/types/ai';
import { transformProviderToDTO, transformDTOToProvider } from '../transforms/ai-provider';

export async function fetchDashboardStats() {
  const response = await api.get('/api/admin/stats');
  return response.data;
}

export async function fetchUsers() {
  const response = await api.get<User[]>('/api/admin/users');
  return response.data;
}

export async function updateUser(id: string, data: Partial<User>) {
  const response = await api.put<User>(`/api/admin/users/${id}`, data);
  return response.data;
}

export async function deleteUser(id: string) {
  const response = await api.delete(`/api/admin/users/${id}`);
  return response.data;
}

export async function fetchAiProviders() {
  const response = await api.get<any[]>('/api/admin/ai-providers');
  return response.data.map(transformDTOToProvider);
}

export async function createAiProvider(data: Partial<AiProvider>) {
  console.log('createAiProvider called with:', data);
  const dto = transformProviderToDTO(data);
  console.log('sending DTO to backend:', dto);
  try {
    const response = await api.post<any>('/api/admin/ai-providers', dto);
    console.log('createAiProvider response:', response.data);
    return transformDTOToProvider(response.data);
  } catch (error) {
    console.error('createAiProvider error:', error);
    throw error;
  }
}

export async function updateAiProvider(id: string, data: Partial<AiProvider>) {
  console.log('updateAiProvider called with:', { id, data });
  const dto = transformProviderToDTO({ ...data, id });
  console.log('sending DTO to backend:', dto);
  try {
    const response = await api.put<any>(`/api/admin/ai-providers/${id}`, dto);
    console.log('updateAiProvider response:', response.data);
    return transformDTOToProvider(response.data);
  } catch (error) {
    console.error('updateAiProvider error:', error);
    throw error;
  }
}

export async function deleteAiProvider(id: string) {
  await api.delete(`/api/admin/ai-providers/${id}`);
} 