import { api } from '../api';
import { AxiosError } from 'axios';

export interface CourseGenerationParams {
  title: string;
  rawMaterial: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  targetAudience: string;
  sectionCount?: number;
  interactionCount?: number;
  includeExercises?: boolean;
  tone?: 'formal' | 'casual';
}

export interface RegenerateSectionParams {
  sectionTitle: string;
  outline: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  interactionCount?: number;
  includeExercises?: boolean;
  selectedContent?: string;
}

export async function generateCourse(params: CourseGenerationParams): Promise<{ content: string }> {
  try {
    const { data } = await api.post('/api/course-generator/generate', params);
    return data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(error.response?.data?.message || '生成课程失败');
    }
    throw error;
  }
}

export async function regenerateSection(params: RegenerateSectionParams): Promise<{ content: string }> {
  try {
    const { data } = await api.post('/api/course-generator/regenerate-section', params);
    return data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(error.response?.data?.message || '重新生成小节失败');
    }
    throw error;
  }
} 