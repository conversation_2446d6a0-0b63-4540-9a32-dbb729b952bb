import axios from 'axios';
import { ENV_CONFIG } from '@/config/environment';

const envType = ENV_CONFIG.isDevelopment ? 'development' : 'production';

// 创建 axios 实例
export const api = axios.create({
  baseURL: ENV_CONFIG.api[envType].baseUrl,
  timeout: 180000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// 请求拦截器
api.interceptors.request.use((config) => {
  console.log('[API Request]', {
    url: config.url,
    method: config.method,
    baseURL: config.baseURL,
    headers: config.headers,
    withCredentials: config.withCredentials,
    data: config.data,
    params: config.params,
    environment: process.env.NODE_ENV,
    isDevelopment: ENV_CONFIG.isDevelopment,
    timestamp: new Date().toISOString(),
  });
  return config;
}, (error) => {
  console.error('[API Request Error]', {
    error: error.message,
    timestamp: new Date().toISOString(),
  });
  return Promise.reject(error);
});

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('[API Response]', {
      url: response.config.url,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      timestamp: new Date().toISOString(),
    });
    return response;
  },
  (error) => {
    console.error('[API Response Error]', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      headers: error.response?.headers,
      data: error.response?.data,
      message: error.message,
      timestamp: new Date().toISOString(),
    });

    // 统一错误处理
    if (error.response?.status === 401) {
      // 未认证，重定向到登录页
      window.location.href = ENV_CONFIG.routes.auth.login;
    }

    return Promise.reject(error);
  }
); 