import { AiProvider, AiProviderType, ProviderStatus, ProviderConfig, ProviderDefaultType } from '@/types/ai';

// 后端 DTO 类型定义
interface AiProviderDTO {
  id?: string;
  name: string;
  type: string;
  status: string;
  apiKey: string;
  apiEndpoint?: string;
  defaultType?: string;
  requestCount?: number;
  costAmount?: number;
  createdAt?: string;
  updatedAt?: string;
  lastTestAt?: string;
  lastTestResult?: {
    success: boolean;
    details?: string;
  };
}

// 前端配置转换为后端配置
function transformConfigToDTO(config: Partial<ProviderConfig>): Partial<AiProviderDTO> {
  return {
    apiKey: config.apiKey || '',
    apiEndpoint: config.apiEndpoint,
  };
}

// 前端数据转换为后端 DTO
export function transformProviderToDTO(provider: Partial<AiProvider>): AiProviderDTO {
  console.log('transformProviderToDTO input:', provider);
  
  const dto = {
    id: provider.id,
    name: provider.name!,
    type: provider.type!,
    status: provider.status || ProviderStatus.TESTING,
    defaultType: provider.defaultType,
    apiKey: provider.config?.apiKey === '********' ? '' : provider.config?.apiKey || '',
    apiEndpoint: provider.config?.apiEndpoint,
    requestCount: provider.requestCount,
    costAmount: provider.costAmount,
    createdAt: provider.createdAt,
    updatedAt: provider.updatedAt,
    lastTestAt: provider.lastTestAt,
    lastTestResult: provider.lastTestResult,
  };

  // 移除所有 undefined 的字段
  Object.keys(dto).forEach(key => {
    if (dto[key as keyof typeof dto] === undefined) {
      delete dto[key as keyof typeof dto];
    }
  });

  console.log('transformProviderToDTO output:', dto);
  return dto;
}

// 后端 DTO 转换为前端数据
export function transformDTOToProvider(dto: AiProviderDTO): AiProvider {
  console.log('transformDTOToProvider input:', dto);
  
  const provider = {
    id: dto.id!,
    name: dto.name,
    type: dto.type as AiProviderType,
    status: dto.status as ProviderStatus,
    defaultType: dto.defaultType as ProviderDefaultType,
    config: {
      apiKey: dto.apiKey,
      apiEndpoint: dto.apiEndpoint,
    },
    requestCount: dto.requestCount,
    costAmount: dto.costAmount,
    createdAt: dto.createdAt!,
    updatedAt: dto.updatedAt!,
    lastTestAt: dto.lastTestAt,
    lastTestResult: dto.lastTestResult,
  };

  console.log('transformDTOToProvider output:', provider);
  return provider;
} 