import { api } from '@/lib/api';

export interface UserStats {
  user: {
    id: string;
    email: string;
    createdAt: Date;
  };
  stats: {
    lastLoginAt: Date;
    loginCount: number;
  };
}

export interface SystemStats {
  users: {
    total: number;
    active: number;
  };
}

export interface DashboardStats {
  totalCourses: number;
  studiedCourses: number;
  totalProgress: number;
  recentUpdateTime: number | null;
}

export interface RecentLearning {
  courseId: string;
  courseName: string;
  courseCode: string;
  progress: number;
  lastVisitTime: string;
  lastLesson: {
    id: string;
    title: string;
    order: number;
  } | null;
}

export class StatsService {
  static async getUserStats(): Promise<UserStats> {
    const response = await api.get('/api/stats/user');
    return response.data;
  }

  static async getSystemStats(): Promise<SystemStats> {
    const response = await api.get('/api/stats/system');
    return response.data;
  }

  static async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get('/api/stats/dashboard');
    return response.data;
  }

  static async getRecentLearning(): Promise<RecentLearning[]> {
    const response = await api.get('/api/stats/recent-learning');
    return response.data;
  }
} 