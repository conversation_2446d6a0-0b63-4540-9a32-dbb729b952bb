import { io, Socket } from 'socket.io-client';

export class WebSocketService {
  private static instance: WebSocketService;
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private readonly reconnectDelay = 1000;

  private constructor() {
    this.setupSocket();
  }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  private setupSocket() {
    const userId = localStorage.getItem('userId');
    if (!userId) {
      console.warn('WebSocket: No userId found in localStorage');
      return;
    }

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || window.location.origin;
    
    this.socket = io(wsUrl, {
      query: { userId },
      reconnection: true,
      reconnectionDelay: this.reconnectDelay,
      reconnectionAttempts: this.maxReconnectAttempts
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
    });

    this.socket.on('connect_error', (error: Error) => {
      console.error('WebSocket connection error:', error);
      this.handleReconnect();
    });

    // 图片上传完成事件处理
    this.socket.on('imageUploaded', (data: { sceneId: string; imageUrl: string }) => {
      console.log('Image uploaded:', data);
      // 触发自定义事件，让组件可以监听
      window.dispatchEvent(new CustomEvent('imageUrlUpdated', { detail: data }));
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => this.setupSocket(), this.reconnectDelay);
    } else {
      console.error('WebSocket: Max reconnection attempts reached');
    }
  }

  public getSocket(): Socket | null {
    return this.socket;
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  public reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.setupSocket();
  }
}

// 导出一个获取实例的函数
export const getWebSocketInstance = () => WebSocketService.getInstance(); 