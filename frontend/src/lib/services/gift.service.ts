import { api } from '@/lib/api/index';

export interface GiftCode {
  id: string;
  code: string;
  planType: string;
  durationDays: number;
  expiresAt: string;
  isUsed: boolean;
  usedAt: string | null;
  usedBy: {
    id: string;
    email: string;
  } | null;
  createdAt: string;
}

export interface GiftCodeListResponse {
  data: GiftCode[];
  total: number;
}

/**
 * 兑换礼品码
 */
export async function redeemGiftCode(code: string): Promise<void> {
  await api.post('/api/gift-codes/redeem', { code });
}

/**
 * 获取礼品码列表（管理员）
 */
export async function getGiftCodes(params: {
  page?: number;
  pageSize?: number;
  status?: 'all' | 'used' | 'unused' | 'expired';
  planType?: string;
}): Promise<GiftCodeListResponse> {
  const response = await api.get<GiftCodeListResponse>('/api/gift-codes', { params });
  return response.data;
}

/**
 * 创建礼品码（管理员）
 */
export async function createGiftCodes(params: {
  planType: string;
  durationDays: number;
  quantity: number;
}): Promise<GiftCode[]> {
  const response = await api.post<GiftCode[]>('/api/gift-codes', params);
  return response.data;
}

/**
 * 导出礼品码（管理员）
 */
export async function exportGiftCodes(ids?: string[]): Promise<Blob> {
  const response = await api.get('/api/gift-codes/export', {
    params: ids ? { ids: ids.join(',') } : undefined,
    responseType: 'blob',
  });
  return response.data;
}

/**
 * 批量删除礼品码（管理员）
 */
export async function deleteGiftCodes(ids: string[]): Promise<void> {
  await api.delete('/api/gift-codes/batch', { data: { ids } });
} 