import { api } from '@/lib/api/index';

export interface CreateOrderDto {
  membershipType: 'MONTHLY' | 'QUARTERLY' | 'SEMIANNUAL';
}

export interface OrderResponse {
  orderId: string;
  orderNo: string;
  payment: {
    codeUrl: string;  // Native 支付时返回的二维码链接
  };
}

export interface OrderStatus {
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'REFUNDED' | 'EXPIRED';
  paid: boolean;
  expired: boolean;
}

export interface Order {
  id: string;
  orderNo: string;
  userId: string;
  orderType: 'MEMBERSHIP';
  productName: string;
  amount: number;
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'REFUNDED' | 'EXPIRED';
  paymentMethod: 'WECHAT';
  transactionId?: string;
  paidAt?: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    membershipType: 'MONTHLY' | 'QUARTERLY' | 'SEMIANNUAL';
    duration: number;
  };
}

export interface OrderListResponse {
  items: Order[];
  total: number;
  page: number;
  limit: number;
}

/**
 * 创建支付订单
 */
export async function createOrder(data: CreateOrderDto): Promise<OrderResponse> {
  const response = await api.post<OrderResponse>('/api/payment/orders', data);
  return response.data;
}

/**
 * 查询订单状态
 */
export async function queryOrderStatus(orderNo: string): Promise<OrderStatus> {
  const response = await api.get<Order>(`/api/payment/orders/${orderNo}`);
  return {
    status: response.data.status,
    paid: response.data.status === 'PAID',
    expired: response.data.status === 'EXPIRED',
  };
}

/**
 * 查询订单详情
 */
export async function getOrderDetail(orderNo: string): Promise<Order> {
  const response = await api.get<Order>(`/api/payment/orders/${orderNo}`);
  return response.data;
}

/**
 * 获取用户订单列表
 */
export async function getUserOrders(page: number = 1, limit: number = 10): Promise<OrderListResponse> {
  const response = await api.get<OrderListResponse>('/api/payment/orders', {
    params: { page, limit },
  });
  return response.data;
} 