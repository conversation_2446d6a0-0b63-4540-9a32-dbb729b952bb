import { api } from '@/lib/api/index';

export interface DescriptionCompleteParams {
  type: 'appearance' | 'personality';
  currentDescription: string;
}

export interface AiServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  usage?: {
    totalTokens: number;
    cost: number;
  };
  metadata?: {
    provider?: string;
    model?: string;
    timestamp?: string;
  };
}

export interface AiImageGenerateParams {
  prompt: string;
  count?: number;
}

export interface AiSceneGenerateParams {
  theme: string;
  sceneCount: number;
  wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200';
  description: string;
  keyWords?: string;
  subjectKnowledge?: string;
  vocabularyLevel: string;
  mainCharacter?: string;
}

export interface AiEditContentParams {
  content?: string;
  instruction: string;
  style?: 'cartoon' | 'watercolor' | 'children' | 'simple' | 'cute';
  character?: {
    name: string;
    type: string;
    appearance?: string;
    personality?: string;
    description?: string;
  };
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatParams {
  messages: ChatMessage[];
  character?: string;
  temperature?: number;
  max_tokens?: number;
}

export class AiService {
  static async generateImages(params: AiImageGenerateParams): Promise<AiServiceResponse<string[]>> {
    try {
      const response = await api.post('/api/ai/images/generate', params);
      return response.data;
    } catch (error: any) {
      console.error('生成图片失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '生成图片失败',
      };
    }
  }

  static async generateScenes(params: AiSceneGenerateParams): Promise<AiServiceResponse<string>> {
    try {
      const response = await api.post('/api/ai/generate-scenes', params);
      return response.data;
    } catch (error: any) {
      console.error('生成场景失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '生成场景失败',
      };
    }
  }

  static async editContent(params: AiEditContentParams): Promise<AiServiceResponse<string>> {
    try {
      const response = await api.post('/api/ai/edit', params);
      return response.data;
    } catch (error: any) {
      console.error('编辑内容失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '编辑内容失败',
      };
    }
  }

  static async completeDescription(params: DescriptionCompleteParams): Promise<AiServiceResponse<string>> {
    try {
      const response = await api.post<AiServiceResponse<string>>('/api/ai/complete-description', params);
      return response.data;
    } catch (error: any) {
      console.error('补全描述失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '补全描述失败',
      };
    }
  }

  static async chat(params: ChatParams): Promise<AiServiceResponse<string>> {
    try {
      const response = await api.post('/api/ai/chat', params);
      return response.data;
    } catch (error: any) {
      console.error('AI 对话失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || 'AI 对话失败',
      };
    }
  }
} 