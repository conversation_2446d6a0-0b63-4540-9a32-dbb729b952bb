import { api } from '@/lib/api/index';

export interface OssServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export class OssService {
  static async saveImage(imageUrl: string): Promise<OssServiceResponse<{ url: string; key: string }>> {
    try {
      const response = await api.post('/api/oss/save-image', { imageUrl });
      return response.data;
    } catch (error: any) {
      console.error('保存图片失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '保存图片失败',
      };
    }
  }

  static async saveImageAsync(
    imageUrl: string, 
    userId: string, 
    sceneId: string
  ): Promise<OssServiceResponse<{ url: string }>> {
    try {
      const response = await api.post('/api/oss/save-image-async', { 
        imageUrl,
        userId,
        sceneId
      });
      return response.data;
    } catch (error: any) {
      console.error('启动异步上传失败:', error);
      return {
        success: false,
        error: error?.response?.data?.message || error.message || '启动异步上传失败',
      };
    }
  }
} 