import { api } from '@/lib/api/index';
import type { BuildingTool } from './course.service';

export interface CreateBuildingToolData {
  code: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  category: string;
  difficulty: string;
  estimatedTime: string;
  isActive?: boolean;
  sortOrder?: number;
}

export class BuildingToolService {
  /**
   * 获取所有建站工具
   */
  static async getAll(): Promise<BuildingTool[]> {
    const response = await api.get('/api/admin/building-tools', {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取单个建站工具
   */
  static async getOne(id: string): Promise<BuildingTool> {
    const response = await api.get(`/api/admin/building-tools/${id}`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 创建建站工具
   */
  static async create(data: CreateBuildingToolData): Promise<BuildingTool> {
    const response = await api.post('/api/admin/building-tools', data, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 更新建站工具
   */
  static async update(id: string, data: Partial<CreateBuildingToolData>): Promise<BuildingTool> {
    const response = await api.patch(`/api/admin/building-tools/${id}`, data, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 删除建站工具
   */
  static async delete(id: string): Promise<void> {
    await api.delete(`/api/admin/building-tools/${id}`, {
      withCredentials: true,
    });
  }

  /**
   * 为课程分配建站工具
   */
  static async assignToolsToCourse(courseId: string, toolIds: string[]): Promise<void> {
    await api.post(`/api/admin/building-tools/courses/${courseId}/tools`, { toolIds }, {
      withCredentials: true,
    });
  }

  /**
   * 获取课程的建站工具
   */
  static async getCourseTools(courseId: string): Promise<BuildingTool[]> {
    const response = await api.get(`/api/admin/building-tools/courses/${courseId}/tools`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 从课程移除建站工具
   */
  static async removeToolFromCourse(courseId: string, toolId: string): Promise<void> {
    await api.delete(`/api/admin/building-tools/courses/${courseId}/tools/${toolId}`, {
      withCredentials: true,
    });
  }
} 