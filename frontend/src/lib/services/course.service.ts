import { api } from '@/lib/api/index';
import { HealingType } from '@/config/healing-types';

export interface Course {
  id: string;
  code?: string;
  title: string;
  description: string;
  duration: string;
  level: HealingType;
  lessonsCount: number;
  price: number;
  isPurchased?: boolean;
  isPublished?: boolean;
  accessReason?: 'free_course' | 'purchased' | 'subscription' | 'no_access';
  catalogue?: {
    id: string;
    name: string;
    description?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CourseLesson {
  id: string;
  courseId: string;
  title: string;
  description: string;
  duration: string;
  order: number;
  content: string;
  isCompleted?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CourseStep {
  type: 'text' | 'media' | 'wait' | 'input';
  speaker?: string;
  content?: string;
  media?: CourseMedia;
  placeholder?: string;
}

export interface CourseMedia {
  type: 'image' | 'video' | 'audio';
  url: string;
  title?: string;
  description?: string;
}

export class CourseService {
  /**
   * 获取对话列表
   * @param includeUnpublished 是否包含未发布的对话（仅管理员可用）
   */
  static async getCourses(includeUnpublished = false): Promise<Course[]> {
    const response = await api.get('/api/courses', {
      params: { includeUnpublished },
      withCredentials: true,
    });
    return response.data;
  }

  static async getCourse(courseId: string): Promise<Course> {
    const response = await api.get(`/api/courses/${courseId}`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取对话主题列表（带完成进度）
   * 需要用户登录
   */
  static async getCourseLessons(courseId: string): Promise<CourseLesson[]> {
    const response = await api.get(`/api/courses/${courseId}/lessons`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取对话主题列表（公开）
   * 不需要用户登录
   */
  static async getCoursePublicLessons(courseId: string): Promise<CourseLesson[]> {
    const response = await api.get(`/api/courses/${courseId}/public-lessons`, {
      withCredentials: true,
    });
    return response.data;
  }

  static async updateProgress(courseId: string, lessonId: string): Promise<void> {
    await api.post(`/api/courses/${courseId}/lessons/${lessonId}/progress`);
  }

  static async getProgress(courseId: string): Promise<{
    progress: number;
    completedLessons: string[];
    lastVisitedLessonId: string;
  }> {
    const response = await api.get(`/api/courses/${courseId}/progress`);
    return response.data;
  }

  /**
   * 获取对话主题内容
   */
  static async getLessonContent(courseId: string, lessonId: string): Promise<string> {
    const response = await api.get(`/api/courses/${courseId}/lessons/${lessonId}/content`);
    return response.data.content;
  }

  /**
   * 更新对话主题内容
   */
  static async updateLessonContent(courseId: string, lessonId: string, content: string): Promise<void> {
    await api.put(
      `/api/courses/${courseId}/lessons/${lessonId}/content`,
      { content },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // 检查用户是否已购买对话或有订阅访问权限
  static async checkPurchaseStatus(courseId: string): Promise<boolean> {
    try {
      const response = await api.get(`/api/courses/${courseId}/lessons`);
      return true;
    } catch (error: any) {
      if (error.response?.status === 403) {
        const errorData = error.response?.data;
        if (errorData?.reason === 'no_access' || errorData?.requiresSubscription) {
          return false;
        }
      }
      if (error.response?.status === 404 && error.response?.data?.message === '请先购买对话') {
        return false;
      }
      throw error;
    }
  }

  // 检查课程访问权限详情
  static async checkCourseAccess(courseId: string): Promise<{
    hasAccess: boolean;
    reason: 'free_course' | 'purchased' | 'subscription' | 'no_access';
    message?: string;
    requiresSubscription?: boolean;
  }> {
    try {
      const course = await this.getCourse(courseId);
      return {
        hasAccess: course.isPurchased || false,
        reason: course.accessReason || 'no_access',
        requiresSubscription: !course.isPurchased && course.accessReason === 'no_access'
      };
    } catch (error: any) {
      return {
        hasAccess: false,
        reason: 'no_access',
        message: 'Failed to check course access',
        requiresSubscription: true
      };
    }
  }

  // 获取用户已购买的对话
  static async getPurchasedCourses(): Promise<Course[]> {
    const response = await api.get('/api/courses/purchased');
    return response.data;
  }

  // 加入免费对话
  static async enrollFreeCourse(courseId: string): Promise<{ courseId: string; message: string }> {
    const response = await api.post(`/api/courses/free-enroll/${courseId}`);
    return response.data;
  }

  /**
   * 删除对话
   */
  static async deleteCourse(id: string): Promise<void> {
    await api.delete(`/api/courses/${id}`);
  }

  /**
   * 创建对话
   */
  static async createCourse(courseData: Partial<Course>): Promise<Course> {
    const { data } = await api.post('/api/courses', courseData);
    return data;
  }

  /**
   * 更新对话
   */
  static async updateCourse(id: string, courseData: Partial<Course>): Promise<Course> {
    const { data } = await api.put(`/api/courses/${id}`, courseData);
    return data;
  }

  /**
   * 发布对话
   */
  static async publishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/publish`);
    return data;
  }

  /**
   * 取消发布对话
   */
  static async unpublishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/unpublish`);
    return data;
  }

  /**
   * 切换对话发布状态
   */
  static async togglePublishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/toggle-publish`);
    return data;
  }

  /**
   * 创建对话主题
   */
  static async createLesson(courseId: string, lessonData: Partial<CourseLesson>): Promise<CourseLesson> {
    const { data } = await api.post(`/api/courses/${courseId}/lessons`, lessonData);
    return data;
  }

  /**
   * 更新对话主题
   */
  static async updateLesson(
    courseId: string,
    lessonId: string,
    lessonData: Partial<CourseLesson>
  ): Promise<CourseLesson> {
    const { data } = await api.patch(
      `/api/courses/${courseId}/lessons/${lessonId}`,
      lessonData
    );
    return data;
  }

  /**
   * 删除对话主题
   */
  static async deleteLesson(courseId: string, lessonId: string): Promise<void> {
    await api.delete(`/api/courses/${courseId}/lessons/${lessonId}`);
  }

  /**
   * 重新排序对话主题
   */
  static async reorderLessons(courseId: string, lessonIds: string[]): Promise<void> {
    await api.post(`/api/courses/${courseId}/lessons/reorder`, { lessonIds });
  }

  static async getLesson(lessonId: string): Promise<CourseLesson> {
    const response = await api.get(`/api/courses/lessons/${lessonId}`);
    return response.data;
  }

  static async completeLesson(lessonId: string): Promise<void> {
    await api.post(`/api/courses/lessons/${lessonId}/complete`);
  }
} 