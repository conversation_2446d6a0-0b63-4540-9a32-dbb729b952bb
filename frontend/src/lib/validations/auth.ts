import * as z from 'zod'

export const loginSchema = z.object({
  email: z.string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z.string()
    .min(6, '密码至少需要6个字符')
    .max(50, '密码不能超过50个字符'),
})

export const registerSchema = z.object({
  email: z.string().email({ message: "请输入有效的邮箱地址" }),
  password: z.string().min(6, { message: "密码至少需要6个字符" }),
  name: z.string().min(2, { message: "用户名至少需要2个字符" }),
})

export const updateProfileSchema = z.object({
  name: z.string().min(2, '名称至少需要2个字符').optional(),
  password: z.string().min(6, '密码至少需要6个字符').optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: '至少需要更新一个字段'
})

export const adminLoginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
})

export type LoginInput = z.infer<typeof loginSchema>
export type RegisterInput = z.infer<typeof registerSchema>
export type UpdateProfileInput = z.infer<typeof updateProfileSchema> 