import * as z from 'zod';
import { AiProviderType } from '@/types/ai';
import { UserRole, UserStatus } from '@/lib/services/user.service';

export const aiProviderSchema = z.object({
  name: z.string().min(2, '名称至少需要2个字符'),
  type: z.nativeEnum(AiProviderType, {
    errorMap: () => ({ message: '请选择有效的提供商类型' }),
  }),
  apiKey: z.string().min(1, '请输入 API Key'),
  baseUrl: z.string().optional(),
});

export const userUpdateSchema = z.object({
  name: z.string().min(2, '名称至少需要2个字符').optional(),
  email: z.string().email('请输入有效的邮箱地址').optional(),
  role: z.nativeEnum(UserRole, {
    errorMap: () => ({ message: '请选择有效的用户角色' }),
  }).optional(),
  status: z.nativeEnum(UserStatus, {
    errorMap: () => ({ message: '请选择有效的用户状态' }),
  }).optional(),
}); 