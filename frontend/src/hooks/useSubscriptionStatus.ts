'use client';

import { useState, useEffect } from 'react';
import { api } from '@/lib/api';

interface SubscriptionStatus {
  id?: string;
  planName?: string;
  isActive: boolean;
  status: 'active' | 'inactive' | 'cancelled' | 'expired' | 'trial';
  aiCallsUsed: number;
  aiCallsLimit: number;
  daysRemaining: number;
  trialEndDate?: string;
}

interface UseSubscriptionStatusReturn {
  subscription: SubscriptionStatus | null;
  loading: boolean;
  error: string | null;
  hasActiveSubscription: boolean;
  isTrialUser: boolean;
  canUseAI: boolean;
  refreshSubscription: () => Promise<void>;
}

export function useSubscriptionStatus(): UseSubscriptionStatusReturn {
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptionStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const [currentResponse, usageResponse] = await Promise.all([
        api.get('/api/subscription/current').catch(() => null),
        api.get('/api/subscription/usage').catch(() => null)
      ]);

      let subscriptionData = null;
      let usageData = null;

      if (currentResponse) {
        subscriptionData = currentResponse.data;
      }

      if (usageResponse) {
        usageData = usageResponse.data;
      }

      // Combine the data
      if (subscriptionData) {
        setSubscription({
          id: subscriptionData.id,
          planName: subscriptionData.planName,
          isActive: subscriptionData.isActive,
          status: subscriptionData.status,
          aiCallsUsed: usageData?.aiCallsUsed || 0,
          aiCallsLimit: usageData?.aiCallsLimit || 0,
          daysRemaining: usageData?.daysRemaining || 0,
          trialEndDate: subscriptionData.trialEndDate
        });
      } else {
        setSubscription({
          isActive: false,
          status: 'inactive',
          aiCallsUsed: 0,
          aiCallsLimit: 0,
          daysRemaining: 0
        });
      }
    } catch (err) {
      console.error('Failed to fetch subscription status:', err);
      setError(err instanceof Error ? err.message : 'Failed to load subscription status');
      setSubscription({
        isActive: false,
        status: 'inactive',
        aiCallsUsed: 0,
        aiCallsLimit: 0,
        daysRemaining: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const hasActiveSubscription = Boolean(subscription?.isActive && 
    (subscription.status === 'active' || subscription.status === 'trial'));

  const isTrialUser = subscription?.status === 'trial';

  const canUseAI = Boolean(subscription?.isActive && 
    subscription.aiCallsUsed < subscription.aiCallsLimit);

  return {
    subscription,
    loading,
    error,
    hasActiveSubscription,
    isTrialUser,
    canUseAI,
    refreshSubscription: fetchSubscriptionStatus
  };
}