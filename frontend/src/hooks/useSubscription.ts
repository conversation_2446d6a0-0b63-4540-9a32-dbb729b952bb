import { useState, useCallback, useEffect } from 'react'
import { getCurrentSubscription, getSubscriptionHistory } from '@/lib/services/subscription.service'
import type { Subscription, UseSubscriptionReturn } from '@/types/subscription'

export function useSubscription(): UseSubscriptionReturn {
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [history, setHistory] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 格式化历史记录
  const formatHistory = useCallback((items: Subscription[]) => {
    return items.map(item => ({
      ...item,
      isActive: new Date(item.endDate) > new Date()
    }))
  }, [])

  // 加载数据
  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [currentSub, historyData] = await Promise.all([
        getCurrentSubscription(),
        getSubscriptionHistory()
      ])

      setSubscription(currentSub)
      setHistory(formatHistory(historyData.items || []))
    } catch (err) {
      console.error('加载订阅信息失败:', err)
      setError(err instanceof Error ? err.message : '加载订阅信息失败')
    } finally {
      setLoading(false)
    }
  }, [formatHistory])

  // 初始加载
  useEffect(() => {
    loadData()
  }, [loadData])

  return {
    subscription,
    history,
    loading,
    error,
    reload: loadData,
    formatHistory
  }
}