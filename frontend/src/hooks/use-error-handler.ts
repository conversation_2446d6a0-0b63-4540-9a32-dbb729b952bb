import { useToast } from '@/components/ui/use-toast';
import { AxiosError } from 'axios';

interface ErrorResponse {
  message: string;
  error?: string;
  statusCode?: number;
}

export function useErrorHandler() {
  const { toast } = useToast();

  const handleError = (error: unknown) => {
    if (error instanceof AxiosError) {
      const data = error.response?.data as ErrorResponse | undefined;
      toast({
        title: '错误',
        description: data?.message || error.message,
        variant: 'destructive',
      });
    } else if (error instanceof Error) {
      toast({
        title: '错误',
        description: error.message,
        variant: 'destructive',
      });
    } else {
      toast({
        title: '错误',
        description: '发生未知错误',
        variant: 'destructive',
      });
    }
  };

  return { handleError };
} 