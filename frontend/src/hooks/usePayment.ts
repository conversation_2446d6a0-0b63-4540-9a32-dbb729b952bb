import { useState, useCallback } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { createOrder } from '@/lib/services/payment.service'
import type { PaymentResponse, CreateOrderParams, MembershipType } from '@/types/subscription'

interface UsePaymentReturn {
  isLoading: boolean
  orderNo: string | null
  createPayment: (membershipType: MembershipType) => Promise<PaymentResponse | null>
  handleWechatPay: (payment: PaymentResponse['payment']) => Promise<boolean>
  resetPayment: () => void
}

export function usePayment(): UsePaymentReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [orderNo, setOrderNo] = useState<string | null>(null)
  const { toast } = useToast()

  const resetPayment = useCallback(() => {
    setOrderNo(null)
  }, [])

  const createPayment = useCallback(async (membershipType: MembershipType) => {
    try {
      setIsLoading(true)
      const params: CreateOrderParams = { membershipType }
      const response = await createOrder(params)
      setOrderNo(response.orderNo)
      return response
    } catch (error) {
      toast({
        title: '创建订单失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive',
      })
      return null
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  const handleWechatPay = useCallback(async (payment: PaymentResponse['payment']): Promise<boolean> => {
    try {
      const wx = (window as any).WeixinJSBridge
      await new Promise<void>((resolve, reject) => {
        wx.invoke('getBrandWCPayRequest', {
          ...payment,
          success: (res: any) => {
            if (res.err_msg === 'get_brand_wcpay_request:ok') {
              resolve()
            } else {
              reject(new Error('支付失败'))
            }
          },
          fail: (err: any) => {
            reject(err)
          },
        })
      })
      return true
    } catch (error) {
      toast({
        title: '支付失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive',
      })
      return false
    }
  }, [toast])

  return {
    isLoading,
    orderNo,
    createPayment,
    handleWechatPay,
    resetPayment
  }
}