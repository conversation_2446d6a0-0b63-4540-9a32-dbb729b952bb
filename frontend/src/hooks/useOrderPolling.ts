import { useState, useEffect, useCallback } from 'react'
import { useToast } from '@/components/ui/use-toast'
import { queryOrderStatus, getOrderDetail } from '@/lib/services/payment.service'
import type { OrderStatus } from '@/types/subscription'

interface UseOrderPollingReturn {
  polling: boolean
  orderStatus: OrderStatus | null
  startPolling: (orderNo: string) => void
  stopPolling: () => void
}

export function useOrderPolling(
  onSuccess?: () => void,
  onExpired?: () => void,
  interval: number = 3000
): UseOrderPollingReturn {
  const [polling, setPolling] = useState(false)
  const [orderNo, setOrderNo] = useState<string | null>(null)
  const [orderStatus, setOrderStatus] = useState<OrderStatus | null>(null)
  const { toast } = useToast()

  const stopPolling = useCallback(() => {
    setPolling(false)
    setOrderNo(null)
    setOrderStatus(null)
  }, [])

  const startPolling = useCallback((newOrderNo: string) => {
    setOrderNo(newOrderNo)
    setPolling(true)
  }, [])

  useEffect(() => {
    if (!orderNo || !polling) return

    const checkOrderStatus = async () => {
      try {
        const statusResult = await queryOrderStatus(orderNo)
        const orderDetail = await getOrderDetail(orderNo)
        
        // 构造完整的 OrderStatus 对象
        const fullStatus: OrderStatus = {
          paid: statusResult.paid,
          expired: statusResult.expired,
          status: statusResult.status,
          orderNo,
          createdAt: orderDetail.createdAt,
          updatedAt: orderDetail.updatedAt,
        }
        
        setOrderStatus(fullStatus)
        
        if (fullStatus.paid) {
          toast({
            title: '支付成功',
            description: '订单支付成功，正在处理...',
          })
          stopPolling()
          onSuccess?.()
        } else if (fullStatus.expired) {
          toast({
            title: '订单已过期',
            description: '请重新创建订单',
            variant: 'destructive',
          })
          stopPolling()
          onExpired?.()
        }
      } catch (error) {
        console.error('查询订单状态失败:', error)
        // 可选：在发生错误时停止轮询
        // stopPolling()
      }
    }

    const intervalId = setInterval(checkOrderStatus, interval)

    return () => {
      clearInterval(intervalId)
    }
  }, [orderNo, polling, interval, onSuccess, onExpired, stopPolling, toast])

  return {
    polling,
    orderStatus,
    startPolling,
    stopPolling
  }
}