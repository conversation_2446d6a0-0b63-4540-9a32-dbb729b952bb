'use client'

import { useState } from 'react'
import { useAuth, useUserRole } from '@/auth/contexts/simplified-auth-context'
import { usePermissions } from '@/auth/hooks/simplified-permissions'
import { PermissionGate, AdminOnly, UserOnly } from '@/auth/components/simplified-permission-gate'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Loader2, AlertCircle, CheckCircle, User, ShieldAlert } from 'lucide-react'

/**
 * 认证系统示例组件
 * 展示如何使用新的认证系统
 */
export function AuthExample() {
  // 使用认证Hook
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error, 
    login, 
    logout 
  } = useAuth()
  
  // 使用角色Hook
  const { isAdmin, isUser, role } = useUserRole()
  
  // 使用权限Hook
  const { hasPermission, hasRole, canAccessRoute } = usePermissions()
  
  // 登录表单状态
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loginError, setLoginError] = useState<string | null>(null)
  
  // 处理登录
  const handleLogin = async () => {
    setLoginError(null)
    try {
      await login(email, password)
    } catch (error) {
      setLoginError(error instanceof Error ? error.message : '登录失败')
    }
  }
  
  // 处理登出
  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出失败:', error)
    }
  }
  
  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    )
  }
  
  return (
    <div className="max-w-md mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>认证系统示例</CardTitle>
          <CardDescription>
            展示如何使用新的认证系统
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* 错误提示 */}
          {(error || loginError) && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>错误</AlertTitle>
              <AlertDescription>
                {error?.message || loginError}
              </AlertDescription>
            </Alert>
          )}
          
          {/* 认证状态 */}
          <div className="mb-4 p-3 bg-muted rounded-md">
            <h3 className="font-medium mb-2">认证状态</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>已认证:</div>
              <div>{isAuthenticated ? '是' : '否'}</div>
              
              <div>用户角色:</div>
              <div>{role || '未登录'}</div>
              
              <div>是管理员:</div>
              <div>{isAdmin ? '是' : '否'}</div>
              
              <div>是普通用户:</div>
              <div>{isUser ? '是' : '否'}</div>
            </div>
          </div>
          
          {/* 权限检查示例 */}
          <div className="mb-4 p-3 bg-muted rounded-md">
            <h3 className="font-medium mb-2">权限检查</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>有管理员角色:</div>
              <div>{hasRole('admin') ? '是' : '否'}</div>
              
              <div>有读取权限:</div>
              <div>{hasPermission('read') ? '是' : '否'}</div>
              
              <div>有创建权限:</div>
              <div>{hasPermission('create') ? '是' : '否'}</div>
              
              <div>可访问仪表盘:</div>
              <div>{canAccessRoute('/dashboard') ? '是' : '否'}</div>
              
              <div>可访问管理员页面:</div>
              <div>{canAccessRoute('/admin/dashboard') ? '是' : '否'}</div>
            </div>
          </div>
          
          {/* 权限组件示例 */}
          <div className="mb-4">
            <h3 className="font-medium mb-2">权限组件</h3>
            
            <div className="space-y-2">
              {/* 管理员可见内容 */}
              <AdminOnly fallback={<div className="p-2 bg-muted rounded text-sm">需要管理员权限</div>}>
                <div className="p-2 bg-green-100 rounded flex items-center">
                  <ShieldAlert className="h-4 w-4 mr-2" />
                  <span className="text-sm">管理员可见内容</span>
                </div>
              </AdminOnly>
              
              {/* 普通用户可见内容 */}
              <UserOnly fallback={<div className="p-2 bg-muted rounded text-sm">需要用户权限</div>}>
                <div className="p-2 bg-blue-100 rounded flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  <span className="text-sm">用户可见内容</span>
                </div>
              </UserOnly>
              
              {/* 自定义权限内容 */}
              <PermissionGate 
                permission="create" 
                fallback={<div className="p-2 bg-muted rounded text-sm">需要创建权限</div>}
              >
                <div className="p-2 bg-purple-100 rounded flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm">有创建权限可见内容</span>
                </div>
              </PermissionGate>
            </div>
          </div>
          
          {/* 登录表单或用户信息 */}
          {!isAuthenticated ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="请输入邮箱"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="请输入密码"
                />
              </div>
            </div>
          ) : (
            <div className="p-3 bg-green-100 rounded-md">
              <h3 className="font-medium mb-2">用户信息</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>ID:</div>
                <div>{user?.id}</div>
                
                <div>邮箱:</div>
                <div>{user?.email}</div>
                
                <div>姓名:</div>
                <div>{user?.name}</div>
                
                <div>角色:</div>
                <div>{user?.role}</div>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-end">
          {!isAuthenticated ? (
            <Button onClick={handleLogin}>登录</Button>
          ) : (
            <Button variant="outline" onClick={handleLogout}>登出</Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
