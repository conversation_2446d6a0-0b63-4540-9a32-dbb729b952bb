{"name": "admin-core-backend", "version": "0.1.0", "description": "A Professional Admin System Framework", "scripts": {"build": "nest build -p tsconfig.json", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --exec \"ts-node -r tsconfig-paths/register src/main.ts\" --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "create-admin": "ts-node -r tsconfig-paths/register src/infrastructure/cli/create-admin.ts", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js", "migration:create": "npm run typeorm migration:create", "migration:generate": "npm run typeorm migration:generate -- -d src/config/typeorm.config.ts", "migration:run": "npm run typeorm migration:run -- -d src/config/typeorm.config.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/config/typeorm.config.ts", "swagger:json": "ts-node -r tsconfig-paths/register src/swagger.ts"}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@anthropic-ai/sdk": "^0.33.1", "@nestjs/bull": "^11.0.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.12", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.12", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-socket.io": "^10.4.12", "@nestjs/schedule": "^5.0.0", "@nestjs/serve-static": "^5.0.2", "@nestjs/swagger": "^8.0.7", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.12", "@types/ali-oss": "^6.16.11", "@types/axios": "^0.14.4", "@types/bull": "^4.10.4", "@types/cookie-parser": "^1.4.8", "@types/ioredis": "^5.0.0", "@types/nodemailer": "^6.4.17", "ali-oss": "^6.22.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "ioredis": "^5.4.1", "next-themes": "^0.4.4", "nodemailer": "^6.10.0", "openai": "^4.74.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "stripe": "^18.0.0", "typeorm": "^0.3.20", "uuid": "^11.0.5", "wechatpay-node-v3": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.4.8", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.12", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cache-manager-redis-store": "^2.0.4", "@types/dotenv": "^8.2.3", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.17.9", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "main": "jest.config.js", "directories": {"test": "test"}, "keywords": [], "author": "", "license": "ISC"}