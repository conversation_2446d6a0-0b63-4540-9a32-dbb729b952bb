import { NestFactory } from '@nestjs/core';
import { AppModule } from '@/app.module';
import { DataSource } from 'typeorm';
import { User, UserRole, UserStatus } from '@/modules/user/entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { ConfigService } from '@nestjs/config';

async function createAdmin() {
  try {
    // 创建一个独立的应用上下文
    const app = await NestFactory.createApplicationContext(AppModule);
    const dataSource = app.get(DataSource);
    const configService = app.get(ConfigService);

    // 从环境变量获取管理员信息
    const adminEmail = configService.get('ADMIN_EMAIL');
    const adminPassword = configService.get('ADMIN_PASSWORD');
    const adminName = configService.get('ADMIN_NAME');

    // 检查必要的环境变量
    if (!adminEmail) {
      throw new Error('环境变量 ADMIN_EMAIL 未设置');
    }
    if (!adminPassword) {
      throw new Error('环境变量 ADMIN_PASSWORD 未设置');
    }
    if (!adminName) {
      throw new Error('环境变量 ADMIN_NAME 未设置');
    }

    // 检查是否已存在管理员
    const existingAdmin = await dataSource.getRepository(User).findOne({
      where: [
        { email: adminEmail },
        { role: UserRole.ADMIN }
      ]
    });

    if (existingAdmin) {
      console.log('管理员账号已存在！');
      console.log('------------------------');
      console.log('邮箱:', existingAdmin.email);
      console.log('名称:', existingAdmin.name);
      console.log('角色: 管理员');
      await app.close();
      return;
    }

    // 创建管理员用户
    const user = new User();
    user.email = adminEmail;
    user.name = adminName;
    user.password = await bcrypt.hash(adminPassword, 10);
    user.role = UserRole.ADMIN;
    user.status = UserStatus.ACTIVE;

    // 保存到数据库
    const userRepository = dataSource.getRepository(User);
    const savedUser = await userRepository.save(user);

    console.log('管理员账号创建成功:');
    console.log(`ID: ${savedUser.id}`);
    console.log(`邮箱: ${savedUser.email}`);
    console.log(`角色: ${savedUser.role}`);
    console.log(`状态: ${savedUser.status}`);

    // 获取并显示所有用户列表
    const users = await dataSource.getRepository(User).find();
    
    console.log('\n用户列表：');
    console.log('------------------------');
    users.forEach(user => {
      console.log(`ID: ${user.id}`);
      console.log(`邮箱: ${user.email}`);
      console.log(`名称: ${user.name}`);
      console.log(`角色: ${user.role}`);
      console.log(`状态: ${user.status}`);
      console.log('------------------------');
    });

    await app.close();
  } catch (error) {
    console.error('操作失败:', error.message);
    process.exit(1);
  }
}

// 只运行创建管理员的函数
createAdmin();
