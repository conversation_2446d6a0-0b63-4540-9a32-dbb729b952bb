import { CacheModuleOptions } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';

// 缓存键前缀
export const CACHE_PREFIXES = {
  USER: 'user',
  AUTH: 'auth',
  ADMIN: 'admin',
} as const;

// 获取缓存配置
export function getCacheConfig(configService: ConfigService): CacheModuleOptions {
  return {
    ttl: configService.get('cache.ttl', 60), // 默认缓存时间60秒
    max: configService.get('cache.max', 100), // 最大缓存数量
  };
} 