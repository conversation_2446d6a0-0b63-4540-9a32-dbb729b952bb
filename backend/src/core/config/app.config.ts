import { ConfigService } from '@nestjs/config';

export const getAppConfig = (configService: ConfigService) => ({
  port: configService.get('PORT', 3001),
  nodeEnv: configService.get('NODE_ENV', 'development'),
  apiPrefix: configService.get('API_PREFIX', 'api'),
  swaggerPath: configService.get('SWAGGER_PATH', 'api/docs'),
  corsOrigin: configService.get('CORS_ORIGIN', 'http://localhost:3000'),
});

export const appConfig = () => ({
  app: getAppConfig(new ConfigService()),
}); 