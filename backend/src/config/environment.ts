import { z } from 'zod';

// 环境配置验证 schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production']).default('development'),
  PORT: z.string().default('3001'),
  JWT_SECRET: z.string().optional().default('your-secret-key'),
  COOKIE_DOMAIN: z.string().optional(),
  FRONTEND_URL: z.string().optional(),
});

// 验证环境变量
const validateEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('环境变量验证失败:', error);
    process.exit(1);
  }
};

const env = validateEnv();

// CORS 配置类型
type CorsConfig = {
  development: {
    origins: string[];
  };
  production: {
    origins: string[];
  };
};

export const ENV_CONFIG = {
  // 环境判断
  isDevelopment: env.NODE_ENV !== 'production',
  
  // API 配置
  api: {
    port: parseInt(env.PORT),
    prefix: '/api',
  },
  
  // Cookie 配置
  cookie: {
    development: {
      domain: undefined,  // localhost
      secure: false,
      sameSite: 'lax' as const,
    },
    production: {
      domain: env.COOKIE_DOMAIN || '.soultalkai.com',
      secure: true,
      sameSite: 'strict' as const,
    },
    common: {
      httpOnly: true,
      path: '/',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    }
  },
  
  // CORS 配置
  cors: {
    development: {
      origins: [
        'http://localhost:3000',
        'http://localhost:3001',
      ],
    },
    production: {
      origins: [
        env.FRONTEND_URL || 'https://www.soultalkai.com',
        'https://www.soultalkai.com',
        'https://api.soultalkai.com',
        'http://www.soultalkai.com',
        'http://***************',
      ],
    }
  } satisfies CorsConfig,
  
  // JWT 配置
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: '7d',
  }
} as const;

// 类型导出
export type EnvConfig = typeof ENV_CONFIG; 