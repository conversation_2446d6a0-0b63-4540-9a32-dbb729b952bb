import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { writeFileSync } from 'fs';
import { join } from 'path';

async function generateSwaggerJson() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle('IP Navigator API')
    .setDescription('IP Navigator 后端 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  
  // 导出到项目根目录的 shared/types 目录
  const outputPath = join(__dirname, '../../shared/types/swagger.json');
  writeFileSync(outputPath, JSON.stringify(document, null, 2), { encoding: 'utf8' });
  
  console.log(`Swagger JSON 已导出到: ${outputPath}`);
  await app.close();
}

generateSwaggerJson(); 