import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

// 扩展Cache类型以支持两种缓存清除方法
interface ExtendedCache extends Cache {
  delete?: (key: string) => Promise<void>;
}

@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: ExtendedCache) {}

  // 设置缓存
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.cacheManager.set(key, value, ttl);
  }

  // 获取缓存
  async get<T>(key: string): Promise<T | undefined> {
    return this.cacheManager.get<T>(key);
  }

  // 删除缓存
  async del(key: string): Promise<void> {
    try {
      if (typeof this.cacheManager.del === 'function') {
        await this.cacheManager.del(key);
      } else if (typeof this.cacheManager.delete === 'function') {
        await this.cacheManager.delete(key);
      } else {
        console.warn('缓存管理器不支持del或delete方法');
      }
    } catch (error) {
      console.error('清除缓存时出错:', error);
    }
  }

  // 清空所有缓存
  async reset(): Promise<void> {
    await this.cacheManager.reset();
  }

  // 生成缓存键
  generateKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {} as Record<string, any>);

    return `${prefix}:${JSON.stringify(sortedParams)}`;
  }

  // 批量删除指定前缀的缓存
  async delByPrefix(prefix: string): Promise<void> {
    try {
      const keys = await this.cacheManager.store.keys();
      const matchedKeys = keys.filter((key: string) => key.startsWith(prefix));
      
      await Promise.all(
        matchedKeys.map(key => {
          if (typeof this.cacheManager.del === 'function') {
            return this.cacheManager.del(key);
          } else if (typeof this.cacheManager.delete === 'function') {
            return this.cacheManager.delete(key);
          } else {
            console.warn('缓存管理器不支持del或delete方法');
            return Promise.resolve();
          }
        })
      );
    } catch (error) {
      console.error('批量清除缓存时出错:', error);
    }
  }
} 