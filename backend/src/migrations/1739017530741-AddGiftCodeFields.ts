import { MigrationInterface, QueryRunner } from "typeorm";

export class AddG<PERSON><PERSON><PERSON>Fields1739017530741 implements MigrationInterface {
    name = 'AddGiftCodeFields1739017530741'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "course_lessons" DROP CONSTRAINT "fk_course_lessons_course"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "FK_gift_codes_course"`);
        await queryRunner.query(`ALTER TABLE "user_courses" DROP CONSTRAINT "fk_user_courses_user"`);
        await queryRunner.query(`ALTER TABLE "user_courses" DROP CONSTRAINT "fk_user_courses_course"`);
        await queryRunner.query(`DROP INDEX "public"."idx_user_course_unique"`);
        await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT IF EXISTS "courses_level_check"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "membershipLevel"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."users_membershiplevel_enum"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "expires_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "is_used"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "used_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "isUsed" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "usedAt" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "expiresAt" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN IF EXISTS "level"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum" AS ENUM('入门', '进阶', '高级')`);
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum"`);
        await queryRunner.query(`UPDATE "courses" SET "level" = '入门' WHERE "level" IS NULL`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "code" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "user_courses" ALTER COLUMN "completed_lessons" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "user_courses" DROP COLUMN "last_visited_lesson_id"`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD "last_visited_lesson_id" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."orders_order_type_enum" RENAME TO "orders_order_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_order_type_enum" AS ENUM('course')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "order_type" TYPE "public"."orders_order_type_enum" USING "order_type"::"text"::"public"."orders_order_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_order_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "course_lessons" ADD CONSTRAINT "FK_1bb754da7dd104c4a3beb9677c8" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_4bd0232a6e77d9cbc5ca2859a12" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD CONSTRAINT "FK_7ecb10d15b858768c36d37727f9" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD CONSTRAINT "FK_d65a2771413a10753d76937b3d6" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_user_course_unique" ON "user_courses" ("user_id", "course_id")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_courses" DROP CONSTRAINT "FK_d65a2771413a10753d76937b3d6"`);
        await queryRunner.query(`ALTER TABLE "user_courses" DROP CONSTRAINT "FK_7ecb10d15b858768c36d37727f9"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "FK_4bd0232a6e77d9cbc5ca2859a12"`);
        await queryRunner.query(`ALTER TABLE "course_lessons" DROP CONSTRAINT "FK_1bb754da7dd104c4a3beb9677c8"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_order_type_enum_old" AS ENUM('membership', 'extra_quota')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "order_type" TYPE "public"."orders_order_type_enum_old" USING "order_type"::"text"::"public"."orders_order_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."orders_order_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."orders_order_type_enum_old" RENAME TO "orders_order_type_enum"`);
        await queryRunner.query(`ALTER TABLE "user_courses" DROP COLUMN "last_visited_lesson_id"`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD "last_visited_lesson_id" uuid`);
        await queryRunner.query(`ALTER TABLE "user_courses" ALTER COLUMN "completed_lessons" SET DEFAULT '[]'`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "code" character varying(32) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b" UNIQUE ("code")`);
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);
        await queryRunner.query(`DROP TYPE "public"."courses_level_enum"`);
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "expiresAt"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "usedAt"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "isUsed"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "used_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "is_used" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "expires_at" TIMESTAMP`);
        await queryRunner.query(`CREATE TYPE "public"."users_membershiplevel_enum" AS ENUM('free', 'premium')`);
        await queryRunner.query(`ALTER TABLE "users" ADD "membershipLevel" "public"."users_membershiplevel_enum" NOT NULL DEFAULT 'free'`);
        await queryRunner.query(`ALTER TABLE "courses" ADD CONSTRAINT "courses_level_check" CHECK (((level)::text = ANY ((ARRAY['入门'::character varying, '进阶'::character varying, '高级'::character varying])::text[])))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_user_course_unique" ON "user_courses" ("user_id", "course_id")`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD CONSTRAINT "fk_user_courses_course" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_courses" ADD CONSTRAINT "fk_user_courses_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_gift_codes_course" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "course_lessons" ADD CONSTRAINT "fk_course_lessons_course" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
}
