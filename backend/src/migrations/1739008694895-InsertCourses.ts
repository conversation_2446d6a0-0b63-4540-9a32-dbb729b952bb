import { MigrationInterface, QueryRunner } from "typeorm";

export class InsertCourses1739008694895 implements MigrationInterface {
    name = 'InsertCourses1739008694895'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 插入课程数据
        await queryRunner.query(`
            INSERT INTO "courses" (
                "code", "title", "description", "level", "duration", 
                "lessons_count", "price", "is_published"
            ) VALUES 
            (
                'deepseek_productivity_guide',
                'DEEPSEEK的生产力指南',
                '深入探索DEEPSEEK AI助手的高级使用技巧，掌握AI辅助编程、文档处理、代码审查等实用场景，提升开发效率。',
                '入门',
                '6小时',
                8,
                299.00,
                true
            ),
            (
                'prompt_engineering_practice',
                'AI提示词工程实战',
                '系统学习提示词工程的核心原理和最佳实践，掌握结构化提示、上下文管理、提示词模板等高级技巧，提升AI模型输出质量。',
                '进阶',
                '8小时',
                10,
                399.00,
                true
            ),
            (
                'ai_application_development',
                'AI应用开发实践',
                '从实战角度出发，学习如何设计和开发AI驱动的应用，包括LLM应用架构设计、API集成、向量数据库应用、性能优化等进阶主题。',
                '高级',
                '12小时',
                15,
                599.00,
                true
            )
        `);

        // 获取DEEPSEEK课程的ID
        const deepseekCourse = await queryRunner.query(
            `SELECT id FROM courses WHERE code = 'deepseek_productivity_guide' LIMIT 1`
        );
        const deepseekCourseId = deepseekCourse[0].id;

        // 插入DEEPSEEK课程的章节数据
        await queryRunner.query(`
            INSERT INTO "course_lessons" (
                "course_id", "title", "order", "duration", "description", "script_path"
            ) VALUES 
            (
                '${deepseekCourseId}',
                '认识DEEPSEEK',
                1,
                '45分钟',
                'DEEPSEEK简介和特点、与其他AI助手的对比、核心优势、如何访问和使用DEEPSEEK',
                'course_deepseek_productivity_guide/chapter1.md'
            ),
            (
                '${deepseekCourseId}',
                '基础使用技巧',
                2,
                '45分钟',
                '界面功能介绍、提示词的基本结构、对话上下文管理、常见错误和解决方案',
                'course_deepseek_productivity_guide/chapter2.md'
            ),
            (
                '${deepseekCourseId}',
                '代码开发助手',
                3,
                '45分钟',
                '代码补全和建议、代码重构和优化、代码审查和调试、单元测试生成',
                'course_deepseek_productivity_guide/chapter3.md'
            ),
            (
                '${deepseekCourseId}',
                '文档处理专家',
                4,
                '45分钟',
                '技术文档编写、API文档生成、注释优化、Markdown格式处理',
                'course_deepseek_productivity_guide/chapter4.md'
            ),
            (
                '${deepseekCourseId}',
                '项目管理助手',
                5,
                '45分钟',
                '需求分析和拆解、任务规划和估算、代码架构设计、技术方案撰写',
                'course_deepseek_productivity_guide/chapter5.md'
            ),
            (
                '${deepseekCourseId}',
                '学习和进阶指南',
                6,
                '45分钟',
                '编程概念解释、源码分析和学习、最佳实践建议、性能优化指导',
                'course_deepseek_productivity_guide/chapter6.md'
            ),
            (
                '${deepseekCourseId}',
                '团队协作增强',
                7,
                '45分钟',
                '代码评审辅助、技术文档协作、知识库建设、团队培训支持',
                'course_deepseek_productivity_guide/chapter7.md'
            ),
            (
                '${deepseekCourseId}',
                '高级应用场景',
                8,
                '45分钟',
                '复杂项目开发、系统重构指导、性能调优建议、安全审计支持',
                'course_deepseek_productivity_guide/chapter8.md'
            )
        `);

        // 为每个课程插入章节数据
        // DEEPSEEK的生产力指南
        await queryRunner.query(`
            INSERT INTO "course_lessons" (
                "course_id", "title", "order", "duration", "description", "script_path"
            )
            SELECT 
                c.id,
                unnest(ARRAY[
                    '课程介绍与环境准备',
                    'DEEPSEEK基础功能详解',
                    'AI辅助编程最佳实践',
                    '智能代码审查技巧',
                    '自动化文档生成',
                    '调试与问题排查',
                    '工作流程优化',
                    '实战案例分析'
                ]) as title,
                unnest(ARRAY[1, 2, 3, 4, 5, 6, 7, 8]) as order,
                unnest(ARRAY[
                    '30分钟', '45分钟', '60分钟', '45分钟',
                    '45分钟', '45分钟', '45分钟', '45分钟'
                ]) as duration,
                unnest(ARRAY[
                    '了解课程内容，准备开发环境',
                    '掌握DEEPSEEK的核心功能和基本用法',
                    '学习如何使用AI提升编程效率',
                    '使用AI进行代码审查和优化',
                    '自动生成技术文档和注释',
                    '使用AI辅助调试和解决问题',
                    '优化日常开发工作流程',
                    '实际项目中的应用案例'
                ]) as description,
                unnest(ARRAY[
                    'deepseek_guide/lesson_1.md',
                    'deepseek_guide/lesson_2.md',
                    'deepseek_guide/lesson_3.md',
                    'deepseek_guide/lesson_4.md',
                    'deepseek_guide/lesson_5.md',
                    'deepseek_guide/lesson_6.md',
                    'deepseek_guide/lesson_7.md',
                    'deepseek_guide/lesson_8.md'
                ]) as script_path
            FROM "courses" c
            WHERE c.code = 'deepseek_productivity_guide'
        `);

        // AI提示词工程实战
        await queryRunner.query(`
            INSERT INTO "course_lessons" (
                "course_id", "title", "order", "duration", "description", "script_path"
            )
            SELECT 
                c.id,
                unnest(ARRAY[
                    '提示词工程概述',
                    '提示词的结构与组成',
                    '上下文管理策略',
                    '提示词模板设计',
                    '角色与人设定制',
                    '多轮对话优化',
                    '提示词调试技巧',
                    '领域特定提示词',
                    '提示词性能优化',
                    '实战项目演练'
                ]) as title,
                unnest(ARRAY[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]) as order,
                unnest(ARRAY[
                    '45分钟', '45分钟', '45分钟', '45分钟', '45分钟',
                    '45分钟', '45分钟', '45分钟', '45分钟', '60分钟'
                ]) as duration,
                unnest(ARRAY[
                    '理解提示词工程的基本概念和重要性',
                    '学习提示词的基本结构和组成部分',
                    '掌握上下文信息的有效管理方法',
                    '设计可复用的提示词模板',
                    '创建有效的AI角色和人设',
                    '优化多轮对话的质量和连贯性',
                    '学习提示词的调试和优化方法',
                    '针对特定领域的提示词设计',
                    '提高提示词的性能和效率',
                    '综合运用所学知识完成实战项目'
                ]) as description,
                unnest(ARRAY[
                    'prompt_engineering/lesson_1.md',
                    'prompt_engineering/lesson_2.md',
                    'prompt_engineering/lesson_3.md',
                    'prompt_engineering/lesson_4.md',
                    'prompt_engineering/lesson_5.md',
                    'prompt_engineering/lesson_6.md',
                    'prompt_engineering/lesson_7.md',
                    'prompt_engineering/lesson_8.md',
                    'prompt_engineering/lesson_9.md',
                    'prompt_engineering/lesson_10.md'
                ]) as script_path
            FROM "courses" c
            WHERE c.code = 'prompt_engineering_practice'
        `);

        // AI应用开发实践
        await queryRunner.query(`
            INSERT INTO "course_lessons" (
                "course_id", "title", "order", "duration", "description", "script_path"
            )
            SELECT 
                c.id,
                unnest(ARRAY[
                    'AI应用架构设计',
                    'LLM API集成基础',
                    '向量数据库应用',
                    '流式响应处理',
                    '多模态AI集成',
                    '提示词管理系统',
                    '上下文记忆设计',
                    '性能监控与优化',
                    '安全性与隐私保护',
                    ' 错误处理与重试',
                    '缓存策略设计',
                    '成本控制与优化',
                    '系统扩展性设计',
                    'AI功能测试方法',
                    '项目实战演练'
                ]) as title,
                unnest(ARRAY[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]) as order,
                unnest(ARRAY[
                    '45分钟', '45分钟', '45分钟', '45分钟', '45分钟',
                    '45分钟', '45分钟', '45分钟', '45分钟', '45分钟',
                    '45分钟', '45分钟', '45分钟', '45分钟', '60分钟'
                ]) as duration,
                unnest(ARRAY[
                    '设计可扩展的AI应用架构',
                    '集成各种LLM API的基础知识',
                    '使用向量数据库增强AI应用',
                    '实现流式响应的处理机制',
                    '集成图像、语音等多模态AI能力',
                    '构建提示词的管理系统',
                    '设计高效的上下文记忆机制',
                    '监控和优化AI应用性能',
                    '确保AI应用的安全性和隐私',
                    '处理API错误和实现重试机制',
                    '设计高效的缓存策略',
                    '控制和优化API调用成本',
                    '设计可扩展的系统架构',
                    '编写AI功能的测试用例',
                    '综合运用所学知识开发实战项目'
                ]) as description,
                unnest(ARRAY[
                    'ai_development/lesson_1.md',
                    'ai_development/lesson_2.md',
                    'ai_development/lesson_3.md',
                    'ai_development/lesson_4.md',
                    'ai_development/lesson_5.md',
                    'ai_development/lesson_6.md',
                    'ai_development/lesson_7.md',
                    'ai_development/lesson_8.md',
                    'ai_development/lesson_9.md',
                    'ai_development/lesson_10.md',
                    'ai_development/lesson_11.md',
                    'ai_development/lesson_12.md',
                    'ai_development/lesson_13.md',
                    'ai_development/lesson_14.md',
                    'ai_development/lesson_15.md'
                ]) as script_path
            FROM "courses" c
            WHERE c.code = 'ai_application_development'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除所有课程章节
        await queryRunner.query(`DELETE FROM "course_lessons"`);
        // 删除所有课程
        await queryRunner.query(`DELETE FROM "courses"`);
    }
}
