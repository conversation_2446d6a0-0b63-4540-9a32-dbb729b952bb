import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUniqueCourseLesson1739017531000 implements MigrationInterface {
    name = 'AddUniqueCourseLesson1739017531000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 先删除重复的章节记录，保留最新的一条
        await queryRunner.query(`
            DELETE FROM course_lessons
            WHERE id IN (
                SELECT id
                FROM (
                    SELECT id,
                           ROW_NUMBER() OVER (PARTITION BY course_id, "order" ORDER BY created_at DESC) as rn
                    FROM course_lessons
                ) t
                WHERE t.rn > 1
            );
        `);

        // 添加联合唯一约束
        await queryRunner.query(`
            ALTER TABLE course_lessons
            ADD CONSTRAINT "UQ_course_lesson_order" UNIQUE ("course_id", "order");
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除联合唯一约束
        await queryRunner.query(`
            ALTER TABLE course_lessons
            DROP CONSTRAINT "UQ_course_lesson_order";
        `);
    }
} 