import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateGiftCodeTable1739017038931 implements MigrationInterface {
    name = 'UpdateGiftCodeTable1739017038931'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. 先删除旧的礼品码数据
        await queryRunner.query(`DELETE FROM "gift_codes"`);

        // 2. 删除旧的列
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "plan_type"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "duration_days"`);

        // 3. 添加新的列
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "course_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "is_used" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "used_at" TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "expires_at" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "valid_days" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN IF NOT EXISTS "used_by" uuid`);

        // 4. 添加外键约束
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_gift_codes_course" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_gift_codes_user" FOREIGN KEY ("used_by") REFERENCES "users"("id") ON DELETE SET NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 1. 删除外键约束
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT IF EXISTS "FK_gift_codes_course"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT IF EXISTS "FK_gift_codes_user"`);
        
        // 2. 删除新添加的列
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "course_id"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "is_used"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "used_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "expires_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "valid_days"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "created_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "updated_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN IF EXISTS "used_by"`);

        // 3. 恢复旧的列
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN "plan_type" varchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD COLUMN "duration_days" integer NOT NULL`);
    }
}
