import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCourseLevelToEnglish1752307200000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建新的英文枚举类型
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum_english" AS ENUM ('Emotional Guidance', 'Relationship Support', 'Personal Growth')`);

        // 添加临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_temp" "public"."courses_level_enum_english"`);

        // 数据迁移：将中文值映射到英文值
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level_temp" = CASE 
                WHEN "level" = '情绪疏导' THEN 'Emotional Guidance'::courses_level_enum_english
                WHEN "level" = '关系咨询' THEN 'Relationship Support'::courses_level_enum_english  
                WHEN "level" = '个人成长' THEN 'Personal Growth'::courses_level_enum_english
                ELSE 'Emotional Guidance'::courses_level_enum_english
            END
        `);

        // 删除旧列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 删除旧枚举类型
        await queryRunner.query(`DROP TYPE "public"."courses_level_enum"`);

        // 重命名新列
        await queryRunner.query(`ALTER TABLE "courses" RENAME COLUMN "level_temp" TO "level"`);

        // 重命名新枚举类型
        await queryRunner.query(`ALTER TYPE "public"."courses_level_enum_english" RENAME TO "courses_level_enum"`);

        // 设置非空约束
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 创建旧的中文枚举类型
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum_chinese" AS ENUM ('情绪疏导', '关系咨询', '个人成长')`);

        // 添加临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_temp" "public"."courses_level_enum_chinese"`);

        // 数据迁移：将英文值映射回中文值
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level_temp" = CASE 
                WHEN "level" = 'Emotional Guidance' THEN '情绪疏导'::courses_level_enum_chinese
                WHEN "level" = 'Relationship Support' THEN '关系咨询'::courses_level_enum_chinese
                WHEN "level" = 'Personal Growth' THEN '个人成长'::courses_level_enum_chinese
                ELSE '情绪疏导'::courses_level_enum_chinese
            END
        `);

        // 删除旧列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 删除旧枚举类型
        await queryRunner.query(`DROP TYPE "public"."courses_level_enum"`);

        // 重命名新列
        await queryRunner.query(`ALTER TABLE "courses" RENAME COLUMN "level_temp" TO "level"`);

        // 重命名新枚举类型
        await queryRunner.query(`ALTER TYPE "public"."courses_level_enum_chinese" RENAME TO "courses_level_enum"`);

        // 设置非空约束
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" SET NOT NULL`);
    }
}