import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionPlansAndUpdateSubscriptions1752400000000 implements MigrationInterface {
  name = 'CreateSubscriptionPlansAndUpdateSubscriptions1752400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create subscription_plans table
    await queryRunner.query(`
      CREATE TABLE "subscription_plans" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "description" text,
        "price" numeric(10,2) NOT NULL,
        "type" character varying NOT NULL,
        "duration_days" integer NOT NULL,
        "ai_call_limit" integer NOT NULL,
        "max_courses" integer,
        "trial_days" integer NOT NULL DEFAULT 0,
        "status" character varying NOT NULL DEFAULT 'active',
        "sort_order" integer NOT NULL DEFAULT 0,
        "features" json,
        "stripe_price_id" character varying,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_subscription_plans_name" UNIQUE ("name"),
        CONSTRAINT "PK_subscription_plans" PRIMARY KEY ("id")
      )
    `);

    // Add CHECK constraints for enums
    await queryRunner.query(`
      ALTER TABLE "subscription_plans" 
      ADD CONSTRAINT "CHK_subscription_plan_type" 
      CHECK ("type" IN ('monthly', 'quarterly', 'yearly'))
    `);

    await queryRunner.query(`
      ALTER TABLE "subscription_plans" 
      ADD CONSTRAINT "CHK_subscription_plan_status" 
      CHECK ("status" IN ('active', 'inactive', 'archived'))
    `);

    // Add new columns to subscriptions table
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      ADD COLUMN "plan_id" uuid,
      ADD COLUMN "status" character varying NOT NULL DEFAULT 'active',
      ADD COLUMN "stripe_subscription_id" character varying,
      ADD COLUMN "auto_renew" boolean NOT NULL DEFAULT true,
      ADD COLUMN "trial_end_date" timestamp,
      ADD COLUMN "cancelled_at" timestamp,
      ADD COLUMN "next_billing_date" timestamp
    `);

    // Add CHECK constraint for subscription status
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      ADD CONSTRAINT "CHK_subscription_status" 
      CHECK ("status" IN ('active', 'inactive', 'cancelled', 'expired', 'trial'))
    `);

    // Add foreign key constraint for plan_id
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      ADD CONSTRAINT "FK_subscriptions_plan_id" 
      FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE SET NULL
    `);

    // Add subscription_id column to orders table
    await queryRunner.query(`
      ALTER TABLE "orders" 
      ADD COLUMN "subscription_id" uuid
    `);

    // Update order_type enum to include subscription
    await queryRunner.query(`
      ALTER TYPE "orders_order_type_enum" ADD VALUE 'subscription'
    `);

    // Add foreign key constraint for subscription_id
    await queryRunner.query(`
      ALTER TABLE "orders" 
      ADD CONSTRAINT "FK_orders_subscription_id" 
      FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE SET NULL
    `);

    // Insert default subscription plans
    await queryRunner.query(`
      INSERT INTO "subscription_plans" ("name", "description", "price", "type", "duration_days", "ai_call_limit", "max_courses", "trial_days", "features", "sort_order") VALUES
      ('Basic Monthly', 'Basic monthly subscription plan', 29.99, 'monthly', 30, 1000, NULL, 7, '["Unlimited course access", "1000 AI conversations", "7-day free trial"]', 1),
      ('Standard Quarterly', 'Standard quarterly subscription plan', 79.99, 'quarterly', 90, 3500, NULL, 7, '["Unlimited course access", "3500 AI conversations", "7-day free trial", "Priority customer support"]', 2),
      ('Premium Yearly', 'Premium yearly subscription plan', 299.99, 'yearly', 365, 15000, NULL, 14, '["Unlimited course access", "15000 AI conversations", "14-day free trial", "Priority customer support", "Exclusive content access"]', 3)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_subscription_id"`);
    await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_plan_id"`);

    // Remove columns from orders table
    await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "subscription_id"`);

    // Note: PostgreSQL doesn't support removing enum values easily
    // This would require recreating the enum type, which is complex
    // For now, we'll leave the enum value in place

    // Remove columns from subscriptions table
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      DROP COLUMN "plan_id",
      DROP COLUMN "status",
      DROP COLUMN "stripe_subscription_id",
      DROP COLUMN "auto_renew",
      DROP COLUMN "trial_end_date",
      DROP COLUMN "cancelled_at",
      DROP COLUMN "next_billing_date"
    `);

    // Drop subscription_plans table
    await queryRunner.query(`DROP TABLE "subscription_plans"`);
  }
}