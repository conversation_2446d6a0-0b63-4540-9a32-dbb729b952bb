import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCourseLevelToVarchar1740208777509 implements MigrationInterface {
    name = 'UpdateCourseLevelToVarchar1740208777509'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_new" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_new" = "level"::text`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 添加新列并设置为字符串类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" character varying NOT NULL DEFAULT '综合基础'`);

        // 更新数据
        await queryRunner.query(`UPDATE "courses" SET "level" = "level_new"`);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_new"`);

        // 删除枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_old" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_old" = "level"`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 创建枚举类型
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum" AS ENUM ('综合基础', '专项进阶', '技术高阶')`);

        // 添加新列并设置为枚举类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum" NOT NULL DEFAULT '综合基础'`);

        // 更新数据
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level" = CASE 
                WHEN "level_old" = '综合基础' THEN '综合基础'::courses_level_enum
                WHEN "level_old" = '专项进阶' THEN '专项进阶'::courses_level_enum
                WHEN "level_old" = '技术高阶' THEN '技术高阶'::courses_level_enum
                ELSE '综合基础'::courses_level_enum
            END
        `);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_old"`);
    }
} 