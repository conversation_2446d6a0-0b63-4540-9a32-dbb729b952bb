import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1739005993344 implements MigrationInterface {
    name = 'InitialSchema1739005993344'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('user', 'admin')`);
        await queryRunner.query(`CREATE TYPE "public"."users_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "password" character varying NOT NULL, "name" character varying, "avatar" character varying, "role" "public"."users_role_enum" NOT NULL DEFAULT 'user', "status" "public"."users_status_enum" NOT NULL DEFAULT 'active', "loginCount" integer NOT NULL DEFAULT '0', "lastLoginAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "subscriptions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userid" uuid NOT NULL, "planname" character varying NOT NULL, "price" numeric(10,2) NOT NULL, "startdate" TIMESTAMP NOT NULL, "enddate" TIMESTAMP NOT NULL, "aicalllimit" integer NOT NULL, "aicallused" integer NOT NULL DEFAULT '0', "isactive" boolean NOT NULL DEFAULT true, "createdat" TIMESTAMP NOT NULL DEFAULT now(), "updatedat" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_a87248d73155605cf782be9ee5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."ai_providers_type_enum" AS ENUM('openai', 'azure_openai', 'anthropic', 'moonshot', 'gemini', 'volcengine', 'mistral', 'deepseek', 'flux', 'siliconflow', 'custom')`);
        await queryRunner.query(`CREATE TYPE "public"."ai_providers_status_enum" AS ENUM('active', 'inactive', 'testing')`);
        await queryRunner.query(`CREATE TYPE "public"."ai_providers_defaulttype_enum" AS ENUM('text', 'image')`);
        await queryRunner.query(`CREATE TABLE "ai_providers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "type" "public"."ai_providers_type_enum" NOT NULL, "status" "public"."ai_providers_status_enum" NOT NULL DEFAULT 'active', "apiKey" character varying NOT NULL, "secretKey" character varying, "apiEndpoint" character varying, "requestCount" integer NOT NULL DEFAULT '0', "config" jsonb, "costAmount" numeric(10,2) NOT NULL DEFAULT '0', "defaultType" "public"."ai_providers_defaulttype_enum", "lastTestAt" TIMESTAMP, "lastTestResult" jsonb, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_de28ebefc0fb425c37b27a4c0a7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "gift_codes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying(32) NOT NULL, "plan_type" character varying(20) NOT NULL, "duration_days" integer NOT NULL, "expires_at" TIMESTAMP, "is_used" boolean NOT NULL DEFAULT false, "used_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "used_by" uuid, CONSTRAINT "UQ_2ab475f14dfa3594af8a80c2e9b" UNIQUE ("code"), CONSTRAINT "PK_a18bf56cdcac2e27f2d22bc2f69" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."orders_order_type_enum" AS ENUM('course', 'extra_quota')`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('pending', 'paid', 'cancelled', 'refunded', 'expired')`);
        await queryRunner.query(`CREATE TABLE "orders" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
            "order_no" character varying(32) NOT NULL,
            "user_id" uuid NOT NULL,
            "order_type" "public"."orders_order_type_enum" NOT NULL,
            "product_name" character varying NOT NULL,
            "amount" numeric(10,2) NOT NULL,
            "payment_method" character varying,
            "transaction_id" character varying,
            "status" "public"."orders_status_enum" NOT NULL DEFAULT 'pending',
            "metadata" jsonb,
            "paid_at" TIMESTAMP,
            "expires_at" TIMESTAMP,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "UQ_035026a83bef9740d7ad05df383" UNIQUE ("order_no"),
            CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id")
        )`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_order_type_enum"`);
        await queryRunner.query(`DROP TABLE "gift_codes"`);
        await queryRunner.query(`DROP TYPE "public"."ai_providers_defaulttype_enum"`);
        await queryRunner.query(`DROP TYPE "public"."ai_providers_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."ai_providers_type_enum"`);
        await queryRunner.query(`DROP TABLE "ai_providers"`);
        await queryRunner.query(`DROP TABLE "subscriptions"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    }
}