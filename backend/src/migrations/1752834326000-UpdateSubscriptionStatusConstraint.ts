import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSubscriptionStatusConstraint1752834326000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 删除旧的约束
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      DROP CONSTRAINT "CHK_subscription_status"
    `);
    
    // 添加新的约束，包含 'pending' 状态
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      ADD CONSTRAINT "CHK_subscription_status" 
      CHECK ("status" IN ('pending', 'active', 'inactive', 'cancelled', 'expired', 'trial'))
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除新的约束
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      DROP CONSTRAINT "CHK_subscription_status"
    `);
    
    // 恢复旧的约束
    await queryRunner.query(`
      ALTER TABLE "subscriptions" 
      ADD CONSTRAINT "CHK_subscription_status" 
      CHECK ("status" IN ('active', 'inactive', 'cancelled', 'expired', 'trial'))
    `);
  }
}