import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCourseLevelEnum1740208777508 implements MigrationInterface {
    name = 'UpdateCourseLevelEnum1740208777508'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_new" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_new" = "level"::text`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 创建新的枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum" AS ENUM ('综合基础', '专项进阶', '技术高阶')`);

        // 添加新列并设置为新的枚举类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum" NOT NULL DEFAULT '综合基础'`);

        // 更新数据
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level" = CASE 
                WHEN "level_new"::text = '入门' THEN '综合基础'::courses_level_enum
                WHEN "level_new"::text = '进阶' THEN '专项进阶'::courses_level_enum
                WHEN "level_new"::text = '高级' THEN '技术高阶'::courses_level_enum
                ELSE '综合基础'::courses_level_enum
            END
        `);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_new"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_old" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_old" = "level"::text`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 创建旧的枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum_old" AS ENUM ('入门', '进阶', '高级')`);

        // 添加新列并设置为旧的枚举类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum_old" NOT NULL DEFAULT '入门'`);

        // 更新数据
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level" = CASE 
                WHEN "level_old"::text = '综合基础' THEN '入门'::courses_level_enum_old
                WHEN "level_old"::text = '专项进阶' THEN '进阶'::courses_level_enum_old
                WHEN "level_old"::text = '技术高阶' THEN '高级'::courses_level_enum_old
                ELSE '入门'::courses_level_enum_old
            END
        `);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_old"`);

        // 删除新的枚举类型
        await queryRunner.query(`DROP TYPE "public"."courses_level_enum"`);
    }
}
