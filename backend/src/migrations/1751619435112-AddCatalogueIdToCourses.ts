import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCatalogueIdToCourses1751619435112 implements MigrationInterface {
    name = 'AddCatalogueIdToCourses1751619435112'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 添加catalogue_id列
        await queryRunner.query(`ALTER TABLE "courses" ADD "catalogue_id" uuid`);
        
        // 添加外键约束
        await queryRunner.query(`ALTER TABLE "courses" ADD CONSTRAINT "FK_courses_catalogue_id" FOREIGN KEY ("catalogue_id") REFERENCES "catalogue"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除外键约束
        await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_catalogue_id"`);
        
        // 删除catalogue_id列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "catalogue_id"`);
    }
} 