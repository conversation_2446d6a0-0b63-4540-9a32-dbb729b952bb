import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateCourseLevelEnumToHealingTypes1740208777510 implements MigrationInterface {
    name = 'UpdateCourseLevelEnumToHealingTypes1740208777510'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_new" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_new" = "level"::text`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 创建新的枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum" AS ENUM ('情绪疏导', '关系咨询', '个人成长')`);

        // 添加新列并设置为新的枚举类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum" NOT NULL DEFAULT '情绪疏导'`);

        // 更新数据
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level" = CASE 
                WHEN "level_new"::text = '综合基础' THEN '情绪疏导'::courses_level_enum
                WHEN "level_new"::text = '专项进阶' THEN '关系咨询'::courses_level_enum
                WHEN "level_new"::text = '技术高阶' THEN '个人成长'::courses_level_enum
                ELSE '情绪疏导'::courses_level_enum
            END
        `);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_new"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 创建临时列
        await queryRunner.query(`ALTER TABLE "courses" ADD "level_old" character varying`);

        // 直接将当前值复制到临时列
        await queryRunner.query(`UPDATE "courses" SET "level_old" = "level"::text`);

        // 删除原有列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level"`);

        // 创建旧的枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."courses_level_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum" AS ENUM ('综合基础', '专项进阶', '技术高阶')`);

        // 添加新列并设置为旧的枚举类型
        await queryRunner.query(`ALTER TABLE "courses" ADD "level" "public"."courses_level_enum" NOT NULL DEFAULT '综合基础'`);

        // 更新数据
        await queryRunner.query(`
            UPDATE "courses" 
            SET "level" = CASE 
                WHEN "level_old"::text = '情绪疏导' THEN '综合基础'::courses_level_enum
                WHEN "level_old"::text = '关系咨询' THEN '专项进阶'::courses_level_enum
                WHEN "level_old"::text = '个人成长' THEN '技术高阶'::courses_level_enum
                ELSE '综合基础'::courses_level_enum
            END
        `);

        // 删除临时列
        await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "level_old"`);
    }
} 