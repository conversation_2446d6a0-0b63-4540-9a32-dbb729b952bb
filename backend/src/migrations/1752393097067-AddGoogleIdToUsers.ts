import { MigrationInterface, QueryRunner } from "typeorm";

export class AddGoogleIdToUsers1752393097067 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD COLUMN "googleId" character varying,
            ALTER COLUMN "password" DROP NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" 
            DROP COLUMN "googleId",
            ALTER COLUMN "password" SET NOT NULL
        `);
    }

}
