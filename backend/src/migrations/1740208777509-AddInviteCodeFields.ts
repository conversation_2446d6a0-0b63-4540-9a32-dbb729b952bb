import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInviteCodeFields1740208777509 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加type字段，默认为'gift'
    await queryRunner.query(`
      ALTER TABLE gift_codes
      ADD COLUMN IF NOT EXISTS type VARCHAR(10) NOT NULL DEFAULT 'gift'
    `);

    // 添加creator_id字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      ADD COLUMN IF NOT EXISTS creator_id UUID NULL
    `);

    // 检查约束是否已存在
    const constraintExists = await queryRunner.query(`
      SELECT 1 FROM pg_constraint 
      WHERE conname = 'fk_gift_codes_creator' 
      LIMIT 1
    `);

    // 如果约束不存在，则添加外键约束
    if (constraintExists.length === 0) {
      await queryRunner.query(`
        ALTER TABLE gift_codes
        ADD CONSTRAINT fk_gift_codes_creator
        FOREIGN KEY (creator_id) REFERENCES users(id)
        ON DELETE SET NULL
      `);
    }

    // 添加邀请相关信息字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      ADD COLUMN IF NOT EXISTS friend_name VARCHAR(100) NULL,
      ADD COLUMN IF NOT EXISTS invite_reason TEXT NULL,
      ADD COLUMN IF NOT EXISTS user_name VARCHAR(100) NULL
    `);

    // 添加用户信息字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      ADD COLUMN IF NOT EXISTS profession VARCHAR(100) NULL,
      ADD COLUMN IF NOT EXISTS ai_experience VARCHAR(50) NULL,
      ADD COLUMN IF NOT EXISTS interests TEXT NULL,
      ADD COLUMN IF NOT EXISTS expectation TEXT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除用户信息字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      DROP COLUMN IF EXISTS expectation,
      DROP COLUMN IF EXISTS interests,
      DROP COLUMN IF EXISTS ai_experience,
      DROP COLUMN IF EXISTS profession
    `);

    // 删除邀请相关信息字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      DROP COLUMN IF EXISTS user_name,
      DROP COLUMN IF EXISTS invite_reason,
      DROP COLUMN IF EXISTS friend_name
    `);

    // 检查约束是否存在
    const constraintExists = await queryRunner.query(`
      SELECT 1 FROM pg_constraint 
      WHERE conname = 'fk_gift_codes_creator' 
      LIMIT 1
    `);

    // 如果约束存在，则删除外键约束
    if (constraintExists.length > 0) {
      await queryRunner.query(`
        ALTER TABLE gift_codes
        DROP CONSTRAINT fk_gift_codes_creator
      `);
    }

    // 删除creator_id字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      DROP COLUMN IF EXISTS creator_id
    `);

    // 删除type字段
    await queryRunner.query(`
      ALTER TABLE gift_codes
      DROP COLUMN IF EXISTS type
    `);
  }
} 