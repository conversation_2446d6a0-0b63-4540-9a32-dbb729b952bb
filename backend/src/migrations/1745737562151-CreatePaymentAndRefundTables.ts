import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePaymentAndRefundTables1745737562151 implements MigrationInterface {
    name = 'CreatePaymentAndRefundTables1745737562151'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建支付状态枚举类型
        await queryRunner.query(`
            CREATE TYPE "public"."payments_status_enum" AS ENUM (
                'pending',     -- 待支付
                'success',     -- 支付成功
                'failed',      -- 支付失败
                'refunded'     -- 已退款
            )
        `);

        // 创建退款状态枚举类型
        await queryRunner.query(`
            CREATE TYPE "public"."refunds_status_enum" AS ENUM (
                'pending',     -- 待处理
                'success',     -- 退款成功
                'failed'       -- 退款失败
            )
        `);

        // 创建支付记录表
        await queryRunner.query(`
            CREATE TABLE "payments" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "order_id" uuid NOT NULL,
                "payment_method" character varying NOT NULL,
                "transaction_id" character varying,
                "amount" numeric(10,2) NOT NULL,
                "status" "public"."payments_status_enum" NOT NULL DEFAULT 'pending',
                "metadata" jsonb,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_payments_id" PRIMARY KEY ("id"),
                CONSTRAINT "FK_payments_order" FOREIGN KEY ("order_id") 
                    REFERENCES "orders"("id") ON DELETE CASCADE
            )
        `);

        // 创建退款记录表
        await queryRunner.query(`
            CREATE TABLE "refunds" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "order_id" uuid NOT NULL,
                "refund_no" character varying(32) NOT NULL,
                "amount" numeric(10,2) NOT NULL,
                "status" "public"."refunds_status_enum" NOT NULL DEFAULT 'pending',
                "reason" text,
                "metadata" jsonb,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_refunds_id" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_refunds_refund_no" UNIQUE ("refund_no"),
                CONSTRAINT "FK_refunds_order" FOREIGN KEY ("order_id") 
                    REFERENCES "orders"("id") ON DELETE CASCADE
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除退款记录表
        await queryRunner.query(`DROP TABLE IF EXISTS "refunds"`);
        // 删除支付记录表
        await queryRunner.query(`DROP TABLE IF EXISTS "payments"`);
        // 删除退款状态枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."refunds_status_enum"`);
        // 删除支付状态枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."payments_status_enum"`);
    }
} 