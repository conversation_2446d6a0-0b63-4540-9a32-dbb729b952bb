import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCategoryToCourse1746000000001 implements MigrationInterface {
  name = 'AddCategoryToCourse1746000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加 category_id 字段到 courses 表
    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD COLUMN "category_id" uuid
    `);

    // 添加外键约束
    await queryRunner.query(`
      ALTER TABLE "courses" 
      ADD CONSTRAINT "FK_courses_category_id" 
      FOREIGN KEY ("category_id") REFERENCES "catalogue"("id") 
      ON DELETE SET NULL
    `);

    // 创建索引以提高查询性能
    await queryRunner.query(`
      CREATE INDEX "IDX_courses_category_id" 
      ON "courses"("category_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除索引
    await queryRunner.query(`DROP INDEX "IDX_courses_category_id"`);
    
    // 删除外键约束
    await queryRunner.query(`ALTER TABLE "courses" DROP CONSTRAINT "FK_courses_category_id"`);
    
    // 删除字段
    await queryRunner.query(`ALTER TABLE "courses" DROP COLUMN "category_id"`);
  }
}
