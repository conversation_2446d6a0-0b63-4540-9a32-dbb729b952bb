import { MigrationInterface, QueryRunner } from "typeorm";

export class InsertInitialCatalogues1751619435113 implements MigrationInterface {
    name = 'InsertInitialCatalogues1751619435113'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 先检查是否已经有数据，如果没有才插入
        const existingData = await queryRunner.query(`SELECT COUNT(*) as count FROM "catalogue"`);
        
        if (existingData[0].count === '0') {
            // 插入初始分类数据
            await queryRunner.query(`
                INSERT INTO "catalogue" ("name", "description") VALUES 
                ('情绪疏导', '帮助处理情绪问题，提供心理支持和引导'),
                ('关系咨询', '处理人际关系问题，改善沟通技巧'),
                ('个人成长', '促进个人发展，提升自我认知和能力'),
                ('压力管理', '学习压力应对技巧，保持心理健康'),
                ('生活指导', '提供生活建议，优化生活方式')
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除插入的分类数据
        await queryRunner.query(`
            DELETE FROM "catalogue" WHERE "name" IN (
                '情绪疏导', '关系咨询', '个人成长', '压力管理', '生活指导'
            )
        `);
    }
} 