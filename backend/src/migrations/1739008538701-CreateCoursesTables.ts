import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCoursesTables1739008538701 implements MigrationInterface {
    name = 'CreateCoursesTables1739008538701'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 创建课程表
        await queryRunner.query(`
            CREATE TABLE "courses" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "code" varchar NOT NULL UNIQUE,
                "title" varchar NOT NULL,
                "description" text NOT NULL,
                "level" varchar NOT NULL CHECK (level IN ('入门', '进阶', '高级')),
                "duration" varchar NOT NULL,
                "lessons_count" integer NOT NULL,
                "price" decimal(10,2) NOT NULL,
                "is_published" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now()
            )
        `);

        // 创建课程章节表
        await queryRunner.query(`
            CREATE TABLE "course_lessons" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "course_id" uuid NOT NULL,
                "title" varchar NOT NULL,
                "order" integer NOT NULL,
                "duration" varchar NOT NULL,
                "description" text NOT NULL,
                "script_path" varchar NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "fk_course_lessons_course" FOREIGN KEY ("course_id") 
                    REFERENCES "courses"("id") ON DELETE CASCADE
            )
        `);

        // 创建用户课程关联表
        await queryRunner.query(`
            CREATE TABLE "user_courses" (
                "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "course_id" uuid NOT NULL,
                "progress" integer NOT NULL DEFAULT 0,
                "completed_lessons" jsonb DEFAULT '[]',
                "last_visited_lesson_id" uuid,
                "purchased_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "fk_user_courses_user" FOREIGN KEY ("user_id") 
                    REFERENCES "users"("id") ON DELETE CASCADE,
                CONSTRAINT "fk_user_courses_course" FOREIGN KEY ("course_id") 
                    REFERENCES "courses"("id") ON DELETE CASCADE
            )
        `);

        // 创建唯一索引确保用户不能重复购买同一课程
        await queryRunner.query(`
            CREATE UNIQUE INDEX "idx_user_course_unique" 
            ON "user_courses"("user_id", "course_id")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX IF EXISTS "idx_user_course_unique"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_courses"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "course_lessons"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "courses"`);
    }
}
