import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCourseIdToOrder1745737562150 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ADD COLUMN "course_id" uuid`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_course_id" FOREIGN KEY ("course_id") REFERENCES "courses"("id")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_course_id"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "course_id"`);
    }

}
