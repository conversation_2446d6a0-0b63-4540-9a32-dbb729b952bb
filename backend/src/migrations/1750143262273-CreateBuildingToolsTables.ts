import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateBuildingToolsTables1750143262273 implements MigrationInterface {
    name = 'CreateBuildingToolsTables1750143262273'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 先创建枚举类型
        await queryRunner.query(`
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tool_category_enum') THEN
                    CREATE TYPE "tool_category_enum" AS ENUM('页面制作', 'SEO优化', '设计优化', '数据分析', '开发工具');
                END IF;
                
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tool_difficulty_enum') THEN
                    CREATE TYPE "tool_difficulty_enum" AS ENUM('初级', '中级', '高级');
                END IF;
            END
            $$;
        `);

        // 创建building_tools表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS "building_tools" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "code" character varying NOT NULL,
                "title" character varying NOT NULL,
                "description" text NOT NULL,
                "icon" character varying NOT NULL,
                "url" character varying NOT NULL,
                "category" "tool_category_enum" NOT NULL,
                "difficulty" "tool_difficulty_enum" NOT NULL,
                "estimatedTime" character varying NOT NULL,
                "isActive" boolean NOT NULL DEFAULT true,
                "sortOrder" integer NOT NULL DEFAULT 0,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_building_tools_id" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_building_tools_code" UNIQUE ("code")
            )
        `);

        // 创建多对多关系表
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS "course_building_tools" (
                "tool_id" uuid NOT NULL,
                "course_id" uuid NOT NULL,
                CONSTRAINT "PK_course_building_tools" PRIMARY KEY ("tool_id", "course_id")
            )
        `);

        // 创建索引
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_course_building_tools_tool_id" ON "course_building_tools" ("tool_id")`);
        await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_course_building_tools_course_id" ON "course_building_tools" ("course_id")`);

        // 添加外键约束
        await queryRunner.query(`
            ALTER TABLE "course_building_tools" 
            ADD CONSTRAINT "FK_course_building_tools_tool_id" 
            FOREIGN KEY ("tool_id") REFERENCES "building_tools"("id") 
            ON DELETE CASCADE ON UPDATE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "course_building_tools" 
            ADD CONSTRAINT "FK_course_building_tools_course_id" 
            FOREIGN KEY ("course_id") REFERENCES "courses"("id") 
            ON DELETE CASCADE ON UPDATE CASCADE
        `);

        // 插入默认建站工具数据
        await queryRunner.query(`
            INSERT INTO "building_tools" ("code", "title", "description", "icon", "url", "category", "difficulty", "estimatedTime", "sortOrder") 
            VALUES
            ('landing-page-builder', 'AI落地页生成器', '5分钟制作专业落地页，支持产品推广、活动营销等多种场景', 'Zap', '/interactive-course', '页面制作', '初级', '5分钟', 1),
            ('seo-optimizer', 'SEO优化工具', '智能分析网站SEO表现，提供优化建议和关键词策略', 'Wrench', '#', 'SEO优化', '中级', '10分钟', 2),
            ('responsive-checker', '响应式设计检查', '一键检查网站在不同设备上的显示效果，确保完美适配', 'Wrench', '#', '设计优化', '初级', '3分钟', 3)
            ON CONFLICT ("code") DO NOTHING
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // 删除外键约束
        await queryRunner.query(`ALTER TABLE "course_building_tools" DROP CONSTRAINT IF EXISTS "FK_course_building_tools_course_id"`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" DROP CONSTRAINT IF EXISTS "FK_course_building_tools_tool_id"`);
        
        // 删除索引
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_course_building_tools_course_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_course_building_tools_tool_id"`);
        
        // 删除表
        await queryRunner.query(`DROP TABLE IF EXISTS "course_building_tools"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "building_tools"`);
        
        // 删除枚举类型
        await queryRunner.query(`DROP TYPE IF EXISTS "tool_difficulty_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "tool_category_enum"`);
    }
}
