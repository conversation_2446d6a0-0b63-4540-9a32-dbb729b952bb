import { config } from 'dotenv';
import { resolve } from 'path';

// 如果没有设置 NODE_ENV，默认设置为 development
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
}

// 根据环境加载对应的 .env 文件
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';
console.log(`[Environment] Loading environment from ${envFile}`);
config({ path: resolve(__dirname, '..', envFile) });

// 打印环境变量，用于调试
console.log('[Environment] NODE_ENV:', process.env.NODE_ENV);
console.log('[Environment] DB_HOST:', process.env.DB_HOST);
console.log('[Environment] DB_USERNAME:', process.env.DB_USERNAME);

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, ClassSerializerInterceptor } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import cookieParser from 'cookie-parser';
import { useContainer } from 'class-validator';
import { HttpExceptionFilter } from './core/filters/http-exception.filter';
import { Request, Response, NextFunction } from 'express';
import express from 'express';
import { ENV_CONFIG } from './config/environment';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'debug', 'log', 'verbose'],
  });

  // 配置全局前缀
  app.setGlobalPrefix(ENV_CONFIG.api.prefix);

  // 配置全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    })
  );

  // 配置全局序列化拦截器
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get('Reflector')));

  // 配置依赖注入容器
  useContainer(app.select(AppModule), { fallbackOnErrors: true });

  // 添加请求日志中间件
  app.use((req: Request, res: Response, next: NextFunction) => {
    console.log('[Request]', {
      method: req.method,
      url: req.url,
      query: req.query,
      body: req.body,
      headers: req.headers,
      timestamp: new Date().toISOString(),
    });
    next();
  });

  // 配置 cookie-parser
  app.use(cookieParser());

  // 配置 body-parser
  app.use(express.json({ limit: '50mb', verify: (req: any, res, buf) => {
    // 保存原始请求体，用于Stripe Webhook验证
    req.rawBody = buf;
  } }));
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // CORS 配置
  const envType = ENV_CONFIG.isDevelopment ? 'development' : 'production';
  app.enableCors({
    origin: function(origin, callback) {
      // 允许没有origin的请求（如同源请求）
      if(!origin) return callback(null, true);

      // 检查origin是否在允许列表中
      if(ENV_CONFIG.cors[envType].origins.indexOf(origin) !== -1){
        callback(null, true);
      } else {
        callback(null, false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // 配置错误过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 配置 Swagger
  const config = new DocumentBuilder()
    .setTitle('Math Nootbook API')
    .setDescription('Math Nootbook 后端 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('认证', '用户认证相关接口')
    .addTag('笔记本', '笔记本管理相关接口')
    .addTag('笔记', '笔记管理相关接口')
    .addTag('分类', '分类管理相关接口')
    .addTag('标签', '标签管理相关接口')
    .addTag('用户', '用户管理相关接口')
    .addTag('管理后台', '管理后台相关接口')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 启动服务
  await app.listen(ENV_CONFIG.api.port);

  console.log(`[Server] 应用已启动`, {
    environment: process.env.NODE_ENV,
    isDevelopment: ENV_CONFIG.isDevelopment,
    port: ENV_CONFIG.api.port,
    apiPrefix: ENV_CONFIG.api.prefix,
    cors: ENV_CONFIG.cors[envType],
    timestamp: new Date().toISOString(),
  });
}

bootstrap();