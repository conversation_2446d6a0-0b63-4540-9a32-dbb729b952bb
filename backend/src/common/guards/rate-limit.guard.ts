import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RATE_LIMIT_KEY, RateLimitMetadata } from '../decorators/rate-limit.decorator';
import { RedisService } from '../../modules/redis/redis.service';

@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private redisService: RedisService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const rateLimitMetadata = this.reflector.get<RateLimitMetadata>(
      RATE_LIMIT_KEY,
      context.getHandler(),
    );

    if (!rateLimitMetadata) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const userId = request.user?.id;
    
    if (!userId) {
      return true;
    }

    const { limit, ttl } = rateLimitMetadata;
    const key = `rateLimit:${userId}:${context.getClass().name}:${context.getHandler().name}`;

    const currentCount = await this.redisService.incr(key);
    
    if (currentCount === 1) {
      await this.redisService.expire(key, ttl);
    }

    if (currentCount > limit) {
      throw new HttpException(
        'Too many requests',
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    return true;
  }
} 