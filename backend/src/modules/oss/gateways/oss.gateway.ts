import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  cors: {
    origin: '*',  // 在生产环境中应该设置为具体的域名
  },
})
export class OssGateway {
  @WebSocketServer()
  private server: Server;

  private readonly logger = new Logger(OssGateway.name);

  // 通知前端图片已上传成功
  async notifyImageUploaded(userId: string, sceneId: string, imageUrl: string): Promise<void> {
    try {
      this.logger.debug('Preparing to notify image uploaded:', {
        userId,
        sceneId,
        imageUrl: imageUrl.substring(0, 50) + '...'
      });

      // 检查用户是否在线
      const sockets = await this.server.in(userId).fetchSockets();
      this.logger.debug('Found connected sockets:', {
        userId,
        socketCount: sockets.length
      });

      if (sockets.length === 0) {
        this.logger.warn('No connected sockets found for user:', {
          userId,
          sceneId
        });
        throw new Error('User not connected');
      }

      // 发送通知
      this.server.to(userId).emit('imageUploaded', {
        sceneId,
        imageUrl
      });

      this.logger.debug('Successfully notified image uploaded:', {
        userId,
        sceneId,
        socketCount: sockets.length
      });
    } catch (error) {
      this.logger.error('Failed to notify image uploaded:', {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        userId,
        sceneId
      });
      throw error;
    }
  }

  // 用户连接时加入房间
  handleConnection(client: Socket) {
    const userId = client.handshake.query.userId as string;
    if (userId) {
      client.join(userId);
      this.logger.debug('Client connected:', {
        userId,
        socketId: client.id
      });
    }
  }

  // 用户断开连接时离开房间
  handleDisconnect(client: Socket) {
    const userId = client.handshake.query.userId as string;
    if (userId) {
      client.leave(userId);
      this.logger.debug('Client disconnected:', {
        userId,
        socketId: client.id
      });
    }
  }
} 