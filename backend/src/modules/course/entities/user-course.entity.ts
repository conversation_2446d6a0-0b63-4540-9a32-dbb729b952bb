import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Course } from './course.entity';
import { User } from '../../user/entities/user.entity';

@Entity('user_courses')
export class UserCourse {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'course_id' })
  courseId: string;

  @ManyToOne(() => Course)
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @Column({ default: 0 })
  progress: number;  // 学习进度（百分比）

  @Column({ type: 'jsonb', nullable: true, name: 'completed_lessons' })
  completedLessons: string[];  // 已完成的课程ID数组

  @Column({ name: 'last_visited_lesson_id', nullable: true })
  lastVisitedLessonId: string;  // 最后访问的课程章节ID

  @CreateDateColumn({ name: 'purchased_at' })
  purchasedAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 