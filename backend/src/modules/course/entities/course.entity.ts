import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToMany, ManyToOne, JoinColumn } from 'typeorm';
import { CourseLesson } from './course-lesson.entity';
import { BuildingTool } from './building-tool.entity';
import { Catalogue } from './catalogue.entity';

@Entity('courses')
export class Course {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string;  // 例如：'course_1_enterprise_ai_basic'

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column()
  level: string;

  @Column()
  duration: string;  // 例如：'8小时'

  @Column({ name: 'lessons_count', default: 0 })
  lessonsCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'is_published', default: false })
  isPublished: boolean;

  @Column({ name: 'catalogue_id', nullable: true })
  catalogueId: string;

  @OneToMany(() => CourseLesson, lesson => lesson.course)
  lessons: CourseLesson[];

  @ManyToMany(() => BuildingTool, tool => tool.courses)
  buildingTools: BuildingTool[];

  @ManyToOne(() => Catalogue, { nullable: true })
  @JoinColumn({ name: 'catalogue_id' })
  catalogue: Catalogue;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 