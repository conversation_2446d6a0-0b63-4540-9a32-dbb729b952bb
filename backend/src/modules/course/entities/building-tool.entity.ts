import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { Course } from './course.entity';

export enum ToolCategory {
  PAGE_CREATION = '页面制作',
  SEO_OPTIMIZATION = 'SEO优化', 
  DESIGN_OPTIMIZATION = '设计优化',
  ANALYTICS = '数据分析',
  DEVELOPMENT = '开发工具'
}

export enum ToolDifficulty {
  BEGINNER = '初级',
  INTERMEDIATE = '中级',
  ADVANCED = '高级'
}

@Entity('building_tools')
export class BuildingTool {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string; // 例如：'landing-page-builder'

  @Column()
  title: string; // 例如：'AI落地页生成器'

  @Column({ type: 'text' })
  description: string;

  @Column()
  icon: string; // 图标名称，如 'Zap', 'Wrench'

  @Column()
  url: string; // 工具链接

  @Column({
    type: 'enum',
    enum: ToolCategory,
    enumName: 'tool_category_enum'
  })
  category: ToolCategory;

  @Column({
    type: 'enum',
    enum: ToolDifficulty,
    enumName: 'tool_difficulty_enum'
  })
  difficulty: ToolDifficulty;

  @Column()
  estimatedTime: string; // 预计使用时间，如 '5分钟'

  @Column({ default: true })
  isActive: boolean; // 是否启用

  @Column({ default: 0 })
  sortOrder: number; // 排序权重

  @ManyToMany(() => Course, course => course.buildingTools, { cascade: true })
  @JoinTable({
    name: 'course_building_tools',
    joinColumn: { name: 'tool_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'course_id', referencedColumnName: 'id' }
  })
  courses: Course[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 