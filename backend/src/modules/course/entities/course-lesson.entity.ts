import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { Course } from './course.entity';

@Entity('course_lessons')
@Unique(['courseId', 'order'])  // 添加联合唯一约束
export class CourseLesson {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'course_id' })
  courseId: string;

  @ManyToOne(() => Course, course => course.lessons)
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @Column()
  title: string;

  @Column()
  order: number;  // 课程顺序

  @Column()
  duration: string;  // 例如：'45分钟'

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'script_path' })
  scriptPath: string;  // 例如：'lesson_1_script.md'

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 