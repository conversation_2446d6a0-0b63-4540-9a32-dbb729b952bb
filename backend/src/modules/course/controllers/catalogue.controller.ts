import { Controller, Get, Post, Body, Param, Put, Delete } from '@nestjs/common';
import { CatalogueService } from '../services/catalogue.service';
import { CreateCatalogueDto, UpdateCatalogueDto } from '../dto/catalogue.dto';

@Controller('catalogue')
export class CatalogueController {
  constructor(private readonly catalogueService: CatalogueService) {}

  @Get()
  async findAll() {
    return this.catalogueService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.catalogueService.findOne(id);
  }

  @Post()
  async create(@Body() createCatalogueDto: CreateCatalogueDto) {
    return this.catalogueService.create(createCatalogueDto);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCatalogueDto: UpdateCatalogueDto,
  ) {
    return this.catalogueService.update(id, updateCatalogueDto);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.catalogueService.delete(id);
  }
}