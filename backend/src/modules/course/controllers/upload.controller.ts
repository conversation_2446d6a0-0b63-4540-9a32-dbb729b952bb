import {
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/entities/user.entity';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';

interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

@Controller('upload')
export class UploadController {
  @Post('image')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/images',
        filename: (req: Request, file: MulterFile, callback: (error: Error | null, filename: string) => void) => {
          const uniqueSuffix = uuidv4();
          callback(null, `${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req: Request, file: MulterFile, callback: (error: Error | null, acceptFile: boolean) => void) => {
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
          return callback(new BadRequestException('只支持图片文件'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 限制5MB
      },
    }),
  )
  async uploadImage(@UploadedFile() file: MulterFile) {
    if (!file) {
      throw new BadRequestException('请选择要上传的图片');
    }

    // 返回图片的URL
    return {
      url: `/uploads/images/${file.filename}`,
    };
  }

  @Post('video')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/videos',
        filename: (req: Request, file: MulterFile, callback: (error: Error | null, filename: string) => void) => {
          const uniqueSuffix = uuidv4();
          callback(null, `${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req: Request, file: MulterFile, callback: (error: Error | null, acceptFile: boolean) => void) => {
        if (!file.originalname.match(/\.(mp4|webm|ogg)$/)) {
          return callback(new BadRequestException('只支持 MP4、WebM、OGG 格式的视频文件'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 100 * 1024 * 1024, // 限制100MB
      },
    }),
  )
  async uploadVideo(@UploadedFile() file: MulterFile) {
    if (!file) {
      throw new BadRequestException('请选择要上传的视频');
    }

    return {
      url: `/uploads/videos/${file.filename}`,
    };
  }

  @Post('audio')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/audios',
        filename: (req: Request, file: MulterFile, callback: (error: Error | null, filename: string) => void) => {
          const uniqueSuffix = uuidv4();
          callback(null, `${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req: Request, file: MulterFile, callback: (error: Error | null, acceptFile: boolean) => void) => {
        if (!file.originalname.match(/\.(mp3|wav|ogg|m4a)$/)) {
          return callback(new BadRequestException('只支持 MP3、WAV、OGG、M4A 格式的音频文件'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 20 * 1024 * 1024, // 限制20MB
      },
    }),
  )
  async uploadAudio(@UploadedFile() file: MulterFile) {
    if (!file) {
      throw new BadRequestException('请选择要上传的音频');
    }

    return {
      url: `/uploads/audios/${file.filename}`,
    };
  }
} 