import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CourseGeneratorService } from '../services/course-generator.service';
import { GenerateCourseDto, RegenerateSectionDto } from '../dto/course-generation.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../user/entities/user.entity';

@ApiTags('课程生成')
@Controller('course-generator')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class CourseGeneratorController {
  constructor(private readonly courseGeneratorService: CourseGeneratorService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成课程内容' })
  @ApiResponse({ status: 201, description: '返回生成的课程内容' })
  async generateCourse(@Body() generateCourseDto: GenerateCourseDto): Promise<{ content: string }> {
    const content = await this.courseGeneratorService.generateCourse(generateCourseDto);
    return { content };
  }

  @Post('regenerate-section')
  @ApiOperation({ summary: '重新生成课程小节' })
  @ApiResponse({ status: 201, description: '返回重新生成的小节内容' })
  async regenerateSection(@Body() regenerateSectionDto: RegenerateSectionDto): Promise<{ content: string }> {
    const content = await this.courseGeneratorService.regenerateSection(regenerateSectionDto);
    return { content };
  }
} 