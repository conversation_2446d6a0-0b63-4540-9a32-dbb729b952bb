import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CourseAccessService } from '../services/course-access.service';
import { UserRole } from '../../user/entities/user.entity';

export interface SubscriptionGuardOptions {
  requireSubscription?: boolean;
  allowPurchasedAccess?: boolean;
  allowFreeAccess?: boolean;
}

// Decorator to set subscription guard options  
export const SubscriptionOptions = Reflector.createDecorator<SubscriptionGuardOptions>();

@Injectable()
export class SubscriptionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private courseAccessService: CourseAccessService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    // Allow access if no user (will be handled by auth guards)
    if (!user) {
      return true;
    }

    // Admin users always have access
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // Get subscription options from decorator
    const options = this.reflector.get(
      SubscriptionOptions,
      context.getHandler(),
    ) || { requireSubscription: false, allowPurchasedAccess: true, allowFreeAccess: true };

    // Get course ID from request parameters
    const courseId = request.params.id || request.params.courseId;
    
    if (!courseId) {
      // If no course ID, no subscription check needed
      return true;
    }

    // Check course access
    const accessResult = await this.courseAccessService.checkCourseAccess(user.id, courseId);

    // Handle different access scenarios
    switch (accessResult.reason) {
      case 'free_course':
        return options.allowFreeAccess !== false;
      
      case 'purchased':
        return options.allowPurchasedAccess !== false;
      
      case 'subscription':
        return true;
      
      case 'no_access':
        if (options.requireSubscription) {
          throw new ForbiddenException({
            message: 'Active subscription required to access this content',
            reason: 'subscription_required',
            requiresSubscription: true
          });
        }
        return false;
      
      default:
        return false;
    }
  }
}

// Convenience decorators for common use cases
export const RequireSubscription = () => 
  SubscriptionOptions({ requireSubscription: true });

export const AllowPurchasedOrSubscription = () => 
  SubscriptionOptions({ allowPurchasedAccess: true, requireSubscription: false });

export const RequireSubscriptionOrPurchase = () => 
  SubscriptionOptions({ requireSubscription: true, allowPurchasedAccess: true });