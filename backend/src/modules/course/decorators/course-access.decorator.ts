import { createParamDecorator, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { CourseAccessService } from '../services/course-access.service';
import { UserRole } from '../../user/entities/user.entity';

export interface CourseAccessInfo {
  hasAccess: boolean;
  reason: 'free_course' | 'purchased' | 'subscription' | 'no_access';
  message?: string;
  requiresSubscription?: boolean;
}

export const CourseAccess = createParamDecorator(
  async (data: { required?: boolean } = {}, ctx: ExecutionContext): Promise<CourseAccessInfo> => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    const courseId = request.params.id || request.params.courseId;
    
    if (!user || !courseId) {
      const defaultResult: CourseAccessInfo = {
        hasAccess: false,
        reason: 'no_access',
        message: 'User not authenticated or course ID not provided'
      };
      
      if (data.required) {
        throw new ForbiddenException(defaultResult);
      }
      
      return defaultResult;
    }

    // Admin users always have access
    if (user.role === UserRole.ADMIN) {
      return {
        hasAccess: true,
        reason: 'subscription', // Admin treated as subscription user
        message: 'Admin access'
      };
    }

    // Get course access service from the application context
    const app = ctx.getClass();
    const courseAccessService = app.prototype.courseAccessService || 
      request.app?.get?.(CourseAccessService);

    if (!courseAccessService) {
      throw new Error('CourseAccessService not available in context');
    }

    const accessResult = await courseAccessService.checkCourseAccess(user.id, courseId);
    
    const result: CourseAccessInfo = {
      hasAccess: accessResult.hasAccess,
      reason: accessResult.reason,
      message: accessResult.message,
      requiresSubscription: accessResult.reason === 'no_access'
    };

    if (data.required && !result.hasAccess) {
      throw new ForbiddenException(result);
    }

    return result;
  },
);

// Convenience decorator that throws exception if no access
export const RequireCourseAccess = () => CourseAccess({ required: true });