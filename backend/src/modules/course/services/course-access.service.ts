import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course } from '../entities/course.entity';
import { UserCourse } from '../entities/user-course.entity';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { User } from '../../user/entities/user.entity';

export interface CourseAccessResult {
  hasAccess: boolean;
  reason: 'free_course' | 'purchased' | 'subscription' | 'no_access';
  message?: string;
}

@Injectable()
export class CourseAccessService {
  constructor(
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(UserCourse)
    private userCourseRepository: Repository<UserCourse>,
    private subscriptionService: SubscriptionService,
  ) {}

  /**
   * Check if user can access a specific course
   * Business Logic:
   * 1. Free courses (price = 0) → Always accessible
   * 2. Already purchased courses → Always accessible (permanent access)
   * 3. Users with valid subscription → Accessible
   * 4. Otherwise → No access
   */
  async checkCourseAccess(userId: string, courseId: string): Promise<CourseAccessResult> {
    // Get course information
    const course = await this.courseRepository.findOne({
      where: { id: courseId }
    });

    if (!course) {
      return {
        hasAccess: false,
        reason: 'no_access',
        message: 'Course not found'
      };
    }

    // 1. Free courses are always accessible
    if (Number(course.price) === 0) {
      return {
        hasAccess: true,
        reason: 'free_course',
        message: 'Free course access'
      };
    }

    // 2. Check if user has already purchased this course
    const userCourse = await this.userCourseRepository.findOne({
      where: {
        userId,
        courseId
      }
    });

    if (userCourse) {
      return {
        hasAccess: true,
        reason: 'purchased',
        message: 'Previously purchased course'
      };
    }

    // 3. Check if user has valid subscription
    const hasValidSubscription = await this.subscriptionService.hasValidSubscription(userId);
    
    if (hasValidSubscription) {
      return {
        hasAccess: true,
        reason: 'subscription',
        message: 'Access via active subscription'
      };
    }

    // 4. No access
    return {
      hasAccess: false,
      reason: 'no_access',
      message: 'Course requires purchase or active subscription'
    };
  }

  /**
   * Check if user can access any course lessons
   */
  async checkLessonAccess(userId: string, courseId: string): Promise<CourseAccessResult> {
    return this.checkCourseAccess(userId, courseId);
  }

  /**
   * Get accessible courses for a user
   */
  async getAccessibleCourses(userId: string): Promise<Course[]> {
    const user = { id: userId } as User;
    
    // Get all published courses
    const allCourses = await this.courseRepository.find({
      where: { isPublished: true },
      relations: ['catalogue']
    });

    // Get user's purchased courses
    const userCourses = await this.userCourseRepository.find({
      where: { userId },
      relations: ['course']
    });

    const purchasedCourseIds = new Set(userCourses.map(uc => uc.courseId));

    // Check if user has valid subscription
    const hasValidSubscription = await this.subscriptionService.hasValidSubscription(userId);

    // Filter accessible courses
    const accessibleCourses = allCourses.filter(course => {
      // Free courses are always accessible
      if (Number(course.price) === 0) {
        return true;
      }

      // Previously purchased courses are accessible
      if (purchasedCourseIds.has(course.id)) {
        return true;
      }

      // Subscription users can access all paid courses
      if (hasValidSubscription) {
        return true;
      }

      return false;
    });

    // Mark courses as purchased for frontend
    return accessibleCourses.map(course => ({
      ...course,
      isPurchased: purchasedCourseIds.has(course.id) || Number(course.price) === 0 || hasValidSubscription
    }));
  }

  /**
   * Grant course access (for purchases and free enrollments)
   */
  async grantCourseAccess(userId: string, courseId: string, grantedBy: 'purchase' | 'free_enroll' | 'gift'): Promise<void> {
    // Check if access already exists
    const existingAccess = await this.userCourseRepository.findOne({
      where: { userId, courseId }
    });

    if (existingAccess) {
      return; // Access already granted
    }

    // Create new access record
    const userCourse = this.userCourseRepository.create({
      userId,
      courseId,
      progress: 0,
      completedLessons: [],
      lastVisitedLessonId: undefined,
    });

    await this.userCourseRepository.save(userCourse);
  }

  /**
   * Check if user needs subscription for course access
   */
  async requiresSubscription(userId: string, courseId: string): Promise<boolean> {
    const accessResult = await this.checkCourseAccess(userId, courseId);
    return !accessResult.hasAccess && accessResult.reason === 'no_access';
  }
}