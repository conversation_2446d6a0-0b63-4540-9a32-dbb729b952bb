import { Injectable, Logger } from '@nestjs/common';
import { AiService } from '../../ai/services/ai.service';
import { PromptManager } from '../../ai/services/prompt/prompt-manager';
import { BaseAiService } from '../../ai/services/base/base-ai.service';
import { RegenerateSectionDto } from '../dto/course-generation.dto';

export interface CourseGeneratorParams {
  title: string;
  difficulty: string;
  targetAudience: string;
  interactionCount?: number;
  includeExercises?: boolean;
  sectionCount?: number;
  rawMaterial?: string;
  tone?: string;
}

@Injectable()
export class CourseGeneratorService {
  private readonly logger = new Logger(CourseGeneratorService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly promptManager: PromptManager,
  ) {}

  async generateCourse(params: CourseGeneratorParams): Promise<string> {
    try {
      this.logger.log('Starting course generation:', {
        title: params.title,
        difficulty: params.difficulty,
        targetAudience: params.targetAudience,
        sectionCount: params.sectionCount,
        timestamp: new Date().toISOString(),
      });

      const service = await this.aiService.getDefaultService();
      
      // 使用 prompt-manager 生成提示词
      const prompt = this.promptManager.formatCoursePrompt(params);

      this.logger.log('Sending chat request with prompt:', {
        promptLength: prompt.length,
        systemPromptType: 'courseGenerator',
      });

      // 一次性生成所有内容
      const response = await service.chat({
        messages: [
          { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
          { role: 'user', content: prompt }
        ]
      });

      this.logger.log('Successfully generated course content:', {
        contentLength: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to generate course:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        params: {
          title: params.title,
          difficulty: params.difficulty,
          targetAudience: params.targetAudience,
        },
        timestamp: new Date().toISOString(),
        stack: error.stack,
      });

      // 根据错误类型返回更具体的错误信息
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        throw new Error('生成课程内容超时，请减少内容量或稍后重试');
      }
      
      if (error.message.includes('rate limit') || error.message.includes('频率')) {
        throw new Error('系统繁忙，请稍后重试');
      }

      if (error.message.includes('API key') || error.message.includes('密钥')) {
        throw new Error('AI 服务配置错误，请联系管理员');
      }

      throw new Error(`生成课程失败：${error.message}`);
    }
  }

  private async generateOutline(params: CourseGeneratorParams): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateOutlinePrompt(params) }
      ]
    });

    return response;
  }

  private async generateIntroduction(params: CourseGeneratorParams, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateIntroductionPrompt(params, outline) }
      ]
    });

    return response;
  }

  private async generateMainContent(params: CourseGeneratorParams, outline: string): Promise<string> {
    const sections = outline.split('\n').filter(line => line.startsWith('###')).map(line => line.replace('### ', ''));
    let mainContent = '';

    for (const section of sections) {
      const sectionContent = await this.generateSection(params, section, outline);
      mainContent += '\n\n' + sectionContent;
    }

    return mainContent;
  }

  private async generateSection(params: CourseGeneratorParams, sectionTitle: string, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateSectionPrompt(params, sectionTitle) }
      ]
    });

    return response;
  }

  private async generateSummary(params: CourseGeneratorParams, outline: string): Promise<string> {
    const service = await this.aiService.getDefaultService();
    const response = await service.chat({
      messages: [
        { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
        { role: 'user', content: this.promptManager.generateSummaryPrompt(params, outline) }
      ]
    });

    return response;
  }

  private combineContent(introduction: string, mainContent: string, summary: string): string {
    return `${introduction}\n\n${mainContent}\n\n${summary}`;
  }

  async regenerateSection(params: RegenerateSectionDto): Promise<string> {
    try {
      this.logger.log('Starting section regeneration:', {
        sectionTitle: params.sectionTitle,
        difficulty: params.difficulty,
        timestamp: new Date().toISOString(),
      });

      const service = await this.aiService.getDefaultService();
      const sectionPrompt = this.promptManager.generateSectionPrompt({
        difficulty: params.difficulty,
        interactionCount: params.interactionCount,
        includeExercises: params.includeExercises,
      } as CourseGeneratorParams, params.sectionTitle);

      this.logger.log('Sending chat request for section regeneration:', {
        promptLength: sectionPrompt.length,
        systemPromptType: 'courseGenerator',
      });

      const response = await service.chat({
        messages: [
          { role: 'system', content: this.promptManager.getSystemPrompt('courseGenerator') },
          { role: 'user', content: sectionPrompt }
        ]
      });

      this.logger.log('Successfully regenerated section content:', {
        contentLength: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      this.logger.error('Failed to regenerate section:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        params: {
          sectionTitle: params.sectionTitle,
          difficulty: params.difficulty,
        },
        timestamp: new Date().toISOString(),
        stack: error.stack,
      });

      // 根据错误类型返回更具体的错误信息
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        throw new Error('重新生成小节内容超时，请减少内容量或稍后重试');
      }
      
      if (error.message.includes('rate limit') || error.message.includes('频率')) {
        throw new Error('系统繁忙，请稍后重试');
      }

      if (error.message.includes('API key') || error.message.includes('密钥')) {
        throw new Error('AI 服务配置错误，请联系管理员');
      }

      throw new Error(`重新生成小节失败：${error.message}`);
    }
  }
} 