import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { BuildingTool, ToolCategory, ToolDifficulty } from '../entities/building-tool.entity';
import { Course } from '../entities/course.entity';

export interface CreateBuildingToolDto {
  code: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  category: ToolCategory;
  difficulty: ToolDifficulty;
  estimatedTime: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateBuildingToolDto extends Partial<CreateBuildingToolDto> {}

@Injectable()
export class BuildingToolService {
  constructor(
    @InjectRepository(BuildingTool)
    private readonly buildingToolRepository: Repository<BuildingTool>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
  ) {}

  async findAll(): Promise<BuildingTool[]> {
    return this.buildingToolRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<BuildingTool> {
    const tool = await this.buildingToolRepository.findOne({
      where: { id },
      relations: ['courses'],
    });
    if (!tool) {
      throw new NotFoundException('建站工具不存在');
    }
    return tool;
  }

  async create(createBuildingToolDto: CreateBuildingToolDto): Promise<BuildingTool> {
    const tool = this.buildingToolRepository.create(createBuildingToolDto);
    return this.buildingToolRepository.save(tool);
  }

  async update(id: string, updateBuildingToolDto: UpdateBuildingToolDto): Promise<BuildingTool> {
    const tool = await this.findOne(id);
    Object.assign(tool, updateBuildingToolDto);
    return this.buildingToolRepository.save(tool);
  }

  async delete(id: string): Promise<void> {
    const tool = await this.findOne(id);
    await this.buildingToolRepository.remove(tool);
  }

  async getToolsByCourse(courseId: string): Promise<BuildingTool[]> {
    return this.buildingToolRepository
      .createQueryBuilder('tool')
      .innerJoin('tool.courses', 'course')
      .where('course.id = :courseId', { courseId })
      .andWhere('tool.isActive = true')
      .orderBy('tool.sortOrder', 'ASC')
      .addOrderBy('tool.createdAt', 'DESC')
      .getMany();
  }

  async assignToolToCourse(toolId: string, courseId: string): Promise<void> {
    const tool = await this.findOne(toolId);
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      relations: ['buildingTools'],
    });

    if (!course) {
      throw new NotFoundException('课程不存在');
    }

    // 检查是否已经关联
    const isAlreadyAssigned = course.buildingTools?.some(t => t.id === toolId);
    if (!isAlreadyAssigned) {
      if (!course.buildingTools) {
        course.buildingTools = [];
      }
      course.buildingTools.push(tool);
      await this.courseRepository.save(course);
    }
  }

  async removeToolFromCourse(toolId: string, courseId: string): Promise<void> {
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      relations: ['buildingTools'],
    });

    if (!course) {
      throw new NotFoundException('课程不存在');
    }

    course.buildingTools = course.buildingTools?.filter(tool => tool.id !== toolId) || [];
    await this.courseRepository.save(course);
  }

  async updateToolsForCourse(courseId: string, toolIds: string[]): Promise<void> {
    try {
      // 验证实体元数据
      if (!this.courseRepository.metadata) {
        throw new Error('Course entity metadata not loaded');
      }
      if (!this.buildingToolRepository.metadata) {
        throw new Error('BuildingTool entity metadata not loaded');
      }

      // 验证关系配置
      const buildingToolsRelation = this.courseRepository.metadata.relations.find(
        r => r.propertyName === 'buildingTools'
      );
      if (!buildingToolsRelation) {
        throw new Error('buildingTools relation not configured in Course entity');
      }

      const course = await this.courseRepository.findOne({
        where: { id: courseId },
        relations: ['buildingTools'],
      });

      if (!course) {
        throw new NotFoundException('课程不存在');
      }

      // 获取要关联的工具
      const tools = await this.buildingToolRepository.find({
        where: { id: In(toolIds) }
      });
      if (tools.length !== toolIds.length) {
        throw new NotFoundException('部分工具ID不存在');
      }

      course.buildingTools = tools;
      await this.courseRepository.save(course);
    } catch (error) {
      console.error('Failed to update tools for course:', {
        courseId,
        toolIds,
        error: error.stack || error.message
      });
      throw error;
    }
  }

  async updateCourseTools(courseId: string, toolIds: string[]): Promise<void> {
    return this.updateToolsForCourse(courseId, toolIds);
  }
} 