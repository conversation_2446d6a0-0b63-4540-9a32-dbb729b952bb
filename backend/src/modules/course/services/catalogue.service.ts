import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Catalogue } from '../entities/catalogue.entity';
import { CreateCatalogueDto, UpdateCatalogueDto } from '../dto/catalogue.dto';

@Injectable()
export class CatalogueService {
  constructor(
    @InjectRepository(Catalogue)
    private readonly catalogueRepository: Repository<Catalogue>,
  ) {}

  async findAll(): Promise<Catalogue[]> {
    return this.catalogueRepository.find();
  }

  async findOne(id: string): Promise<Catalogue | null> {
    return this.catalogueRepository.findOne({ where: { id } });
  }

  async create(createCatalogueDto: CreateCatalogueDto): Promise<Catalogue> {
    const catalogue = this.catalogueRepository.create(createCatalogueDto);
    return this.catalogueRepository.save(catalogue);
  }

  async update(id: string, updateCatalogueDto: UpdateCatalogueDto): Promise<Catalogue | null> {
    await this.catalogueRepository.update(id, updateCatalogueDto);
    return this.findOne(id);
  }

  async delete(id: string): Promise<void> {
    await this.catalogueRepository.delete(id);
  }
}