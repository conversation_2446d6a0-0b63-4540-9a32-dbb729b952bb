import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, Min } from 'class-validator';

export class CourseDto {
  @ApiProperty({ description: '课程ID' })
  id: string;

  @ApiProperty({ description: '课程代码' })
  code: string;

  @ApiProperty({ description: '课程标题' })
  title: string;

  @ApiProperty({ description: '课程描述' })
  description: string;

  @ApiProperty({ description: '课程等级' })
  level: string;

  @ApiProperty({ description: '课程时长' })
  duration: string;

  @ApiProperty({ description: '课时数量' })
  lessonsCount: number;

  @ApiProperty({ description: '课程价格' })
  price: number;

  @ApiProperty({ description: '是否已发布' })
  isPublished: boolean;

  @ApiProperty({ description: '是否已购买', required: false })
  isPurchased?: boolean;

  @ApiProperty({ description: '学习进度', required: false })
  progress?: number;
}

export class CourseQueryDto {
  @ApiProperty({ required: false, description: '课程等级' })
  @IsOptional()
  @IsString()
  level?: string;

  @ApiProperty({ required: false, description: '是否包含未发布课程' })
  @IsOptional()
  @IsBoolean()
  includeUnpublished?: boolean;

  @ApiProperty({ required: false, description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number = 10;
}

export class LessonDto {
  @ApiProperty({ description: '章节ID' })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ description: '章节标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '章节序号' })
  @IsOptional()
  @IsNumber()
  order?: number;

  @ApiProperty({ description: '章节时长' })
  @IsString()
  duration: string;

  @ApiProperty({ description: '章节描述' })
  @IsString()
  description: string;

  @ApiProperty({ description: '是否已完成', required: false })
  @IsOptional()
  @IsBoolean()
  isCompleted?: boolean;
}

export class UpdateProgressDto {
  @ApiProperty({ description: '课程ID' })
  @IsString()
  courseId: string;

  @ApiProperty({ description: '章节ID' })
  @IsString()
  lessonId: string;
}

export class UpdateCourseDto {
  @ApiProperty({ description: '课程标题', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: '课程描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '课程代码', required: false })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({ description: '课程等级', required: false })
  @IsString()
  @IsOptional()
  level?: string;

  @ApiProperty({ description: '课程时长', required: false })
  @IsString()
  @IsOptional()
  duration?: string;

  @ApiProperty({ description: '课程价格', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  price?: number;

  @ApiProperty({ description: '分类ID', required: false })
  @IsString()
  @IsOptional()
  catalogueId?: string;
} 