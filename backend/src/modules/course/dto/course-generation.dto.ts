import { IsString, IsEnum, IsOptional, IsNumber, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum CourseDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum CourseTone {
  FORMAL = 'formal',
  CASUAL = 'casual'
}

export class GenerateCourseDto {
  @ApiProperty({ description: '课程标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '原始素材' })
  @IsString()
  rawMaterial: string;

  @ApiProperty({ description: '难度级别', enum: CourseDifficulty })
  @IsEnum(CourseDifficulty)
  difficulty: CourseDifficulty;

  @ApiProperty({ description: '目标受众' })
  @IsString()
  targetAudience: string;

  @ApiProperty({ description: '小节数量', required: false })
  @IsOptional()
  @IsNumber()
  sectionCount?: number;

  @ApiProperty({ description: '每节交互轮数', required: false })
  @IsOptional()
  @IsNumber()
  interactionCount?: number;

  @ApiProperty({ description: '是否包含练习', required: false })
  @IsOptional()
  @IsBoolean()
  includeExercises?: boolean;

  @ApiProperty({ description: '语气风格', enum: CourseTone, required: false })
  @IsOptional()
  @IsEnum(CourseTone)
  tone?: CourseTone;

  @ApiProperty({ description: '课程大纲', required: false })
  @IsOptional()
  @IsString()
  outline?: string;
}

export class RegenerateSectionDto {
  @ApiProperty({ description: '小节标题' })
  @IsString()
  sectionTitle: string;

  @ApiProperty({ description: '课程大纲' })
  @IsString()
  outline: string;

  @ApiProperty({ description: '难度级别', enum: CourseDifficulty })
  @IsEnum(CourseDifficulty)
  difficulty: CourseDifficulty;

  @ApiProperty({ description: '每节交互轮数', required: false })
  @IsOptional()
  @IsNumber()
  interactionCount?: number;

  @ApiProperty({ description: '是否包含练习', required: false })
  @IsOptional()
  @IsBoolean()
  includeExercises?: boolean;

  @ApiProperty({ description: '选中的原始内容', required: false })
  @IsOptional()
  @IsString()
  selectedContent?: string;
} 