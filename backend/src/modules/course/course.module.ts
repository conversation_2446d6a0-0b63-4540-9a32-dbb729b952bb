import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Course } from './entities/course.entity';
import { CourseLesson } from './entities/course-lesson.entity';
import { UserCourse } from './entities/user-course.entity';
import { BuildingTool } from './entities/building-tool.entity';
import { Catalogue } from './entities/catalogue.entity';
import { CourseService } from './services/course.service';
import { BuildingToolService } from './services/building-tool.service';
import { CatalogueService } from './services/catalogue.service';
import { CourseController } from './controllers/course.controller';
import { UploadController } from './controllers/upload.controller';
import { CatalogueController } from './controllers/catalogue.controller';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { CourseGeneratorService } from './services/course-generator.service';
import { CourseGeneratorController } from './controllers/course-generator.controller';
import { CourseAccessService } from './services/course-access.service';
import { AiModule } from '../ai/ai.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Course, CourseLesson, UserCourse, BuildingTool, Catalogue]),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', '..', 'uploads'),
      serveRoot: '/uploads',
    }),
    AiModule,
    SubscriptionModule,
  ],
  controllers: [CourseController, UploadController, CourseGeneratorController, CatalogueController],
  providers: [CourseService, CourseGeneratorService, BuildingToolService, CatalogueService, CourseAccessService],
  exports: [CourseService, BuildingToolService, CatalogueService, CourseAccessService],
})
export class CourseModule {}
