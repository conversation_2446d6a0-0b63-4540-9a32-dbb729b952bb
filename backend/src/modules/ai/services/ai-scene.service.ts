import { Injectable, Logger } from '@nestjs/common';
import { AiServiceFactory } from './factory/ai-service.factory';
import { AiServiceResponse } from '../interfaces/ai-service.interface';

@Injectable()
export class AiSceneService {
  private readonly logger = new Logger(AiSceneService.name);

  constructor(private readonly aiServiceFactory: AiServiceFactory) {}

  async generateScenes(
    theme: string,
    description: string,
    sceneCount: number,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200' = '2-10',
    ageGroup: '2-4' | '3-6' | '7-9' | '10-12' = '3-6'
  ): Promise<AiServiceResponse<string>> {
    try {
      const service = await this.aiServiceFactory.getDefaultTextService();
      const enhancedDescription = this.enhanceDescription(description, wordCount, ageGroup);
      const result = await service.generateScenes(
        theme,
        enhancedDescription,
        sceneCount,
        wordCount,
        ageGroup
      );
      return result;
    } catch (error) {
      this.logger.error('Scene generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '场景生成失败'
      };
    }
  }

  private enhanceDescription(
    description: string,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200',
    ageGroup: '2-4' | '3-6' | '7-9' | '10-12'
  ): string {
    const wordCountMap = {
      'single': '每个场景使用1个单词',
      '2-10': '每个场景使用2-10个单词',
      '10-30': '每个场景使用10-30个单词',
      '30-80': '每个场景使用30-80个单词',
      '80-200': '每个场景使用80-200个单词'
    };

    const ageGroupMap = {
      '2-4': '适合2-4岁儿童阅读',
      '3-6': '适合3-6岁儿童阅读',
      '7-9': '适合7-9岁儿童阅读',
      '10-12': '适合10-12岁儿童阅读'
    };

    return `${description}\n\n要求：${wordCountMap[wordCount]}，${ageGroupMap[ageGroup]}`;
  }
} 