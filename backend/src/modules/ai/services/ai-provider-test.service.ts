import { Injectable } from '@nestjs/common';
import { AiServiceFactory } from './factory/ai-service.factory';
import { AiProvider, ProviderType } from '../entities/ai-provider.entity';
import { BaseImageService } from './base/base-image.service';
import { BaseAiService } from './base/base-ai.service';

export interface TestResult {
  success: boolean;
  error?: string;
  details?: any;
}

@Injectable()
export class AiProviderTestService {
  constructor(private readonly aiServiceFactory: AiServiceFactory) {}

  async testConnection(provider: AiProvider): Promise<TestResult> {
    try {
      const service = await this.aiServiceFactory.getService(provider);

      if (provider.type === ProviderType.FLUX) {
        if (!(service instanceof BaseImageService)) {
          throw new Error('Invalid service type for image provider');
        }

        const result = await service.generateImage({
          prompt: 'Test image generation',
          model: 'flux-schnell',
          size: '1024*1024',
          steps: 4,
          guidance: 3.5,
        });

        console.log('Image generation result:', result);

        const success = result.success && Array.isArray(result.data) && result.data.length > 0;
        const imageUrl = success && result.data ? result.data[0] : undefined;
        
        return {
          success,
          error: success ? undefined : (result.error || '未能生成图片'),
          details: success && imageUrl ? { imageUrl } : result.data || {},
        };
      } else {
        if (!(service instanceof BaseAiService)) {
          throw new Error('Invalid service type for text provider');
        }

        const result = await service.editContent({
          content: 'This is a test content.',
          instruction: 'Please check if this content can be processed.'
        });

        console.log('Text service test result:', result);

        const success: boolean = Boolean(result.success && result.data && typeof result.data === 'string');
        
        return {
          success,
          error: success ? undefined : (result.error || '未能处理文本内容'),
          details: success ? { content: result.data } : undefined,
        };
      }
    } catch (error) {
      console.error('Test connection error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败',
      };
    }
  }

  async testProvider(provider: AiProvider): Promise<boolean> {
    try {
      const service = await this.aiServiceFactory.getService(provider);
      const result = await this.testConnection(provider);
      return result.success;
    } catch (error) {
      console.error('Test provider error:', error);
      return false;
    }
  }
} 