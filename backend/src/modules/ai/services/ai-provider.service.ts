import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiProvider, ProviderStatus, ProviderType, ProviderDefaultType } from '../entities/ai-provider.entity';

@Injectable()
export class AiProviderService {
  private readonly logger = new Logger(AiProviderService.name);

  constructor(
    @InjectRepository(AiProvider)
    private readonly aiProviderRepository: Repository<AiProvider>,
  ) {}

  async findAll(): Promise<AiProvider[]> {
    return this.aiProviderRepository.find();
  }

  async findOne(id: string): Promise<AiProvider> {
    const provider = await this.aiProviderRepository.findOne({
      where: { id },
    });
    if (!provider) {
      throw new NotFoundException('AI Provider not found');
    }
    return provider;
  }

  async findDefault(type: ProviderType): Promise<AiProvider | null> {
    const defaultType = type === ProviderType.FLUX 
      ? ProviderDefaultType.IMAGE 
      : ProviderDefaultType.TEXT;

    return this.aiProviderRepository.findOne({
      where: {
        type,
        defaultType,
        status: ProviderStatus.ACTIVE,
      },
    });
  }

  async create(data: Partial<AiProvider>): Promise<AiProvider> {
    const provider = this.aiProviderRepository.create(data);
    return this.aiProviderRepository.save(provider);
  }

  async update(id: string, data: Partial<AiProvider>): Promise<AiProvider> {
    const provider = await this.findOne(id);
    Object.assign(provider, data);
    return this.aiProviderRepository.save(provider);
  }

  async delete(id: string): Promise<void> {
    const provider = await this.findOne(id);
    await this.aiProviderRepository.remove(provider);
  }

  async updateRequestCount(providerId: string, tokens: number): Promise<void> {
    try {
      await this.aiProviderRepository.increment(
        { id: providerId },
        'requestCount',
        1
      );
      await this.aiProviderRepository.increment(
        { id: providerId },
        'totalTokens',
        tokens
      );
    } catch (error) {
      this.logger.error('Failed to update request count:', error);
      throw error;
    }
  }

  async updateCostAmount(providerId: string, cost: number): Promise<void> {
    try {
      await this.aiProviderRepository.increment(
        { id: providerId },
        'costAmount',
        cost
      );
    } catch (error) {
      this.logger.error('Failed to update cost amount:', error);
      throw error;
    }
  }

  async setDefault(id: string): Promise<AiProvider> {
    const provider = await this.findOne(id);
    const defaultType = provider.type === ProviderType.FLUX 
      ? ProviderDefaultType.IMAGE 
      : ProviderDefaultType.TEXT;

    // 重置同类型的其他服务商的默认状态
    await this.aiProviderRepository.update(
      { defaultType },
      { defaultType: null }
    );

    // 设置新的默认服务商
    provider.defaultType = defaultType;
    return this.aiProviderRepository.save(provider);
  }

  async getDefaultProvider(): Promise<AiProvider | null> {
    try {
      const provider = await this.aiProviderRepository.findOne({
        where: {
          defaultType: ProviderDefaultType.TEXT,
          status: ProviderStatus.ACTIVE,
        },
      });

      if (!provider) {
        this.logger.warn('No default AI provider found');
        return null;
      }

      return provider;
    } catch (error) {
      this.logger.error('Failed to get default AI provider:', error);
      throw error;
    }
  }
} 