import { Logger } from '@nestjs/common';
import { ImageGenerateOptions, ImageGenerateResponse, ImageServiceOptions } from '../../interfaces/image-service.interface';
import { AiProviderService } from '../ai-provider.service';

export abstract class BaseImageService {
  protected logger: Logger;
  protected initialized = false;
  protected options: ImageServiceOptions;
  protected apiKey: string;
  protected model: string;
  protected size: string;
  protected steps: number;
  protected guidance_scale: number;

  constructor(
    protected readonly aiProviderService: AiProviderService,
    serviceName: string,
  ) {
    this.logger = new Logger(serviceName);
  }

  async initialize(options: ImageServiceOptions): Promise<void> {
    this.options = options;
    await this.initializeClient();
  }

  async generateImage(options: ImageGenerateOptions): Promise<ImageGenerateResponse> {
    if (!this.initialized) {
      throw new Error('Service is not initialized');
    }

    try {
      const { images, totalTokens } = await this.makeRequest(options);
      const cost = this.calculateCost(totalTokens);

      return {
        success: true,
        data: images,
        usage: {
          totalTokens,
          cost,
        }
      };
    } catch (error) {
      this.logger.error('Failed to generate image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '图片生成失败'
      };
    }
  }

  protected abstract initializeClient(): Promise<void>;

  protected abstract makeRequest(options: ImageGenerateOptions): Promise<{
    images: string[];
    totalTokens: number;
  }>;

  protected abstract calculateCost(tokens: number): number;
} 