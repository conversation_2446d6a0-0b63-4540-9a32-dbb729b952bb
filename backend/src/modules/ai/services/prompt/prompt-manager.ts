import { Injectable } from '@nestjs/common';

export interface CourseGeneratorParams {
  title: string;
  difficulty: string;
  targetAudience: string;
  interactionCount?: number;
  includeExercises?: boolean;
  sectionCount?: number;
  rawMaterial?: string;
  tone?: string;
}

export interface QuizParams {
  topic: string;
  difficulty: string;
  questionCount: number;
  type?: 'multiple_choice' | 'true_false' | 'open_ended';
}

@Injectable()
export class PromptManager {
  private systemPrompts: { [key: string]: string } = {
    courseGenerator: `你是一个专业的心理疗愈师，擅长通过对话帮助人们探索和疗愈内心。
请按照以下原则设计疗愈对话：
1. 以温暖、专业的态度引导对话
2. 注重情绪觉察和表达
3. 通过提问帮助对方深入探索
4. 保持对话的安全性和专业性
5. 根据疗愈类型调整对话重点
6. 避免给出直接建议，而是引导对方发现自己的答案
7. 使用共情和积极倾听的技巧
8. 在适当的时候进行情绪疏导和认知调整
9. 系统描述要注重环境营造和氛围感
10. 疗愈师作为对话主体，负责引导和提问
11. 不要描写来访者的动作和表情
12. 系统描述要：
    - 营造对话环境和氛围
    - 描述空间布局和装饰
    - 描写疗愈师的外貌和表情
    - 记录环境中的细微变化`,
    
    quiz: `你是一个专业的测试题生成助手。请根据提供的参数生成高质量的测试题。
生成的内容应该：
1. 难度适中
2. 题目清晰明确
3. 涵盖关键知识点
4. 包含标准答案和解释`,
  };

  getSystemPrompt(type: string): string {
    return this.systemPrompts[type] || '';
  }

  formatCoursePrompt(params: CourseGeneratorParams): string {
    const { title, difficulty, targetAudience, interactionCount, includeExercises, sectionCount, rawMaterial, tone } = params;
    
    let prompt = `请生成一个关于 "${title}" 的疗愈对话内容\n\n`;
    prompt += `疗愈类型：${difficulty}\n`;
    prompt += `目标受众：${targetAudience}\n`;
    prompt += `语气风格：${tone || 'casual'}\n`;
    prompt += `主题数量：${sectionCount || 3}\n`;
    prompt += `每节交互轮数：${interactionCount || 20}\n`;
    prompt += `是否包含练习：${includeExercises ? '是' : '否'}\n\n`;
    
    if (rawMaterial) {
      prompt += `原始素材：\n${rawMaterial}\n\n`;
    }

    prompt += `要求：
1. 内容要循序渐进，适合${targetAudience}的需求
2. 系统描述主要用于：
   - 营造对话环境和氛围
   - 描述空间布局和装饰
   - 描写疗愈师的外貌和表情
   - 记录环境中的细微变化
3. 疗愈师作为对话主体，负责：
   - 引导对话方向
   - 提出开放性问题
   - 进行情绪疏导
   - 提供专业建议
4. 在需要用户反馈的地方，使用互动练习代替预设回答
5. 每个主题都要有明确的疗愈目标和总结
6. 语气要${tone || 'casual'}且专业
7. 不要描写来访者的动作和表情

内容结构：
1. 对话导入：介绍疗愈目标和重要性
2. 主要内容：分为${sectionCount || 3}个主题
3. 对话总结：回顾要点并提供后续疗愈建议

请按照以下格式生成内容：

# ${title}

## 对话导入
[系统]: 描述对话环境的布置、氛围、疗愈师的外貌和表情等

[疗愈师]: 开场白和引导语

## 第一部分：[标题]
[系统]: 描述当前环境的变化和氛围

[疗愈师]: 引导性问题和专业建议

[practice:chat mode="interactive"]
互动练习内容和要求
[/practice]

## 第二部分：[标题]
[系统]: 描述环境变化和氛围

[疗愈师]: 引导性问题和专业建议

[practice:chat mode="interactive"]
互动练习内容和要求
[/practice]

## 对话总结
[系统]: 描述总结时的环境和氛围

[疗愈师]: 总结要点和后续建议`;

    return prompt;
  }

  formatQuizPrompt(params: QuizParams): string {
    const { topic, difficulty, questionCount, type } = params;
    
    let prompt = `请生成 ${questionCount} 道关于 "${topic}" 的测试题\n\n`;
    prompt += `难度级别：${difficulty}\n`;
    
    if (type) {
      prompt += `题目类型：${type}\n`;
    }

    return prompt;
  }

  generateOutlinePrompt(params: CourseGeneratorParams): string {
    return `请根据以下原始素材生成一个疗愈对话大纲：

主题：${params.title}
疗愈类型：${params.difficulty}
目标受众：${params.targetAudience}
原始素材：${params.rawMaterial}

要求：
1. 将内容分为 ${params.sectionCount || '3-5'} 个主要部分
2. 每部分包含 2-3 个对话主题
3. 确保对话循序渐进
4. 适当安排互动环节
5. 系统描述要注重环境营造
6. 疗愈师作为对话主体
7. 不要描写来访者的动作和表情

返回的大纲格式如下：

# 对话主题

## 第一部分：主题
### 1.1 对话重点
### 1.2 对话重点

## 第二部分：主题
### 2.1 对话重点
### 2.2 对话重点

...以此类推`;
  }

  generateIntroductionPrompt(params: CourseGeneratorParams, outline: string): string {
    return `请根据以下对话大纲生成对话的导入部分：

${outline}

要求：
1. 系统描述要：
   - 营造安全舒适的环境
   - 描述空间布局和装饰
   - 描写疗愈师的外貌和表情
2. 疗愈师要：
   - 介绍对话的主要目标
   - 说明对话的安全性和专业性
   - 建立信任和安全感
3. 语气要${params.tone || '温暖关怀'}且专业
4. 确保内容既专业又容易理解
5. 不要描写来访者的动作和表情

格式要求：
[系统]: 描述对话环境的布置、氛围、疗愈师的外貌和表情等

[疗愈师]: 开场白和引导语

注意：确保内容适合${params.targetAudience}的需求。`;
  }

  generateSectionPrompt(params: CourseGeneratorParams, sectionTitle: string): string {
    return `请生成以下主题的疗愈对话内容：

主题：${sectionTitle}
疗愈类型：${params.difficulty}
目标受众：${params.targetAudience}
原始素材：${params.rawMaterial}

要求：
1. 系统描述要：
   - 营造合适的对话氛围
   - 记录环境中的细微变化
   - 描写疗愈师的表情和动作
2. 疗愈师要：
   - 通过提问引导对方深入探索
   - 进行情绪疏导
   - 提供专业建议
3. 内容要循序渐进
4. 适当加入互动环节
5. 语气要${params.tone || '温暖关怀'}
6. 每个重要主题后添加互动
7. 确保内容既专业又有温度
8. 不要描写来访者的动作和表情

格式示例：
## 主题

[系统]: 描述当前环境的变化和氛围

[疗愈师]: 引导性问题和专业建议

[practice:chat mode="interactive"]
互动练习内容和要求
[/practice]

[等待点击继续]`;
  }

  generateSummaryPrompt(params: CourseGeneratorParams, outline: string): string {
    return `请根据以下对话大纲生成总结部分：

${outline}

要求：
1. 系统描述要：
   - 营造总结时的氛围
   - 描写环境的变化
   - 记录疗愈师的表情和动作
2. 疗愈师要：
   - 总结对话的主要内容和收获
   - 提供后续疗愈建议
   - 鼓励对方继续探索和成长
3. 语气要${params.tone || '温暖关怀'}且专业
4. 不要描写来访者的动作和表情

格式示例：
## 对话总结

[系统]: 描述总结时的环境和氛围

[疗愈师]: 总结要点和后续建议`;
  }
} 