import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import { BaseAiService, ChatMessage } from '../base/base-ai.service';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';

@Injectable()
export class OpenAiService extends BaseAiService {
  private client: OpenAI;

  constructor(
    promptManager: PromptManager,
    aiProviderService: AiProviderService,
    provider: AiProvider,
  ) {
    super(promptManager, aiProviderService, provider);
  }

  protected async initializeClient(): Promise<void> {
    const provider = this.options?.provider;
    if (!provider) {
      throw new Error('Provider configuration is required');
    }

    this.client = new OpenAI({
      apiKey: provider.apiKey,
      baseURL: provider.apiEndpoint,
    });
  }

  protected async makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }> {
    if (!this.client) {
      throw new Error('Service is not properly initialized');
    }

    try {
      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages,
        temperature: 0.7,
        max_tokens: 2000,
      });

      return {
        content: response.choices[0]?.message?.content || '',
        totalTokens: response.usage?.total_tokens || 0,
      };
    } catch (error) {
      this.logger.error('OpenAI API request failed:', error);
      throw error;
    }
  }

  protected calculateCost(tokens: number): number {
    const ratePerThousandTokens = 0.002;
    return (tokens / 1000) * ratePerThousandTokens;
  }
}