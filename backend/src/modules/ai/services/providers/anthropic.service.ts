import { Injectable } from '@nestjs/common';
import Anthropic from '@anthropic-ai/sdk';
import { BaseAiService, ChatMessage } from '../base/base-ai.service';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';

@Injectable()
export class AnthropicService extends BaseAiService {
  private client: Anthropic;

  constructor(
    promptManager: PromptManager,
    aiProviderService: AiProviderService,
    provider: AiProvider,
  ) {
    super(promptManager, aiProviderService, provider);
  }

  protected async initializeClient(): Promise<void> {
    const provider = this.options?.provider;
    if (!provider) {
      throw new Error('Provider configuration is required');
    }

    this.client = new Anthropic({
      apiKey: provider.apiKey,
      baseURL: provider.apiEndpoint,
    });
  }

  protected async makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }> {
    // Anthropic API 不支持 system role，需要将 system 消息合并到第一个 user 消息中
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role === 'user' || m.role === 'assistant') as { role: 'user' | 'assistant'; content: string; }[];
    
    if (userMessages.length === 0) {
      throw new Error('At least one user message is required');
    }

    const response = await this.client.messages.create({
      model: 'claude-3-opus-20240229',
      max_tokens: 2000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: systemMessage ? `${systemMessage.content}\n\n${userMessages[0].content}` : userMessages[0].content,
        },
        ...userMessages.slice(1),
      ],
    });

    // 检查响应内容
    const content = response.content.find(block => block.type === 'text');
    if (!content || !('text' in content)) {
      throw new Error('Empty response from Anthropic API');
    }

    // 使用 Anthropic 提供的 token 计数
    const totalTokens = response.usage?.input_tokens + response.usage?.output_tokens || 
      // 如果 API 没有返回 token 数,使用估算
      Math.ceil((messages.reduce((acc, m) => acc + m.content.length, 0) + content.text.length) / 4);

    return {
      content: content.text,
      totalTokens,
    };
  }

  protected calculateCost(tokens: number): number {
    // Claude-3 的计费规则，这里需要根据实际情况调整
    const ratePerThousandTokens = 0.015; // 每千个 token 的价格
    return (tokens / 1000) * ratePerThousandTokens;
  }
} 