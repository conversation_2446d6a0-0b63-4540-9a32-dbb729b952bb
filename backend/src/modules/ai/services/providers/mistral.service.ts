import { Injectable } from '@nestjs/common';
import { BaseAiService, ChatMessage } from '../base/base-ai.service';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';
import axios from 'axios';

@Injectable()
export class MistralService extends BaseAiService {
  private apiKey: string | undefined;
  private apiEndpoint: string | undefined;

  constructor(
    promptManager: PromptManager,
    aiProviderService: AiProviderService,
    provider: AiProvider,
  ) {
    super(promptManager, aiProviderService, provider);
  }

  protected async initializeClient(): Promise<void> {
    const provider = this.options?.provider;
    if (!provider) {
      throw new Error('Provider configuration is required');
    }
    if (!provider.apiKey) {
      throw new Error('API key is required');
    }

    this.apiKey = provider.apiKey;
    this.apiEndpoint = provider.apiEndpoint || 'https://api.mistral.ai/v1';

    // Test connection by making a simple request
    try {
      await axios.get(`${this.apiEndpoint}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 30000,
      });
      this.initialized = true;
      this.logger.log('Mistral client initialized successfully');
    } catch (error) {
      this.initialized = false;
      this.logger.error('Failed to initialize Mistral client:', error);
      throw error;
    }
  }

  protected async makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }> {
    if (!this.apiKey || !this.apiEndpoint) {
      throw new Error('Service is not properly initialized');
    }

    try {
      this.logger.log('Sending request to Mistral API:', {
        endpoint: this.apiEndpoint,
        messageCount: messages.length,
        model: 'mistral-medium',
        temperature: 0.7,
        maxTokens: 2000,
      });

      const response = await axios.post(
        `${this.apiEndpoint}/chat/completions`,
        {
          model: 'mistral-medium',
          messages,
          temperature: 0.7,
          max_tokens: 2000,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.apiKey}`,
          },
          timeout: 120000,
          maxRedirects: 5,
          proxy: false,
        },
      );

      this.logger.log('Received response from Mistral API:', {
        status: response.status,
        statusText: response.statusText,
        hasChoices: !!response.data?.choices,
        choicesLength: response.data?.choices?.length,
        totalTokens: response.data?.usage?.total_tokens,
        responseTime: new Date().toISOString(),
      });

      if (!response.data?.choices?.[0]?.message?.content) {
        this.logger.error('Invalid response structure:', {
          responseData: response.data,
          choices: response.data?.choices,
          firstChoice: response.data?.choices?.[0],
        });
        throw new Error('Invalid response from Mistral API');
      }

      const result = {
        content: response.data.choices[0].message.content,
        totalTokens: response.data.usage?.total_tokens || 0,
      };

      this.logger.log('Successfully processed Mistral API response:', {
        contentLength: result.content.length,
        totalTokens: result.totalTokens,
      });

      return result;
    } catch (error) {
      this.logger.error('Mistral API request failed:', {
        errorType: error.constructor.name,
        errorMessage: error.message,
        errorCode: error.code,
        responseStatus: error.response?.status,
        responseData: error.response?.data,
        stack: error.stack,
      });

      if (error.code === 'ECONNABORTED') {
        this.logger.error('Request timeout:', {
          timeout: 120000,
          errorDetails: error.message,
        });
        throw new Error('请求超时，请稍后重试');
      }

      if (error.response?.status === 429) {
        this.logger.error('Rate limit exceeded:', {
          rateLimitDetails: error.response.data,
        });
        throw new Error('请求频率过高，请稍后重试');
      }

      if (error.response?.status === 401) {
        this.logger.error('Authentication failed:', {
          authDetails: error.response.data,
        });
        throw new Error('API 密钥无效或已过期');
      }

      if (error.response?.status === 503) {
        this.logger.error('Service unavailable:', {
          serviceDetails: error.response.data,
        });
        throw new Error('服务暂时不可用，请稍后重试');
      }

      const errorMessage = error.response?.data?.error?.message || 
                          error.message || 
                          '生成内容失败，请稍后重试';
      
      this.logger.error('Unhandled error:', {
        message: errorMessage,
        originalError: error,
      });

      throw new Error(errorMessage);
    }
  }

  protected calculateCost(tokens: number): number {
    // Mistral Medium 的计费规则，这里需要根据实际情况调整
    const ratePerThousandTokens = 0.007; // 每千个 token 的价格
    return (tokens / 1000) * ratePerThousandTokens;
  }
} 