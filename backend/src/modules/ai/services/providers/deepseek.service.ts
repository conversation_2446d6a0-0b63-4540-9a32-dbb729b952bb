import { Injectable } from '@nestjs/common';
import { BaseAiService, ChatMessage } from '../base/base-ai.service';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';
import axios from 'axios';

@Injectable()
export class DeepseekService extends BaseAiService {
  private apiKey: string | undefined;
  private apiEndpoint: string | undefined;
  private model: string | undefined;
  private initializationPromise: Promise<void> | null = null;

  constructor(
    promptManager: PromptManager,
    aiProviderService: AiProviderService,
    provider: AiProvider,
  ) {
    super(promptManager, aiProviderService, provider);
  }

  protected async initializeClient(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = (async () => {
      try {
        if (!this.provider) {
          throw new Error('Provider configuration is required');
        }
        if (!this.provider.apiKey) {
          throw new Error('API key is required');
        }

        this.apiKey = this.provider.apiKey;
        this.apiEndpoint = this.provider.apiEndpoint || 'https://api.deepseek.com/v1';
        this.model = this.provider.config?.modelName || 'deepseek-chat';

        // Test connection by making a simple request
        await axios.get(`${this.apiEndpoint}/models`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          timeout: 30000,
        });

        this.initialized = true;
        this.logger.log('DeepSeek client initialized successfully');
      } catch (error) {
        this.initialized = false;
        this.initializationPromise = null;
        this.logger.error('Failed to initialize DeepSeek client:', error);
        throw error;
      }
    })();

    return this.initializationPromise;
  }

  protected async makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }> {
    try {
      // 等待初始化完成
      await this.initializeClient();

      if (!this.initialized) {
        throw new Error('Service is not properly initialized');
      }

      const temperature = this.provider.config?.temperature || 0.7;
      const maxTokens = this.provider.config?.maxTokens || 2000;

      this.logger.log('Sending request to DeepSeek API', {
        endpoint: this.apiEndpoint,
        model: this.model,
        messageCount: messages.length,
        temperature,
        maxTokens,
      });

      const response = await axios.post(
        `${this.apiEndpoint}/chat/completions`,
        {
          model: this.model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          temperature,
          max_tokens: maxTokens,
          stream: false,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
          },
          timeout: 60000,
        },
      );

      this.logger.log('DeepSeek API response received', {
        status: response.status,
        hasChoices: !!response.data?.choices,
        hasUsage: !!response.data?.usage,
      });

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from DeepSeek API: missing content');
      }

      return {
        content: response.data.choices[0].message.content,
        totalTokens: response.data.usage?.total_tokens || 0,
      };
    } catch (error) {
      this.logger.error('DeepSeek API request failed:', {
        error,
        response: error.response?.data,
        status: error.response?.status,
      });
      throw error;
    }
  }

  protected calculateCost(tokens: number): number {
    const ratePerToken = this.provider.config?.ratePerToken || 0.000002;
    return tokens * ratePerToken;
  }
} 