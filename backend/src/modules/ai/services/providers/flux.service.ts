import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { BaseImageService } from '../base/base-image.service';
import { ImageGenerateOptions } from '../../interfaces/image-service.interface';
import { AiProviderService } from '../ai-provider.service';

interface DashScopeResponse {
  output: {
    task_id?: string;
    task_status?: 'PENDING' | 'RUNNING' | 'SUCCEEDED' | 'FAILED';
    results?: Array<{
      url: string;
    }>;
    task_metrics?: {
      TOTAL: number;
      SUCCEEDED: number;
      FAILED: number;
    };
    message?: string;
    code?: string;
  };
  usage?: {
    image_count: number;
  };
  status_code?: number;
  request_id: string;
  code?: string;
  message?: string;
  error?: {
    code?: string;
    message?: string;
  };
}

@Injectable()
export class FluxService extends BaseImageService {
  protected apiKey: string;
  protected model: string;
  protected size: string;
  protected steps: number;
  protected guidance_scale: number;
  protected offload: boolean;
  protected add_sampling_metadata: boolean;
  protected apiEndpoint: string;

  constructor(aiProviderService: AiProviderService) {
    super(aiProviderService, FluxService.name);
  }

  protected async initializeClient(): Promise<void> {
    const provider = this.options?.provider;
    if (!provider?.apiKey) {
      throw new Error('Flux API key is required');
    }

    // 设置 API Key 和端点
    this.apiKey = provider.apiKey;
    // 强制使用正确的 API 端点，忽略数据库中的配置
    this.apiEndpoint = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis';

    this.logger.log(`FluxService initialized with endpoint: ${this.apiEndpoint}`);

    // 从 providerConfig.flux 中获取配置
    const fluxConfig = provider.config?.providerConfig?.flux;
    this.model = fluxConfig?.model || 'wanx2.1-t2i-plus';
    this.size = fluxConfig?.size || '1024*1024';
    this.steps = fluxConfig?.steps || 4;
    this.guidance_scale = fluxConfig?.guidance || 3.5;
    this.offload = fluxConfig?.offload || false;
    this.add_sampling_metadata = fluxConfig?.add_sampling_metadata || true;

    this.initialized = true;
  }

  private async fetchTaskResult(taskId: string): Promise<DashScopeResponse> {
    const apiEndpoint = `https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`;

    const response = await axios.get<DashScopeResponse>(
      apiEndpoint,
      {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      }
    );

    // 记录完整的响应信息，便于调试
    this.logger.log('Task result response:', response.data);

    return response.data;
  }

  private async waitForTaskCompletion(taskId: string, maxAttempts = 20): Promise<DashScopeResponse> {
    let attempts = 0;
    while (attempts < maxAttempts) {
      const result = await this.fetchTaskResult(taskId);
      this.logger.log('Task status check:', {
        taskId,
        attempt: attempts + 1,
        status: result.output?.task_status,
      });

      if (result.output?.task_status === 'SUCCEEDED') {
        return result;
      }

      if (result.output?.task_status === 'FAILED') {
        this.logger.error('Task failed with details:', {
          taskId,
          result,
          message: result.message,
          code: result.code,
          output: result.output
        });
        throw new Error(result.message || result.code || 'Task failed');
      }

      // 等待2秒后重试
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }

    throw new Error('Task timeout');
  }

  protected async makeRequest(options: ImageGenerateOptions): Promise<{
    images: string[];
    totalTokens: number;
  }> {
    if (!this.apiKey) {
      throw new Error('Service is not properly initialized');
    }

    this.logger.log(`Making request to endpoint: ${this.apiEndpoint}`);
    // 将 1024x1024 格式转换为 1024*1024
    let size = (options.size || this.size).replace('x', '*');

    // 对于通义万相模型，支持更灵活的尺寸设置
    if (this.model.startsWith('wanx')) {
      // 检查尺寸格式是否正确
      if (!size.includes('*')) {
        this.logger.warn(`尺寸格式不正确: ${size}，应为 width*height 格式，将使用默认尺寸 1024*1024`);
        size = '1024*1024';
      } else {
        // 检查尺寸是否在有效范围内
        const [width, height] = size.split('*').map(Number);
        const maxPixels = 2000000; // 最大200万像素
        const minDimension = 512;
        const maxDimension = 1440;

        if (isNaN(width) || isNaN(height) ||
            width < minDimension || height < minDimension ||
            width > maxDimension || height > maxDimension ||
            width * height > maxPixels) {
          this.logger.warn(`尺寸超出有效范围: ${size}，宽高范围应为[512, 1440]，总像素不超过200万，将使用默认尺寸 1024*1024`);
          size = '1024*1024';
        }
      }
    } else {
      // 原有Flux模型的尺寸限制
      const supportedSizes = ['512*1024', '768*512', '768*1024', '1024*576', '576*1024', '1024*1024'];
      if (!supportedSizes.includes(size)) {
        this.logger.warn(`不支持的尺寸: ${size}，将使用默认尺寸 1024*1024`);
        size = '1024*1024';
      }
    }

    // 根据阿里云文档构造请求体
    const parameters: any = {
      size: size,
      seed: options.seed || Math.floor(Math.random() * 2147483647),
    };

    // 根据不同模型添加不同参数
    if (this.model.startsWith('wanx')) {
      // 通义万相模型特有参数
      parameters.prompt_extend = options.prompt_extend !== undefined ? options.prompt_extend : false; // 默认不开启智能改写
      parameters.watermark = options.watermark !== undefined ? options.watermark : false; // 默认不添加水印
    } else {
      // 原有Flux模型的参数
      parameters.steps = options.steps || this.steps || 4;
      parameters.guidance = options.guidance || this.guidance_scale || 3.5;
      parameters.offload = this.offload || false;
      parameters.add_sampling_metadata = this.add_sampling_metadata || true;
    }

    // 如果有 count 参数，添加 n 参数
    if (options.count && options.count > 1) {
      parameters.n = options.count;
    }

    const requestBody = {
      model: this.model,
      input: {
        prompt: options.prompt,
        negative_prompt: options.negativePrompt || '',
      },
      parameters
    };

    this.logger.log('Sending request to DashScope Flux API', {
      endpoint: this.apiEndpoint,
      body: requestBody,
    });

    try {
      // 1. 提交异步任务
      const submitResponse = await axios.post<DashScopeResponse>(
        this.apiEndpoint,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'X-DashScope-Async': 'enable',  // 启用异步调用
          },
          timeout: 30000, // 30秒超时
        },
      );

      this.logger.log('DashScope API Response:', submitResponse.data);

      // 检查是否有错误码
      if (submitResponse.data.code && submitResponse.data.code !== 'Success') {
        this.logger.error('Task submission failed with code:', {
          code: submitResponse.data.code,
          message: submitResponse.data.message,
          response: submitResponse.data
        });
        throw new Error(submitResponse.data.message || `Failed to submit task: ${submitResponse.data.code}`);
      }

      const taskId = submitResponse.data.output?.task_id;
      if (!taskId) {
        throw new Error('No task ID returned');
      }

      this.logger.log('Task submitted successfully, task ID:', taskId);

      // 2. 等待任务完成
      const result = await this.waitForTaskCompletion(taskId);

      this.logger.log('Task completed:', result);

      // 检查任务是否成功
      if (result.output?.task_status === 'FAILED') {
        throw new Error(result.message || 'Task failed');
      }

      const images = result.output.results?.map((item) => item.url) || [];
      const totalTokens = result.usage?.image_count || images.length;

      this.logger.log('Returning result from makeRequest:', {
        images,
        totalTokens,
      });

      return {
        images,
        totalTokens,
      };
    } catch (error) {
      this.logger.error('DashScope API error:', error);
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || error.message;
        this.logger.error('DashScope API response:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          headers: error.response?.headers,
        });
        throw new Error(`Failed to connect to DashScope API: ${errorMessage}`);
      }
      throw error;
    }
  }

  protected calculateCost(tokens: number): number {
    // 根据不同模型计算费用
    if (this.model === 'wanx2.1-t2i-plus') {
      return tokens * 0.2; // 每张图片0.2元
    } else if (this.model === 'wanx2.1-t2i-turbo') {
      return tokens * 0.14; // 每张图片0.14元
    } else if (this.model === 'wanx2.0-t2i-turbo') {
      return tokens * 0.04; // 每张图片0.04元
    } else {
      // 默认费用（原有的flux模型）
      return tokens * 0.2; // 每张图片0.2元
    }
  }
}