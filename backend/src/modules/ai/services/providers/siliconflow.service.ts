import { Injectable, Logger } from '@nestjs/common';
import { BaseAiService, ChatMessage } from '../base/base-ai.service';
import { AiProviderService } from '../ai-provider.service';
import { AiProvider } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';

@Injectable()
export class SiliconFlowService extends BaseAiService {
  constructor(
    promptManager: PromptManager,
    aiProviderService: AiProviderService,
    provider: AiProvider,
  ) {
    super(promptManager, aiProviderService, provider);
  }

  protected async initializeClient(): Promise<void> {
    if (!this.provider?.apiKey) {
      throw new Error('SiliconFlow API key is required');
    }
    
    // 设置 API 端点，如果没有配置则使用默认值
    if (!this.provider.apiEndpoint) {
      // 默认使用全球负载均衡的域名
      this.provider.apiEndpoint = 'https://api.siliconflow.com';
    }
  }

  protected async makeRequest(messages: ChatMessage[]): Promise<{
    content: string;
    totalTokens: number;
  }> {
    if (!this.provider?.apiEndpoint || !this.provider?.apiKey) {
      throw new Error('SiliconFlow configuration is incomplete');
    }

    // 定义两个可能的 API 端点
    const endpoints = [
      this.provider.apiEndpoint,
      this.provider.apiEndpoint.replace('siliconflow.cn', 'siliconflow.com')
    ];

    let lastError = null;

    // 依次尝试不同的端点
    for (const endpoint of endpoints) {
      try {
        // 确保 API 端点格式正确，并避免重复的 v1 路径
        const apiEndpoint = endpoint.replace(/\/+$/, '').replace(/\/v1$/, '');
        const requestUrl = `${apiEndpoint}/v1/chat/completions`;
        this.logger.debug('Trying API URL:', requestUrl);

        const temperature = this.provider.config?.temperature || 0.7;
        const maxTokens = this.provider.config?.maxTokens || 2000;

        const requestBody = {
          model: this.provider.config?.modelName || 'deepseek-ai/DeepSeek-V3',
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          stream: false,
          max_tokens: maxTokens,
          stop: ["null"],
          temperature,
          top_p: 0.7,
          top_k: 50,
          frequency_penalty: 0.5,
          n: 1,
          response_format: {
            type: "text"
          }
        };

        this.logger.debug('Making request to SiliconFlow API with body:', requestBody);

        const headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.provider.apiKey}`,
          'Accept': 'application/json',
          'User-Agent': 'SiliconFlow-Node-Client'
        };

        this.logger.debug('Request headers:', {
          ...headers,
          'Authorization': 'Bearer [HIDDEN]'
        });

        const apiResponse = await fetch(requestUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody),
        });

        const responseText = await apiResponse.text();
        this.logger.debug('Raw API response:', responseText);

        if (!apiResponse.ok) {
          // 根据文档处理特定的错误码
          switch (apiResponse.status) {
            case 400:
              throw new Error('参数不正确，请检查模型名称等参数');
            case 401:
              throw new Error('API Key 没有正确设置');
            case 403:
              throw new Error('权限不够，可能需要实名认证');
            case 429:
              throw new Error('触发了速率限制，请稍后重试');
            case 503:
            case 504:
              throw new Error('服务负载较高，请稍后重试或使用流式输出');
            default:
              let errorMessage: string;
              try {
                const error = JSON.parse(responseText);
                errorMessage = error.message || error.error || 'Failed to call SiliconFlow API';
              } catch (e) {
                errorMessage = `Failed to parse error response: ${responseText}`;
              }
              throw new Error(errorMessage);
          }
        }

        let result;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          this.logger.error('Failed to parse API response:', e);
          throw new Error(`Invalid JSON response: ${responseText}`);
        }
        
        if (!result.choices?.[0]?.message?.content) {
          this.logger.error('Unexpected response format:', result);
          throw new Error('Unexpected response format from SiliconFlow API');
        }

        return {
          content: result.choices[0].message.content,
          totalTokens: result.usage?.total_tokens || 0,
        };
      } catch (error) {
        this.logger.error(`Error with endpoint ${endpoint}:`, error);
        lastError = error;
        // 如果是第一个端点失败，继续尝试下一个端点
        if (endpoint === endpoints[0]) {
          this.logger.debug('First endpoint failed, trying alternative endpoint...');
          continue;
        }
        throw error;
      }
    }

    // 如果所有端点都失败了
    throw lastError;
  }

  protected calculateCost(totalTokens: number): number {
    // SiliconFlow 的计费规则，这里需要根据实际情况调整
    const ratePerToken = this.provider.config?.ratePerToken || 0.002;
    return totalTokens * ratePerToken;
  }
} 