import { Injectable, Logger } from '@nestjs/common';
import { AiProviderService } from '../ai-provider.service';
import { BaseAiService } from '../base/base-ai.service';
import { BaseImageService } from '../base/base-image.service';
import { DeepseekService } from '../providers/deepseek.service';
import { AnthropicService } from '../providers/anthropic.service';
import { OpenAiService } from '../providers/openai.service';
import { MistralService } from '../providers/mistral.service';
import { SiliconFlowService } from '../providers/siliconflow.service';
import { FluxService } from '../providers/flux.service';
import { AiProvider, ProviderType } from '../../entities/ai-provider.entity';
import { PromptManager } from '../prompt/prompt-manager';

@Injectable()
export class AiServiceFactory {
  private readonly logger = new Logger(AiServiceFactory.name);
  private serviceInstances: Map<string, BaseAiService | BaseImageService> = new Map();

  constructor(
    private readonly aiProviderService: AiProviderService,
    private readonly promptManager: PromptManager
  ) {}

  async getDefaultService(): Promise<BaseAiService> {
    try {
      // 从数据库获取默认的 AI 服务商配置
      const defaultProvider = await this.aiProviderService.getDefaultProvider();
      if (!defaultProvider) {
        throw new Error('No default AI provider configured');
      }

      const service = await this.getService(defaultProvider);
      if (!(service instanceof BaseAiService)) {
        throw new Error('Default provider must be a text service');
      }
      return service;
    } catch (error) {
      this.logger.error('Failed to get default AI service:', error);
      throw error;
    }
  }

  async getService(provider: AiProvider): Promise<BaseAiService | BaseImageService> {
    try {
      // 检查是否已有实例
      const existingService = this.serviceInstances.get(provider.id);
      if (existingService) {
        return existingService;
      }

      // 根据提供商类型创建对应的服务实例
      let service: BaseAiService | BaseImageService;
      
      switch (provider.type) {
        case 'deepseek':
          service = new DeepseekService(this.promptManager, this.aiProviderService, provider);
          break;
        case 'anthropic':
          service = new AnthropicService(this.promptManager, this.aiProviderService, provider);
          break;
        case 'openai':
          service = new OpenAiService(this.promptManager, this.aiProviderService, provider);
          break;
        case 'mistral':
          service = new MistralService(this.promptManager, this.aiProviderService, provider);
          break;
        case 'siliconflow':
          service = new SiliconFlowService(this.promptManager, this.aiProviderService, provider);
          break;
        case 'flux':
          service = new FluxService(this.aiProviderService);
          break;
        default:
          throw new Error(`Unsupported AI provider type: ${provider.type}`);
      }

      // 初始化服务
      await service.initialize({
        provider,
      });

      // 缓存服务实例
      this.serviceInstances.set(provider.id, service);

      return service;
    } catch (error) {
      this.logger.error(`Failed to create AI service for provider ${provider.type}:`, error);
      throw error;
    }
  }

  // 兼容旧的方法名称
  async getDefaultTextService(): Promise<BaseAiService> {
    return this.getDefaultService();
  }

  async getDefaultImageService(): Promise<BaseImageService> {
    try {
      const defaultProvider = await this.aiProviderService.findDefault(ProviderType.FLUX);
      if (!defaultProvider) {
        throw new Error('No default image service provider configured');
      }
      const service = await this.getService(defaultProvider);
      if (!(service instanceof BaseImageService)) {
        throw new Error('Default image provider must be an image service');
      }
      return service;
    } catch (error) {
      this.logger.error('Failed to get default image service:', error);
      throw error;
    }
  }
} 