import { Injectable, Logger } from '@nestjs/common';
import { BaseAiService } from './base/base-ai.service';
import { BaseImageService } from './base/base-image.service';
import { AiServiceFactory } from './factory/ai-service.factory';
import { AiEditParams, AiServiceResponse, DescriptionCompleteParams } from '../interfaces/ai-service.interface';
import { ChatDto } from '../controllers/ai.controller';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(private readonly aiServiceFactory: AiServiceFactory) {}

  async getDefaultService(): Promise<BaseAiService> {
    return this.aiServiceFactory.getDefaultService();
  }

  async getDefaultTextService(): Promise<BaseAiService> {
    return this.aiServiceFactory.getDefaultTextService();
  }

  async getDefaultImageService(): Promise<BaseImageService> {
    return this.aiServiceFactory.getDefaultImageService();
  }

  async editContent(params: AiEditParams): Promise<AiServiceResponse<string>> {
    try {
      this.logger.log('=== AI服务层开始处理编辑请求 ===');
      this.logger.log('编辑参数:', JSON.stringify(params, null, 2));

      const service = await this.aiServiceFactory.getDefaultTextService();
      this.logger.log('获取到默认文本服务:', service.constructor.name);

      const result = await service.editContent(params);
      
      this.logger.log('=== AI服务处理结果 ===');
      this.logger.log('处理结果:', JSON.stringify({
        success: result.success,
        hasData: !!result.data,
        error: result.error,
        usage: result.usage,
        metadata: result.metadata,
      }, null, 2));

      return result;
    } catch (error) {
      this.logger.error('=== AI服务处理失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);

      throw error;
    }
  }

  async generateScenes(
    theme: string,
    description: string,
    sceneCount: number,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200',
    keyWords?: string,
    subjectKnowledge?: string,
    vocabularyLevel?: string,
    mainCharacter?: string,
  ): Promise<AiServiceResponse<string>> {
    const service = await this.aiServiceFactory.getDefaultTextService();
    return service.generateScenes(
      theme,
      description,
      sceneCount,
      wordCount,
      keyWords,
      subjectKnowledge,
      vocabularyLevel,
      mainCharacter,
    );
  }

  async completeDescription(params: DescriptionCompleteParams): Promise<AiServiceResponse<string>> {
    try {
      this.logger.log('=== AI服务层开始处理描述补充请求 ===');
      this.logger.log('补充参数:', JSON.stringify(params, null, 2));

      const service = await this.aiServiceFactory.getDefaultTextService();
      this.logger.log('获取到默认文本服务:', service.constructor.name);

      const result = await service.completeDescription(params);
      
      this.logger.log('=== AI服务处理结果 ===');
      this.logger.log('处理结果:', JSON.stringify({
        success: result.success,
        hasData: !!result.data,
        error: result.error,
        usage: result.usage,
        metadata: result.metadata,
      }, null, 2));

      return result;
    } catch (error) {
      this.logger.error('=== AI服务处理失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);

      throw error;
    }
  }

  async chat(params: ChatDto) {
    try {
      // 获取默认的 AI 服务实例
      const service = await this.aiServiceFactory.getDefaultService();
      
      // 设置默认值
      const temperature = params.temperature || 0.7;
      const max_tokens = params.max_tokens || 1000;
      const character = params.character || '助教';

      // 调用服务进行对话
      const response = await service.chat({
        messages: params.messages,
        character,
        temperature,
        max_tokens,
      });

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      this.logger.error('AI 对话失败:', error);
      return {
        success: false,
        error: error.message || '对话失败，请稍后重试',
      };
    }
  }
} 