import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum ProviderType {
  OPENAI = 'openai',
  AZURE_OPENAI = 'azure_openai',
  ANTHROPIC = 'anthropic',
  MOONSHOT = 'moonshot',
  GEMINI = 'gemini',
  VOLCENGINE = 'volcengine',
  MISTRAL = 'mistral',
  DEEPSEEK = 'deepseek',
  FLUX = 'flux',
  SILICONFLOW = 'siliconflow',
  CUSTOM = 'custom',
}

export enum ProviderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TESTING = 'testing',
}

export enum ProviderDefaultType {
  TEXT = 'text',
  IMAGE = 'image',
}

export interface ProviderConfig {
  modelName?: string;
  organizationId?: string;
  deploymentName?: string;
  temperature?: number;
  maxTokens?: number;
  ratePerToken?: number;
  providerConfig?: {
    flux?: {
      model?: 'flux-schnell' | 'flux-dev' | 'wanx2.1-t2i-turbo' | 'wanx2.1-t2i-plus' | 'wanx2.0-t2i-turbo';
      size?: '512*1024' | '768*512' | '768*1024' | '1024*576' | '576*1024' | '1024*1024';
      steps?: number;
      guidance?: number;
      offload?: boolean;
      add_sampling_metadata?: boolean;
    };
    siliconflow?: {
      model?: string;
    };
  };
}

@Entity('ai_providers')
export class AiProvider {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ProviderType,
  })
  type: ProviderType;

  @Column({
    type: 'enum',
    enum: ProviderStatus,
    default: ProviderStatus.ACTIVE,
  })
  status: ProviderStatus;

  @Column()
  apiKey: string;

  @Column({ nullable: true })
  secretKey?: string;

  @Column({ nullable: true })
  apiEndpoint?: string;

  @Column({ default: 0 })
  requestCount: number;

  @Column('jsonb', { nullable: true })
  config: ProviderConfig;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  costAmount: number;

  @Column({ type: 'enum', enum: ProviderDefaultType, nullable: true, default: null })
  defaultType: ProviderDefaultType | null;

  @Column({ nullable: true })
  lastTestAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  lastTestResult: {
    success: boolean;
    message?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}