import { AiProvider } from '../entities/ai-provider.entity';

export type FluxModelType = 'flux-schnell' | 'flux-dev' | 'wanx2.1-t2i-turbo' | 'wanx2.1-t2i-plus' | 'wanx2.0-t2i-turbo';
export type FluxImageSize = '512*1024' | '768*512' | '768*1024' | '1024*576' | '576*1024' | '1024*1024' | string;

export interface ImageGenerateOptions {
  prompt: string;
  negativePrompt?: string;
  model?: FluxModelType;
  size?: FluxImageSize;
  steps?: number;
  seed?: number;
  count?: number;
  guidance?: number;
  offload?: boolean;
  add_sampling_metadata?: boolean;
  prompt_extend?: boolean; // 通义万相特有参数，是否开启智能改写
  watermark?: boolean; // 通义万相特有参数，是否添加水印
}

export interface ImageGenerateResponse {
  success: boolean;
  data?: string[];  // 图片URL数组
  error?: string;
  usage?: {
    totalTokens?: number;
    cost?: number;
  };
  requestId?: string;  // DashScope 请求ID
  taskId?: string;    // DashScope 任务ID
}

export interface ImageServiceOptions {
  provider: AiProvider;
}

export interface IImageService {
  generateImage(options: ImageGenerateOptions): Promise<ImageGenerateResponse>;
  checkStatus(): Promise<boolean>;
  getConfig(): ImageServiceOptions;
  updateConfig(options: Partial<ImageServiceOptions>): Promise<void>;
  getStats(): Promise<{ requestCount: number; costAmount: number; lastUsedAt?: Date }>;
}