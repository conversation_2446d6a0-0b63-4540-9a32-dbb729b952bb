import { AiProvider } from '../entities/ai-provider.entity';

export interface AiServiceOptions {
  provider: AiProvider;
  maxRetries?: number;
  timeout?: number;
  systemPrompt?: string;
  userPromptTemplate?: string;
}

export interface AiServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  usage?: {
    totalTokens: number;
    cost: number;
  };
  metadata?: {
    provider?: string;
    model?: string;
    timestamp?: string;
  };
}

export interface AiEditParams {
  content?: string;
  instruction: string;
  style?: 'cartoon' | 'watercolor' | 'children' | 'simple' | 'cute';
  character?: {
    name: string;
    type: string;
    appearance?: string;
    personality?: string;
    description?: string;
  };
}

export interface DescriptionCompleteParams {
  type: 'appearance' | 'personality';
  currentDescription: string;
  characterType?: string;  // 角色类型：human, animal, fantasy, other
  species?: string;        // 具体物种，如：狗、猫等
}

export type DescriptionCompleteResponse = AiServiceResponse<string>;

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ImageGenerateResponse extends AiServiceResponse<string[]> {
  metadata?: {
    provider?: string;
    model?: string;
    timestamp?: string;
    size?: string;
    steps?: number;
    guidance?: number;
  };
}

export interface ImageGenerateOptions {
  prompt: string;
  negativePrompt?: string;
  size?: string;
  steps?: number;
  guidance?: number;
  seed?: number;
  model?: string;
}

export interface IAiService {
  // 初始化服务
  initialize(options: AiServiceOptions): Promise<void>;

  // 编辑内容
  editContent(params: AiEditParams): Promise<AiServiceResponse<string>>;

  // 补充描述
  completeDescription(params: DescriptionCompleteParams): Promise<AiServiceResponse<string>>;

  // 生成场景
  generateScenes(
    theme: string,
    description: string,
    sceneCount: number,
    wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200',
    ageGroup: '2-4' | '3-6' | '7-9' | '10-12',
    keyWords?: string,
    subjectKnowledge?: string,
    vocabularyLevel?: string,
    mainCharacter?: string,
  ): Promise<AiServiceResponse<string>>;

  // 检查服务状态
  checkStatus(): Promise<boolean>;

  // 获取服务配置
  getConfig(): AiServiceOptions;

  // 更新服务配置
  updateConfig(options: Partial<AiServiceOptions>): Promise<void>;

  // 获取服务统计信息
  getStats(): Promise<{
    requestCount: number;
    costAmount: number;
    lastUsedAt?: Date;
  }>;
}