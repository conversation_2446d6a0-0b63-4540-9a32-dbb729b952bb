import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';

export enum DescriptionType {
  APPEARANCE = 'appearance',
  PERSONALITY = 'personality',
}

export class DescriptionCompleteParams {
  @ApiProperty({
    enum: DescriptionType,
    description: '补全类型：外貌或性格',
  })
  @IsEnum(DescriptionType)
  type: DescriptionType;

  @ApiProperty({
    description: '当前的描述文本',
  })
  @IsString()
  currentDescription: string;
}

export interface DescriptionCompleteResponse {
  success: boolean;
  data?: string;
  error?: string;
}