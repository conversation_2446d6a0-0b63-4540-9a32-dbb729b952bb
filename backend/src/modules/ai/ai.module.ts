import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiController } from './controllers/ai.controller';
import { AiImageController } from './controllers/ai-image.controller';
import { AiService } from './services/ai.service';
import { AiServiceFactory } from './services/factory/ai-service.factory';
import { AiProviderService } from './services/ai-provider.service';
import { AiProviderTestService } from './services/ai-provider-test.service';
import { AiProvider } from './entities/ai-provider.entity';
import { PromptManager } from './services/prompt/prompt-manager';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([AiProvider])
  ],
  controllers: [AiController, AiImageController],
  providers: [
    AiService,
    AiServiceFactory,
    AiProviderService,
    AiProviderTestService,
    PromptManager
  ],
  exports: [AiService, AiProviderService, AiProviderTestService, PromptManager],
})
export class AiModule {} 