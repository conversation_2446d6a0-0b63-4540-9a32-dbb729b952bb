import { Controller, Post, Body, UseGuards, BadRequestException, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, Min, Max, IsIn, IsArray, ValidateNested } from 'class-validator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AiService } from '../services/ai.service';
import { AiEditParams, AiServiceResponse, DescriptionCompleteParams, DescriptionCompleteResponse } from '../interfaces/ai-service.interface';
import { Type } from 'class-transformer';

export class GenerateScenesDto {
  @IsString()
  @IsNotEmpty()
  theme: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(10)
  sceneCount: number;

  @IsString()
  @IsNotEmpty()
  @IsIn(['single', '2-10', '10-30', '30-80', '80-200'])
  wordCount: 'single' | '2-10' | '10-30' | '30-80' | '80-200';

  @IsString()
  @IsOptional()
  keyWords?: string;

  @IsString()
  @IsOptional()
  subjectKnowledge?: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['zero', 'below100', '100to300', '300to800', '800to1500', 'above1500'])
  vocabularyLevel: string;

  @IsString()
  @IsOptional()
  mainCharacter?: string;
}

export class ChatDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  messages: Array<ChatMessageDto>;

  @IsString()
  @IsOptional()
  character?: string;

  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(2)
  temperature?: number;

  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(4000)
  max_tokens?: number;
}

export class ChatMessageDto {
  @IsString()
  @IsIn(['system', 'user', 'assistant'])
  role: 'system' | 'user' | 'assistant';

  @IsString()
  @IsNotEmpty()
  content: string;
}

@ApiTags('AI 服务')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiController {
  private readonly logger = new Logger(AiController.name);

  constructor(private readonly aiService: AiService) {}

  @Post('edit')
  @ApiOperation({ summary: '编辑内容' })
  @ApiResponse({ status: 200, description: '编辑成功' })
  async editContent(@Body() params: AiEditParams) {
    try {
      this.logger.log('=== 接收到AI编辑请求 ===');
      this.logger.log('请求参数:', JSON.stringify(params, null, 2));

      const result = await this.aiService.editContent(params);

      this.logger.log('=== AI编辑结果 ===');
      this.logger.log('处理结果:', JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      this.logger.error('=== AI编辑失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);

      throw new BadRequestException({
        success: false,
        error: error.message
      });
    }
  }

  @Post('generate-scenes')
  @ApiOperation({ summary: '生成绘本场景' })
  @ApiResponse({ status: 200, description: '生成成功' })
  async generateScenes(@Body() generateScenesDto: GenerateScenesDto) {
    try {
      this.logger.log('=== 接收到场景生成请求 ===');
      this.logger.log('请求参数:', JSON.stringify(generateScenesDto, null, 2));

      const result = await this.aiService.generateScenes(
        generateScenesDto.theme,
        generateScenesDto.description || '',
        generateScenesDto.sceneCount,
        generateScenesDto.wordCount,
        generateScenesDto.keyWords,
        generateScenesDto.subjectKnowledge,
        generateScenesDto.vocabularyLevel,
        generateScenesDto.mainCharacter,
      );

      this.logger.log('=== 场景生成结果 ===');
      this.logger.log('处理结果:', JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      this.logger.error('=== 场景生成失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);

      return {
        success: false,
        error: error instanceof Error ? error.message : '生成场景失败',
      };
    }
  }

  @Post('complete-description')
  @ApiOperation({ summary: '补全描述' })
  @ApiResponse({ status: 200, description: '补全成功' })
  async completeDescription(@Body() params: DescriptionCompleteParams): Promise<DescriptionCompleteResponse> {
    try {
      this.logger.log('=== 接收到描述补全请求 ===');
      this.logger.log('请求参数:', JSON.stringify(params, null, 2));
  
      const result = await this.aiService.completeDescription(params);
  
      this.logger.log('=== 描述补全结果 ===');
      this.logger.log('处理结果:', JSON.stringify(result, null, 2));
  
      return result;
    } catch (error) {
      this.logger.error('=== 描述补全失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);
  
      return {
        success: false,
        error: error instanceof Error ? error.message : '补全描述失败',
      };
    }
  }

  @Post('chat')
  @ApiOperation({ summary: '与 AI 助教对话' })
  async chat(@Body() chatDto: ChatDto) {
    try {
      this.logger.log('=== 接收到 AI 对话请求 ===');
      this.logger.log('请求参数:', JSON.stringify(chatDto, null, 2));

      if (!chatDto.messages || chatDto.messages.length === 0) {
        throw new BadRequestException('消息不能为空');
      }

      const result = await this.aiService.chat(chatDto);

      this.logger.log('=== AI 对话结果 ===');
      this.logger.log('处理结果:', JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      this.logger.error('=== AI 对话失败 ===');
      this.logger.error('错误类型:', error.constructor.name);
      this.logger.error('错误消息:', error.message);
      this.logger.error('错误堆栈:', error.stack);

      throw new BadRequestException({
        success: false,
        error: error instanceof Error ? error.message : 'AI 对话失败'
      });
    }
  }
}