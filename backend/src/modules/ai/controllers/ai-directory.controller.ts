import { Controller, Post, Body, UseGuards, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsN<PERSON>ber, <PERSON>Optional, <PERSON>, <PERSON> } from 'class-validator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AiSceneService } from '../services/ai-scene.service';

export class GenerateScenesDto {
  @IsString({ message: '主题必须是字符串' })
  @IsNotEmpty({ message: '主题不能为空' })
  theme: string;

  @IsString({ message: '描述必须是字符串' })
  @IsOptional()
  description?: string;

  @IsNumber({}, { message: '场景数量必须是数字' })
  @Min(1, { message: '场景数量最小为1' })
  @Max(8, { message: '场景数量最大为8' })
  sceneCount: number;
}

@ApiTags('AI 场景生成')
@Controller('ai/generate-scenes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiSceneController {
  constructor(private readonly aiSceneService: AiSceneService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成绘本场景' })
  @ApiResponse({ status: 200, description: '生成成功' })
  async generateScenes(@Body() generateScenesDto: GenerateScenesDto) {
    try {
      const { theme, description = '', sceneCount } = generateScenesDto;
      const content = await this.aiSceneService.generateScenes(
        theme,
        description,
        sceneCount,
        '2-10',
        '3-6'
      );
      return {
        success: true,
        data: content,
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        error: error.message
      });
    }
  }
} 