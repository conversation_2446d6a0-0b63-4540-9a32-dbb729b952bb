import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AiServiceFactory } from '../services/factory/ai-service.factory';
import { ImageGenerateOptions, ImageGenerateResponse } from '../interfaces/image-service.interface';

@ApiTags('AI 图片服务')
@Controller('ai/images')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiImageController {
  constructor(private readonly aiServiceFactory: AiServiceFactory) {}

  @Post('generate')
  @ApiOperation({ summary: '生成图片' })
  @ApiResponse({ status: 200, description: '生成成功' })
  async generateImage(@Body() options: ImageGenerateOptions): Promise<ImageGenerateResponse> {
    try {
      const imageService = await this.aiServiceFactory.getDefaultImageService();
      return await imageService.generateImage(options);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成图片失败',
      };
    }
  }
} 