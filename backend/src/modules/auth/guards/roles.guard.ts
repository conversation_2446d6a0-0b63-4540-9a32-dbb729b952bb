import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { UserRole } from '../../user/entities/user.entity';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    // 如果没有设置角色要求，默认允许访问
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 确保用户存在且有角色属性
    if (!user || !user.role) {
      throw new UnauthorizedException('未经授权的访问');
    }

    // 验证用户角色是否在允许的角色列表中
    const hasRole = requiredRoles.some((role) => {
      // 确保比较的是有效的角色枚举值
      if (!Object.values(UserRole).includes(role)) {
        console.warn(`Invalid role specified: ${role}`);
        return false;
      }
      return user.role === role;
    });

    if (!hasRole) {
      throw new UnauthorizedException('您没有足够的权限执行此操作');
    }

    return true;
  }
} 