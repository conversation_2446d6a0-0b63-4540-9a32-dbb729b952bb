import { Injectable, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AUTH_ERRORS } from '../constants/auth.constants';
import { Request } from 'express';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<Request>();
    
    this.logger.debug(`Processing request to ${request.url}`, {
      headers: request.headers,
      cookies: request.cookies,
    });

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    if (err) {
      this.logger.error(`Authentication error: ${err.message}`, {
        stack: err.stack,
        info: info?.message,
        timestamp: new Date().toISOString()
      });

      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedException(AUTH_ERRORS.TOKEN_EXPIRED);
      }
      if (err.name === 'JsonWebTokenError') {
        throw new UnauthorizedException(AUTH_ERRORS.INVALID_TOKEN);
      }
      throw err;
    }
    
    if (!user) {
      const errorInfo = {
        message: info?.message || 'No auth token found',
        timestamp: new Date().toISOString()
      };

      if (info) {
        this.logger.warn(`Authentication failed: ${info.message}`, errorInfo);
        
        if (info.name === 'TokenExpiredError') {
          throw new UnauthorizedException(AUTH_ERRORS.TOKEN_EXPIRED);
        }
        if (info.message.includes('No auth token')) {
          throw new UnauthorizedException(AUTH_ERRORS.UNAUTHORIZED);
        }
      }

      this.logger.warn(`Authentication failed: No user found`, errorInfo);
      throw new UnauthorizedException(AUTH_ERRORS.UNAUTHORIZED);
    }

    if (user.status === 'disabled') {
      this.logger.warn(`Disabled account attempted access: ${user.email}`, {
        userId: user.id,
        timestamp: new Date().toISOString()
      });
      throw new UnauthorizedException(AUTH_ERRORS.ACCOUNT_DISABLED);
    }

    this.logger.debug(`Authentication successful for user: ${user.email}`, {
      userId: user.id,
      role: user.role,
      timestamp: new Date().toISOString()
    });
    
    return user;
  }
} 