import { UserRole } from '../../user/entities/user.entity';

export const JWT_CONFIG = {
  secret: process.env.JWT_SECRET || 'your-secret-key',
  expiresIn: process.env.JWT_EXPIRATION ? `${process.env.JWT_EXPIRATION}s` : '1d',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
};

// 导出 UserRole 以保持向后兼容
export { UserRole };

export const AUTH_ERRORS = {
  // 认证相关错误
  INVALID_CREDENTIALS: {
    code: 'AUTH001',
    message: '邮箱或密码错误',
    description: '提供的登录凭证无效，请检查邮箱和密码是否正确'
  },
  USER_NOT_FOUND: {
    code: 'AUTH002',
    message: '用户不存在',
    description: '未找到对应的用户账号，请确认账号信息'
  },
  UNAUTHORIZED: {
    code: 'AUTH003',
    message: '未经授权的访问',
    description: '您没有权限访问此资源，请先登录或确认访问权限'
  },
  ACCOUNT_DISABLED: {
    code: 'AUTH004',
    message: '账号已被禁用',
    description: '您的账号已被禁用，请联系管理员'
  },
  TOKEN_EXPIRED: {
    code: 'AUTH005',
    message: '登录已过期',
    description: '您的登录状态已过期，请重新登录'
  },
  INVALID_TOKEN: {
    code: 'AUTH006',
    message: '无效的认证令牌',
    description: '认证令牌无效或已损坏，请重新登录'
  },
  PERMISSION_DENIED: {
    code: 'AUTH007',
    message: '权限不足',
    description: '您的账号权限不足，无法执行此操作'
  },
  SESSION_EXPIRED: {
    code: 'AUTH008',
    message: '会话已过期',
    description: '您的会话已过期，请重新登录'
  }
} as const; 