import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../../user/services/user.service';
import { UserRole, UserStatus } from '../../user/entities/user.entity';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private configService: ConfigService,
    private userService: UserService,
  ) {
    super({
      clientID: configService.get<string>('GOOGLE_CLIENT_ID'),
      clientSecret: configService.get<string>('GOOGLE_CLIENT_SECRET'),
      callbackURL: configService.get<string>('GOOGLE_CALLBACK_URL'),
      scope: ['email', 'profile'],
      passReqToCallback: true, // 启用以接收 req 参数
    });
  }

  async validate(
    req: any,
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { id, name, emails, photos } = profile;
    const email = emails[0].value;

    try {
      // Check if user already exists
      let user = await this.userService.findByEmail(email);

      if (!user) {
        // Create new user if doesn't exist
        user = await this.userService.create({
          email,
          name: `${name.givenName} ${name.familyName}`,
          avatar: photos[0]?.value,
          googleId: id,
          role: UserRole.USER,
          status: UserStatus.ACTIVE,
        });
      } else {
        // Update existing user with Google ID if not set
        if (!user.googleId) {
          await this.userService.update(user.id, { googleId: id });
        }
      }

      done(null, user);
    } catch (error) {
      done(error, false);
    }
  }
}