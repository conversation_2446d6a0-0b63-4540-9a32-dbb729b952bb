import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../services/auth.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import { AUTH_ERRORS } from '../constants/auth.constants';
import { Request } from 'express';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {
    const jwtSecret = configService.get('JWT_SECRET');
    
    super({
      jwtFromRequest: (req: Request) => {
        try {
          // 从 cookie 中获取 token
          const token = req.cookies?.['auth_token'];
          
          if (token) {
            this.logger.debug('Found token in cookie: auth_token');
            return token;
          }

          // 如果 cookie 中没有，则尝试从 Authorization header 中获取
          const headerToken = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
          if (headerToken) {
            this.logger.debug('Found token in Authorization header');
            return headerToken;
          }

          this.logger.warn('No token found in request');
          return null;
        } catch (error) {
          this.logger.error('Error extracting token:', error);
          return null;
        }
      },
      secretOrKey: jwtSecret,
      ignoreExpiration: false,
    });

    this.logger.log('JWT Strategy initialized', {
      secret: jwtSecret ? '***' : 'not set',
      timestamp: new Date().toISOString(),
    });
  }

  async validate(payload: JwtPayload) {
    try {
      this.logger.debug(`Validating token payload: ${JSON.stringify(payload)}`);
      
      const user = await this.authService.validateUser(payload.sub);
      if (!user) {
        this.logger.warn(`User not found for payload: ${JSON.stringify(payload)}`);
        throw new UnauthorizedException(AUTH_ERRORS.USER_NOT_FOUND);
      }

      this.logger.debug(`User validated successfully: ${JSON.stringify({
        id: user.id,
        email: user.email,
        role: user.role
      })}`);

      return user;
    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`, error.stack);
      throw new UnauthorizedException(AUTH_ERRORS.UNAUTHORIZED);
    }
  }
} 