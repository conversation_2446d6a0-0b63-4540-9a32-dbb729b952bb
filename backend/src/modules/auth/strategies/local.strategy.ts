import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../services/auth.service';
import { AUTH_ERRORS } from '../constants/auth.constants';
import { UserStatus } from '@/modules/user/entities/user.entity';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'email',
      passwordField: 'password',
      passReqToCallback: false,
    });
  }

  async validate(email: string, password: string): Promise<any> {
    const user = await this.authService.validateUser(email, password);
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      membershipLevel: user.membershipLevel,
      status: user.status,
      isActive: user.status === UserStatus.ACTIVE,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
