import { <PERSON><PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ example: '******' })
  @IsString()
  @MinLength(6, { message: '密码至少需要6个字符' })
  password: string;

  @ApiProperty({ example: '张三' })
  @IsString()
  @MinLength(2, { message: '用户名至少需要2个字符' })
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  avatar?: string;
}