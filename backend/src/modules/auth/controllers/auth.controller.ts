import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  UnauthorizedException,
  Request as NestRequest,
  HttpCode,
  HttpStatus,
  <PERSON>s,
  BadRequestException,
  Query,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  <PERSON>piOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiBody
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { LoginDto, RegisterDto } from '../dto/auth.dto';
import { VerificationService } from '../../verification/verification.service';
import { SendVerificationCodeDto } from '../../verification/dto/send-verification-code.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { AuthGuard } from '@nestjs/passport';
import { UserRole } from '../../user/entities/user.entity';
import { AUTH_ERRORS } from '../constants/auth.constants';
import { CurrentUser } from '../decorators/current-user.decorator';
import { User } from '../../user/entities/user.entity';
import { ENV_CONFIG } from '@/config/environment';

interface RequestWithUser extends Request {
  user: User;
}

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly verificationService: VerificationService,
  ) {}

  @Post('send-verification-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '发送邮箱验证码' })
  @ApiBody({ type: SendVerificationCodeDto })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async sendVerificationCode(@Body() dto: SendVerificationCodeDto) {
    const result = await this.verificationService.sendVerificationCode(dto.email);

    if (!result) {
      throw new BadRequestException('验证码发送失败，请稍后重试');
    }

    return { success: true, message: '验证码发送成功' };
  }

  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: 201,
    description: '注册成功',
    schema: {
      properties: {
        token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '邮箱已被注册' })
  async register(
    @Body() registerDto: RegisterDto,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.register(registerDto, response);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '邮箱或密码错误' })
  async login(
    @NestRequest() req: RequestWithUser,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.login(req.user, response);
  }

  @UseGuards(LocalAuthGuard)
  @Post('admin/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '管理员登录' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '邮箱或密码错误' })
  @ApiResponse({ status: 403, description: '非管理员账号' })
  async adminLogin(
    @NestRequest() req: RequestWithUser,
    @Res({ passthrough: true }) response: Response
  ) {
    const result = await this.authService.login(req.user, response);
    if (result.user.role !== UserRole.ADMIN) {
      throw new UnauthorizedException('该账号没有管理员权限');
    }
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        name: { type: 'string' },
        role: { type: 'string', enum: ['user', 'admin'] },
        avatar: { type: 'string', nullable: true },
        isActive: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async getCurrentUser(@CurrentUser() user: User) {
    const validatedUser = await this.authService.validateUser(user.id);
    return this.authService.formatUserResponse(validatedUser);
  }

  @UseGuards(JwtAuthGuard)
  @Post('refresh')
  @ApiOperation({ summary: '刷新Token' })
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: '刷新成功',
    schema: {
      properties: {
        token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            role: { type: 'string', enum: ['user', 'admin'] },
            avatar: { type: 'string', nullable: true },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async refreshToken(
    @CurrentUser() user: User,
    @Res({ passthrough: true }) response: Response
  ) {
    return this.authService.refreshToken(user.id, response);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @ApiOperation({ summary: '用户登出' })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: '登出成功' })
  @ApiResponse({ status: 401, description: '未登录或 Token 已过期' })
  async logout(@Res({ passthrough: true }) response: Response) {
    const envType = ENV_CONFIG.isDevelopment ? 'development' : 'production';
    const cookieConfig = {
      ...ENV_CONFIG.cookie.common,
      ...ENV_CONFIG.cookie[envType],
    };

    // 清理所有认证相关的 cookie
    response.clearCookie('auth_token', cookieConfig);
    response.clearCookie('auth.token', cookieConfig);

    console.log('[AuthController] 用户登出，清理 Cookie', {
      environment: process.env.NODE_ENV,
      isDevelopment: ENV_CONFIG.isDevelopment,
      cookieConfig,
      timestamp: new Date().toISOString(),
    });

    return { success: true };
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth 登录' })
  @ApiResponse({ status: 302, description: '重定向到 Google 认证页面' })
  async googleAuth(@Query('state') state?: string) {
    // Guard 将重定向到 Google，state 参数会被传递给回调
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth 回调' })
  @ApiResponse({ status: 302, description: '登录成功后重定向' })
  async googleAuthCallback(
    @NestRequest() req: RequestWithUser,
    @Res() response: Response,
    @Query('state') state?: string
  ) {
    const loginResult = await this.authService.login(req.user, response);
    
    // 重定向到前端页面，携带登录状态
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    
    // 根据 state 参数决定重定向目标
    let redirectPath = '/login';
    let queryParams = new URLSearchParams({ 
      success: 'true',
      // 在URL中传递token，确保前端能够接收到
      token: await this.authService.generateTokenForUser(req.user)
    });
    
    if (state) {
      try {
        const decodedState = decodeURIComponent(state);
        if (decodedState === 'admin') {
          redirectPath = '/admin/login';
        } else {
          // 如果 state 不是 'admin'，则作为 returnUrl 处理
          queryParams.set('returnUrl', decodedState);
        }
      } catch (error) {
        console.error('解码 state 参数失败:', error);
      }
    }
    
    response.redirect(`${frontendUrl}${redirectPath}?${queryParams.toString()}`);
  }
}