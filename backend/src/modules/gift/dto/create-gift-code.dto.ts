import { IsNotEmpty, IsU<PERSON>D, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateGiftCodeDto {
  @ApiProperty({ description: '课程ID' })
  @IsNotEmpty()
  @IsUUID()
  courseId: string;

  @ApiProperty({ description: '生成数量', minimum: 1, maximum: 100, default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  quantity?: number;

  @ApiProperty({ description: '有效天数', minimum: 1, maximum: 365 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(365)
  validDays: number;
} 