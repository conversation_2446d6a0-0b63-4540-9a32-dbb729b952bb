import { IsNotEmpty, IsUUID, IsString, IsEmail, IsArray, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GenerateInviteCodeDto {
  @ApiProperty({ description: '课程ID' })
  @IsNotEmpty()
  @IsUUID()
  courseId: string;

  @ApiProperty({ description: '用户姓名' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: '用户邮箱' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: '职业' })
  @IsNotEmpty()
  @IsString()
  profession: string;

  @ApiProperty({ description: 'AI经验水平', example: 'beginner' })
  @IsNotEmpty()
  @IsString()
  aiExperience: string;

  @ApiProperty({ description: '兴趣领域', type: [String] })
  @IsArray()
  @IsString({ each: true })
  interests: string[];

  @ApiProperty({ description: '对课程的期望' })
  @IsNotEmpty()
  @IsString()
  expectation: string;

  @ApiProperty({ description: '朋友姓名', required: false })
  @IsOptional()
  @IsString()
  friendName?: string;

  @ApiProperty({ description: '课程名称', required: false })
  @IsOptional()
  @IsString()
  courseName?: string;

  @ApiProperty({ description: '邀请原因', required: false })
  @IsOptional()
  @IsString()
  inviteReason?: string;
} 