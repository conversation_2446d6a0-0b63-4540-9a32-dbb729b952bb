import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GiftCode } from './entities/gift-code.entity';
import { GiftCodeService } from './services/gift-code.service';
import { GiftCodeController } from './controllers/gift-code.controller';
import { InviteCodeController } from './controllers/invite-code.controller';
import { UserModule } from '../user/user.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { CourseModule } from '../course/course.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([GiftCode]),
    UserModule,
    SubscriptionModule,
    CourseModule,
  ],
  controllers: [GiftCodeController, InviteCodeController],
  providers: [GiftCodeService],
  exports: [GiftCodeService],
})
export class GiftModule {} 