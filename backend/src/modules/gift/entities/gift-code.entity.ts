import { 
  <PERSON><PERSON><PERSON>, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Course } from '../../course/entities/course.entity';

@Entity('gift_codes')
export class GiftCode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string;

  @Column({ name: 'course_id' })
  courseId: string;

  @ManyToOne(() => Course)
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @Column({ default: false })
  isUsed: boolean;

  @Column({ type: 'timestamp', nullable: true })
  usedAt: Date;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'used_by' })
  usedBy: User;

  @Column({ default: 'gift' })
  type: string;

  @Column({ name: 'creator_id', nullable: true })
  creatorId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'creator_id' })
  creator: User;

  @Column({ name: 'friend_name', nullable: true })
  friendName: string;

  @Column({ name: 'invite_reason', nullable: true })
  inviteReason: string;

  @Column({ name: 'user_name', nullable: true })
  userName: string;

  @Column({ nullable: true })
  profession: string;

  @Column({ name: 'ai_experience', nullable: true })
  aiExperience: string;

  @Column({ type: 'simple-array', nullable: true })
  interests: string[];

  @Column({ nullable: true })
  expectation: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 