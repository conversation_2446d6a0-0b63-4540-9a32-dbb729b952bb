import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Delete,
  UseGuards,
  Res,
  ParseArrayPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { GiftCodeService } from '../services/gift-code.service';
import { CreateGiftCodeDto } from '../dto/create-gift-code.dto';
import { RedeemGiftCodeDto } from '../dto/redeem-gift-code.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User, UserRole } from '../../user/entities/user.entity';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('礼品码')
@Controller('gift-codes')
@UseGuards(JwtAuthGuard, RolesGuard)
export class GiftCodeController {
  constructor(private readonly giftCodeService: GiftCodeService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '创建礼品码' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async create(@Body() createGiftCodeDto: CreateGiftCodeDto) {
    return this.giftCodeService.create(createGiftCodeDto);
  }

  @Post('redeem')
  @ApiOperation({ summary: '兑换礼品码' })
  @ApiResponse({ status: 200, description: '兑换成功' })
  async redeem(
    @CurrentUser() user: User,
    @Body() redeemDto: RedeemGiftCodeDto,
  ) {
    return this.giftCodeService.redeem(user.id, redeemDto);
  }

  @Get()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '获取礼品码列表' })
  async findAll(
    @Query('page', new ParseIntPipe()) page = 1,
    @Query('pageSize', new ParseIntPipe()) pageSize = 10,
  ) {
    return this.giftCodeService.findAll(page, pageSize);
  }

  @Get('export')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '导出礼品码' })
  async exportToExcel(
    @Query('ids') ids: string,
    @Res() res: Response,
  ) {
    const buffer = await this.giftCodeService.exportToExcel({
      ids: ids ? ids.split(',') : undefined,
    });

    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename=gift-codes-${new Date().toISOString().split('T')[0]}.xlsx`,
      'Content-Length': buffer.length,
    });

    res.send(buffer);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '获取单个礼品码' })
  async findOne(@Param('id') id: string) {
    return this.giftCodeService.findOne(id);
  }

  @Delete('batch')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: '批量删除礼品码' })
  async deleteMany(
    @Body('ids', new ParseArrayPipe({ items: String })) ids: string[],
  ) {
    await this.giftCodeService.deleteMany(ids);
    return { message: '删除成功' };
  }
} 