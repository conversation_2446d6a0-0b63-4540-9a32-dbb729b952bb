import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
} from '@nestjs/common';
import { GiftCodeService } from '../services/gift-code.service';
import { GenerateInviteCodeDto } from '../dto/generate-invite-code.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User } from '../../user/entities/user.entity';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('邀请码')
@Controller('invite-codes')
export class InviteCodeController {
  constructor(private readonly giftCodeService: GiftCodeService) {}

  @Post('generate')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '生成邀请码' })
  @ApiResponse({ status: 201, description: '生成成功' })
  async generate(
    @CurrentUser() user: User,
    @Body() generateDto: GenerateInviteCodeDto,
  ) {
    const result = await this.giftCodeService.generateInviteCode(user.id, generateDto);
    return {
      inviteCode: result.code,
      expiresAt: result.expiresAt,
    };
  }

  @Get('validate/:code')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '验证邀请码' })
  @ApiResponse({ status: 200, description: '验证成功' })
  async validate(
    @CurrentUser() user: User,
    @Param('code') code: string,
  ) {
    return this.giftCodeService.validateInviteCode(user.id, code);
  }
} 