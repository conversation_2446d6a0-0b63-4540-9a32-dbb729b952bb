import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { GiftCode } from '../entities/gift-code.entity';
import { User } from '../../user/entities/user.entity';
import { CreateGiftCodeDto } from '../dto/create-gift-code.dto';
import { RedeemGiftCodeDto } from '../dto/redeem-gift-code.dto';
import { UserService } from '../../user/services/user.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { CourseService } from '../../course/services/course.service';
import { Course } from '../../course/entities/course.entity';
import { randomBytes } from 'crypto';
import * as ExcelJS from 'exceljs';
import { GenerateInviteCodeDto } from '../dto/generate-invite-code.dto';
import { ValidateInviteCodeDto } from '../dto/validate-invite-code.dto';

@Injectable()
export class GiftCodeService {
  private readonly logger = new Logger(GiftCodeService.name);

  constructor(
    @InjectRepository(GiftCode)
    private giftCodeRepository: Repository<GiftCode>,
    private userService: UserService,
    private subscriptionService: SubscriptionService,
    private courseService: CourseService,
    private dataSource: DataSource,
  ) {}

  async create(createGiftCodeDto: CreateGiftCodeDto): Promise<GiftCode[]> {
    this.logger.log(`开始创建礼品码 - ${JSON.stringify(createGiftCodeDto)}`);
    const quantity = createGiftCodeDto.quantity || 1;
    const giftCodes: GiftCode[] = [];
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 验证课程是否存在
      const course = await queryRunner.manager.findOne(Course, {
        where: { id: createGiftCodeDto.courseId }
      });

      if (!course) {
        throw new NotFoundException('课程不存在');
      }

      for (let i = 0; i < quantity; i++) {
        const code = this.generateUniqueCode();
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + createGiftCodeDto.validDays);

        const giftCode = queryRunner.manager.create(GiftCode, {
          code,
          courseId: createGiftCodeDto.courseId,
          expiresAt,
        });

        const savedGiftCode = await queryRunner.manager.save(GiftCode, giftCode);
        giftCodes.push(savedGiftCode);
        this.logger.log(`礼品码创建成功 - code: ${code}`);
      }

      await queryRunner.commitTransaction();
      return giftCodes;
    } catch (error) {
      this.logger.error('创建礼品码失败', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private generateUniqueCode(): string {
    return randomBytes(4).toString('hex').toUpperCase();
  }

  async redeem(userId: string, redeemDto: RedeemGiftCodeDto): Promise<any> {
    this.logger.log(`开始兑换礼品码/邀请码 - userId: ${userId}, code: ${redeemDto.code}`);
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    this.logger.log('事务已开启');

    try {
      // 1. 先查找礼品码/邀请码记录
      const giftCode = await queryRunner.manager
        .createQueryBuilder(GiftCode, 'giftCode')
        .innerJoinAndSelect('giftCode.course', 'course')
        .where('giftCode.code = :code', { code: redeemDto.code })
        .setLock('pessimistic_write')
        .getOne();

      if (!giftCode) {
        throw new NotFoundException('礼品券/邀请码不存在');
      }

      if (giftCode.isUsed) {
        throw new BadRequestException('礼品券/邀请码已被使用');
      }

      if (giftCode.expiresAt < new Date()) {
        throw new BadRequestException('礼品券/邀请码已过期');
      }

      // 如果是邀请码，检查用户不能使用自己生成的邀请码
      if (giftCode.type === 'invite' && giftCode.creatorId === userId) {
        throw new BadRequestException('不能使用自己生成的邀请码');
      }

      // 2. 查找用户
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 3. 检查用户是否已经拥有此课程
      const hasAccess = await this.courseService.hasUserPurchased(userId, giftCode.courseId);

      if (hasAccess) {
        throw new BadRequestException('您已拥有此课程');
      }

      // 4. 授予课程访问权限
      await this.courseService.grantCourseAccess(
        userId,
        giftCode.courseId,
        queryRunner.manager
      );

      // 5. 更新礼品码/邀请码状态
      giftCode.isUsed = true;
      giftCode.usedAt = new Date();
      giftCode.usedBy = user;

      await queryRunner.manager.save(GiftCode, giftCode);
      this.logger.log(`礼品码/邀请码状态已更新 - code: ${redeemDto.code}`);

      // 6. 提交事务
      await queryRunner.commitTransaction();
      this.logger.log('事务提交成功');

      // 返回更多课程信息
      return { 
        courseId: giftCode.courseId,
        course: {
          title: giftCode.course.title,
          description: giftCode.course.description,
          level: giftCode.course.level,
          duration: giftCode.course.duration,
          lessonsCount: giftCode.course.lessonsCount
        }
      };
    } catch (error) {
      this.logger.error(`兑换失败 - ${error.message}`, error.stack);
      await queryRunner.rollbackTransaction();
      this.logger.log('事务已回滚');
      throw error;
    } finally {
      await queryRunner.release();
      this.logger.log('查询运行器已释放');
    }
  }

  async findAll(page = 1, pageSize = 10): Promise<{ data: GiftCode[]; total: number }> {
    this.logger.log(`查询礼品码列表 - page: ${page}, pageSize: ${pageSize}`);
    
    const [data, total] = await this.giftCodeRepository.findAndCount({
      relations: ['usedBy', 'course'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    return { data, total };
  }

  async findOne(id: string): Promise<GiftCode> {
    this.logger.log(`查询单个礼品码 - id: ${id}`);
    
    const giftCode = await this.giftCodeRepository.findOne({
      where: { id },
      relations: ['usedBy'],
    });

    if (!giftCode) {
      this.logger.warn(`礼品码不存在 - id: ${id}`);
      throw new NotFoundException('礼品券不存在');
    }

    return giftCode;
  }

  async exportToExcel(options: { ids?: string[] } = {}): Promise<Buffer> {
    this.logger.log(`导出礼品码到Excel - ids: ${options.ids?.join(',')}`);
    
    const query = this.giftCodeRepository
      .createQueryBuilder('giftCode')
      .leftJoinAndSelect('giftCode.usedBy', 'usedBy')
      .leftJoinAndSelect('giftCode.course', 'course')
      .orderBy('giftCode.createdAt', 'DESC');

    if (options.ids && options.ids.length > 0) {
      query.andWhere('giftCode.id IN (:...ids)', { ids: options.ids });
    }

    const giftCodes = await query.getMany();
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('礼品码');

    worksheet.columns = [
      { header: '礼品码', key: 'code', width: 20 },
      { header: '课程名称', key: 'courseName', width: 30 },
      { header: '过期时间', key: 'expiresAt', width: 20 },
      { header: '状态', key: 'status', width: 15 },
      { header: '使用时间', key: 'usedAt', width: 20 },
      { header: '使用者', key: 'usedBy', width: 30 },
      { header: '创建时间', key: 'createdAt', width: 20 },
    ];

    giftCodes.forEach(code => {
      worksheet.addRow({
        code: code.code,
        courseName: code.course?.title || '未知课程',
        expiresAt: code.expiresAt ? new Date(code.expiresAt).toLocaleString() : '',
        status: code.isUsed
          ? '已使用'
          : code.expiresAt && code.expiresAt < new Date()
          ? '已过期'
          : '未使用',
        usedAt: code.usedAt ? new Date(code.usedAt).toLocaleString() : '',
        usedBy: code.usedBy ? code.usedBy.email : '',
        createdAt: new Date(code.createdAt).toLocaleString(),
      });
    });

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    return await workbook.xlsx.writeBuffer() as Buffer;
  }

  async deleteMany(ids: string[]): Promise<void> {
    this.logger.log(`批量删除礼品码 - ids: ${ids.join(', ')}`);
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const usedCodes = await queryRunner.manager.find(GiftCode, {
        where: {
          id: In(ids),
          isUsed: true,
        },
      });

      if (usedCodes.length > 0) {
        throw new BadRequestException('不能删除已使用的礼品码');
      }

      await queryRunner.manager.delete(GiftCode, { id: In(ids) });
      await queryRunner.commitTransaction();
      this.logger.log(`成功删除 ${ids.length} 个礼品码`);
    } catch (error) {
      this.logger.error('批量删除礼品码失败', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 生成邀请码
  async generateInviteCode(userId: string, dto: GenerateInviteCodeDto): Promise<GiftCode> {
    this.logger.log(`开始生成邀请码 - userId: ${userId}, courseId: ${dto.courseId}`);
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 验证用户是否存在
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 验证课程是否存在
      const course = await queryRunner.manager.findOne(Course, {
        where: { id: dto.courseId }
      });

      if (!course) {
        throw new NotFoundException('课程不存在');
      }

      // 生成唯一邀请码
      let code = this.generateUniqueCode();
      let existingCode = await queryRunner.manager.findOne(GiftCode, {
        where: { code }
      });

      // 确保邀请码唯一
      while (existingCode) {
        code = this.generateUniqueCode();
        existingCode = await queryRunner.manager.findOne(GiftCode, {
          where: { code }
        });
      }

      // 创建邀请码记录
      const expiresAt = new Date();
      expiresAt.setMonth(expiresAt.getMonth() + 3); // 有效期3个月

      const giftCode = new GiftCode();
      giftCode.code = code;
      giftCode.courseId = dto.courseId;
      giftCode.type = 'invite';
      giftCode.creatorId = userId;
      giftCode.expiresAt = expiresAt;
      
      // 保存邀请相关信息
      giftCode.friendName = dto.friendName || '朋友';
      giftCode.inviteReason = dto.inviteReason || '我觉得这门课程非常有价值，希望我们能一起学习，共同进步。';
      giftCode.userName = dto.name;
      
      // 保存用户信息
      giftCode.profession = dto.profession;
      giftCode.aiExperience = dto.aiExperience;
      giftCode.interests = dto.interests;
      giftCode.expectation = dto.expectation;

      const savedGiftCode = await queryRunner.manager.save(GiftCode, giftCode);
      this.logger.log(`邀请码已生成 - code: ${code}`);

      await queryRunner.commitTransaction();
      this.logger.log('事务提交成功');

      return savedGiftCode;
    } catch (error) {
      this.logger.error(`生成邀请码失败 - ${error.message}`, error.stack);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 验证邀请码
  async validateInviteCode(userId: string, code: string): Promise<{ 
    isValid: boolean; 
    isOwnCode: boolean;
    message?: string;
    courseId?: string;
    courseName?: string;
  }> {
    this.logger.log(`验证邀请码 - userId: ${userId}, code: ${code}`);

    // 查找邀请码记录
    const giftCode = await this.giftCodeRepository
      .createQueryBuilder('giftCode')
      .leftJoinAndSelect('giftCode.course', 'course')
      .where('giftCode.code = :code', { code })
      .getOne();

    if (!giftCode) {
      return { 
        isValid: false, 
        isOwnCode: false,
        message: '邀请码不存在' 
      };
    }

    // 检查是否是邀请码
    if (giftCode.type !== 'invite') {
      return { 
        isValid: false, 
        isOwnCode: false,
        message: '无效的邀请码' 
      };
    }

    // 检查是否已使用
    if (giftCode.isUsed) {
      return { 
        isValid: false, 
        isOwnCode: false,
        message: '邀请码已被使用' 
      };
    }

    // 检查是否过期
    if (giftCode.expiresAt < new Date()) {
      return { 
        isValid: false, 
        isOwnCode: false,
        message: '邀请码已过期' 
      };
    }

    // 检查是否是用户自己生成的邀请码
    if (giftCode.creatorId === userId) {
      return { 
        isValid: true, 
        isOwnCode: true,
        message: '不能使用自己生成的邀请码',
        courseId: giftCode.courseId,
        courseName: giftCode.course?.title
      };
    }

    // 检查用户是否已经拥有此课程
    const hasAccess = await this.courseService.hasUserPurchased(userId, giftCode.courseId);

    if (hasAccess) {
      return { 
        isValid: false, 
        isOwnCode: false,
        message: '您已拥有此课程',
        courseId: giftCode.courseId,
        courseName: giftCode.course?.title
      };
    }

    return { 
      isValid: true, 
      isOwnCode: false,
      courseId: giftCode.courseId,
      courseName: giftCode.course?.title
    };
  }
} 