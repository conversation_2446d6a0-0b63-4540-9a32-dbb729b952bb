import { IsString, IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProviderType, ProviderStatus, ProviderDefaultType } from '@/modules/ai/entities/ai-provider.entity';

export class CreateAiProviderDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: ProviderType })
  @IsEnum(ProviderType)
  type: ProviderType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiPropertyOptional({ enum: ProviderDefaultType })
  @IsOptional()
  @IsEnum(ProviderDefaultType)
  defaultType?: ProviderDefaultType;

  @ApiPropertyOptional()
  @IsOptional()
  config?: Record<string, any>;
}

export class UpdateAiProviderDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: ProviderStatus })
  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiPropertyOptional({ enum: ProviderDefaultType })
  @IsOptional()
  @IsEnum(ProviderDefaultType)
  defaultType?: ProviderDefaultType;

  @ApiPropertyOptional()
  @IsOptional()
  config?: Record<string, any>;
}

export class AiProviderResponseDto {
  id: string;
  name: string;
  type: ProviderType;
  status: ProviderStatus;
  apiEndpoint?: string;
  requestCount: number;
  costAmount: number;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
} 