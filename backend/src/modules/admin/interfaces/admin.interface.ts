import { UserRole } from '../../user/entities/user.entity';

export interface IAdminUser {
  id: string;
  email: string;
  name?: string;
  role?: UserRole;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IAdminUserUpdate {
  name?: string;
  role?: UserRole;
  status?: string;
}

export interface IAdminUserQuery {
  page?: number;
  pageSize?: number;
  email?: string;
  role?: UserRole;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date;
}

export interface IPaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
} 