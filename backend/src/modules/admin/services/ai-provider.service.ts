import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { AiProvider, ProviderStatus, ProviderType, ProviderDefaultType } from '@/modules/ai/entities/ai-provider.entity';
import { CreateAiProviderDto, UpdateAiProviderDto } from '../dto/ai-provider.dto';
import { AiProviderService } from '@/modules/ai/services/ai-provider.service';

@Injectable()
export class AdminAiProviderService {
  constructor(
    private readonly aiProviderService: AiProviderService,
    @InjectRepository(AiProvider)
    private readonly aiProviderRepository: Repository<AiProvider>,
  ) {}

  async create(createDto: CreateAiProviderDto): Promise<AiProvider> {
    if (createDto.isDefault) {
      const defaultType = createDto.type === ProviderType.FLUX 
        ? ProviderDefaultType.IMAGE 
        : ProviderDefaultType.TEXT;

      // 重置同类型的其他服务商的默认状态
      await this.aiProviderRepository.update(
        { defaultType },
        { defaultType: null }
      );

      createDto.defaultType = defaultType;
      delete createDto.isDefault;
    }

    const provider = this.aiProviderRepository.create({
      ...createDto,
      status: ProviderStatus.TESTING,
    });
    return this.aiProviderRepository.save(provider);
  }

  async findAll(): Promise<AiProvider[]> {
    return this.aiProviderService.findAll();
  }

  async findOne(id: string): Promise<AiProvider> {
    return this.aiProviderService.findOne(id);
  }

  async update(id: string, updateDto: UpdateAiProviderDto): Promise<AiProvider> {
    const provider = await this.findOne(id);

    if (updateDto.isDefault) {
      const defaultType = provider.type === ProviderType.FLUX 
        ? ProviderDefaultType.IMAGE 
        : ProviderDefaultType.TEXT;

      // 重置同类型的其他服务商的默认状态
      await this.aiProviderRepository.update(
        { defaultType },
        { defaultType: null }
      );

      updateDto.defaultType = defaultType;
      delete updateDto.isDefault;
    }

    Object.assign(provider, updateDto);
    return this.aiProviderRepository.save(provider);
  }

  async remove(id: string): Promise<void> {
    const provider = await this.findOne(id);
    await this.aiProviderRepository.remove(provider);
  }

  async setDefault(id: string): Promise<AiProvider> {
    const provider = await this.findOne(id);

    if (provider.status !== ProviderStatus.ACTIVE) {
      throw new ConflictException('只能将活跃状态的服务商设为默认');
    }

    const defaultType = provider.type === ProviderType.FLUX 
      ? ProviderDefaultType.IMAGE 
      : ProviderDefaultType.TEXT;

    // 重置同类型的其他服务商的默认状态
    await this.aiProviderRepository.update(
      { defaultType },
      { defaultType: null }
    );

    // 设置新的默认服务商
    provider.defaultType = defaultType;
    await this.aiProviderRepository.save(provider);

    return provider;
  }

  async updateUsageStats(
    id: string,
    requestCount: number,
    costAmount: number
  ): Promise<void> {
    await this.aiProviderRepository.increment(
      { id },
      'requestCount',
      requestCount
    );
    
    await this.aiProviderRepository
      .createQueryBuilder()
      .update(AiProvider)
      .set({
        costAmount: () => `cost_amount + ${costAmount}`,
      })
      .where('id = :id', { id })
      .execute();
  }

  async updateTestResult(
    id: string,
    testResult: {
      success: boolean;
      details?: string;
    }
  ): Promise<void> {
    await this.aiProviderRepository.update(id, {
      lastTestAt: new Date(),
      lastTestResult: testResult,
      status: testResult.success ? ProviderStatus.ACTIVE : ProviderStatus.TESTING,
    });
  }
} 