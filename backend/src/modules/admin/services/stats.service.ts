import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';

@Injectable()
export class StatsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async getAdminStats() {
    const [userCount] = await Promise.all([
      this.userRepository.count(),
    ]);

    return {
      users: {
        total: userCount,
      },
    };
  }
} 