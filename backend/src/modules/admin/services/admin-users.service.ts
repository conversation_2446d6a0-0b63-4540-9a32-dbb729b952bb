import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { User, UserRole, UserStatus } from '@/modules/user/entities/user.entity';
import { CreateAdminDto, UpdateUserDto } from '../dto/admin.dto';
import { hashPassword } from '@/common/utils/password';

@Injectable()
export class AdminUsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async findAll(options?: { select?: (keyof User)[] }): Promise<User[]> {
    return this.userRepository.find({
      select: [
        'id',
        'email',
        'name',
        'avatar',
        'role',
        'status',
        'lastLoginAt',
        'createdAt',
        'updatedAt'
      ],
    });
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id',
        'email',
        'name',
        'role',
        'status',
        'createdAt',
        'lastLoginAt',
      ],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  async create(createUserDto: CreateAdminDto): Promise<User> {
    // 检查邮箱是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('该邮箱已被注册');
    }

    // 创建新用户
    const user = this.userRepository.create({
      ...createUserDto,
      password: await hashPassword(createUserDto.password),
      role: createUserDto.role || UserRole.USER,
      status: UserStatus.ACTIVE,
    });

    await this.userRepository.save(user);

    // 返回用户信息(不包含密码)
    const { password, ...result } = user;
    return result as User;
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // 检查是否是最后一个管理员
    if (user.role === UserRole.ADMIN && updateUserDto.role === UserRole.USER) {
      const adminCount = await this.userRepository.count({
        where: { role: UserRole.ADMIN }
      });
      
      if (adminCount <= 1) {
        throw new ForbiddenException('系统必须保留至少一个管理员账号');
      }
    }

    // 创建更新数据对象，只包含实际需要更新的字段
    const updateData: Partial<User> = {};
    
    if (updateUserDto.name !== undefined) {
      updateData.name = updateUserDto.name;
    }
    
    if (updateUserDto.isActive !== undefined) {
      updateData.status = updateUserDto.isActive ? UserStatus.ACTIVE : UserStatus.INACTIVE;
    }
    
    if (updateUserDto.role !== undefined) {
      updateData.role = updateUserDto.role;
    }

    // 更新用户信息
    await this.userRepository.update(id, updateData);

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);

    // 检查是否是最后一个管理员
    if (user.role === UserRole.ADMIN) {
      const adminCount = await this.userRepository.count({
        where: { role: UserRole.ADMIN }
      });
      
      if (adminCount <= 1) {
        throw new ForbiddenException('不能删除最后一个管理员账号');
      }
    }

    await this.userRepository.remove(user);
  }
} 