import { Controller, Get, Post, Body, Param, Patch, Delete, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { AdminGuard } from '@/common/guards/admin.guard';
import { CatalogueService } from '@/modules/course/services/catalogue.service';
import { CreateCatalogueDto, UpdateCatalogueDto } from '@/modules/course/dto/catalogue.dto';

@Controller('admin/categories')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminCategoriesController {
  constructor(private readonly catalogueService: CatalogueService) {}

  @Get()
  async findAll() {
    return this.catalogueService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.catalogueService.findOne(id);
  }

  @Post()
  async create(@Body() createCatalogueDto: CreateCatalogueDto) {
    return this.catalogueService.create(createCatalogueDto);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCatalogueDto: UpdateCatalogueDto,
  ) {
    return this.catalogueService.update(id, updateCatalogueDto);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.catalogueService.delete(id);
  }
} 