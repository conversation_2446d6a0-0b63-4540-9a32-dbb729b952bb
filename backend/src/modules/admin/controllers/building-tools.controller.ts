import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { AdminGuard } from '@/common/guards/admin.guard';
import { BuildingToolService, CreateBuildingToolDto, UpdateBuildingToolDto } from '@/modules/course/services/building-tool.service';

@ApiTags('管理员-建站工具')
@Controller('admin/building-tools')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminBuildingToolsController {
  constructor(private readonly buildingToolService: BuildingToolService) {}

  @Get()
  @ApiOperation({ summary: '获取所有建站工具' })
  async findAll() {
    return this.buildingToolService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个建站工具' })
  async findOne(@Param('id') id: string) {
    return this.buildingToolService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: '创建建站工具' })
  async create(@Body() createBuildingToolDto: CreateBuildingToolDto) {
    return this.buildingToolService.create(createBuildingToolDto);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新建站工具' })
  async update(
    @Param('id') id: string,
    @Body() updateBuildingToolDto: UpdateBuildingToolDto,
  ) {
    return this.buildingToolService.update(id, updateBuildingToolDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除建站工具' })
  async delete(@Param('id') id: string) {
    await this.buildingToolService.delete(id);
    return { message: '删除成功' };
  }

  @Post('courses/:courseId/tools')
  @ApiOperation({ summary: '为课程分配建站工具' })
  async assignToolsToCourse(
    @Param('courseId') courseId: string,
    @Body() data: { toolIds: string[] },
  ) {
    await this.buildingToolService.updateToolsForCourse(courseId, data.toolIds);
    return { message: '分配成功' };
  }

  @Post('courses/:courseId/tools/:toolId')
  @ApiOperation({ summary: '为课程添加单个建站工具' })
  async assignToolToCourse(
    @Param('courseId') courseId: string,
    @Param('toolId') toolId: string,
  ) {
    await this.buildingToolService.assignToolToCourse(toolId, courseId);
    return { message: '添加成功' };
  }

  @Delete('courses/:courseId/tools/:toolId')
  @ApiOperation({ summary: '从课程移除建站工具' })
  async removeToolFromCourse(
    @Param('courseId') courseId: string,
    @Param('toolId') toolId: string,
  ) {
    await this.buildingToolService.removeToolFromCourse(toolId, courseId);
    return { message: '移除成功' };
  }

  @Get('courses/:courseId/tools')
  @ApiOperation({ summary: '获取课程的建站工具' })
  async getCourseTools(@Param('courseId') courseId: string) {
    return this.buildingToolService.getToolsByCourse(courseId);
  }
} 