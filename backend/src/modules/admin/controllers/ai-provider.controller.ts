import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/user/entities/user.entity';
import { AiProviderService } from '@/modules/ai/services/ai-provider.service';
import { AiProviderTestService, TestResult } from '@/modules/ai/services/ai-provider-test.service';
import { AiProvider } from '@/modules/ai/entities/ai-provider.entity';

@ApiTags('AI Provider')
@Controller('admin/ai-providers')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
@Roles(UserRole.ADMIN)
export class AiProviderController {
  constructor(
    private readonly aiProviderService: AiProviderService,
    private readonly aiProviderTestService: AiProviderTestService,
  ) {}

  @Get()
  @ApiOperation({ summary: '获取所有AI服务商' })
  async findAll(): Promise<AiProvider[]> {
    return this.aiProviderService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取指定AI服务商' })
  async findOne(@Param('id') id: string): Promise<AiProvider> {
    return this.aiProviderService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: '创建AI服务商' })
  async create(@Body() data: Partial<AiProvider>): Promise<AiProvider> {
    return this.aiProviderService.create(data);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新AI服务商' })
  async update(
    @Param('id') id: string,
    @Body() data: Partial<AiProvider>,
  ): Promise<AiProvider> {
    return this.aiProviderService.update(id, data);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除AI服务商' })
  async delete(@Param('id') id: string): Promise<void> {
    return this.aiProviderService.delete(id);
  }

  @Post(':id/test')
  @ApiOperation({ summary: '测试服务提供商连接' })
  async testProvider(@Param('id') id: string): Promise<TestResult> {
    const provider = await this.aiProviderService.findOne(id);
    if (!provider) {
      throw new NotFoundException('Provider not found');
    }
    return this.aiProviderTestService.testConnection(provider);
  }

  @Put(':id/default')
  @ApiOperation({ summary: '设置为默认服务商' })
  async setDefault(@Param('id') id: string): Promise<AiProvider> {
    return this.aiProviderService.setDefault(id);
  }
} 