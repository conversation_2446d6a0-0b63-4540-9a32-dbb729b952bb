import { UserRole } from '../entities/user.entity';

export interface IUser {
  id: string;
  email: string;
  password: string;
  name?: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserResponse {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserProfile {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
}

export interface IChangePassword {
  oldPassword: string;
  newPassword: string;
} 