import { UserRole } from '../entities/user.entity';

export const USER_CONSTANTS = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 100,
  DEFAULT_ROLE: UserRole.USER,
} as const;

export const USER_MESSAGES = {
  NOT_FOUND: 'User not found',
  EMAIL_EXISTS: 'Email already exists',
  INVALID_PASSWORD: 'Invalid password',
  PASSWORD_TOO_SHORT: 'Password is too short',
  NAME_TOO_SHORT: 'Name is too short',
} as const;

export const USER_SELECT_FIELDS = {
  DEFAULT: [
    'id',
    'email',
    'name',
    'avatar',
    'role',
    'membershipLevel',
    'isActive',
    'createdAt',
    'updatedAt',
  ],
  PROFILE: [
    'id',
    'email',
    'name',
    'avatar',
    'role',
    'membershipLevel',
    'isActive',
  ],
  ADMIN: [
    'id',
    'email',
    'name',
    'avatar',
    'role',
    'membershipLevel',
    'isActive',
    'createdAt',
    'updatedAt',
  ],
} as const; 