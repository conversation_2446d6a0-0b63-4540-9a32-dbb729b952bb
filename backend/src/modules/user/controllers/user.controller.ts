import { Controller, Get, Put, Body, UseGuards, Request, Patch } from '@nestjs/common';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { UserService } from '../services/user.service';
import { UpdateProfileDto, UpdatePasswordDto } from '../dto/user.dto';
import { RequestWithUser } from '../../../common/interfaces/request.interface';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { UserStatus } from '../entities/user.entity';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('profile')
  @ApiOperation({ summary: '获取用户个人资料' })
  async getProfile(@Request() req: RequestWithUser) {
    return this.userService.findById(req.user.id);
  }

  @Patch('profile')
  @ApiOperation({ summary: '更新个人资料' })
  async updateProfile(
    @Request() req: RequestWithUser,
    @Body() updateProfileDto: UpdateProfileDto
  ) {
    const user = await this.userService.findById(req.user.id);
    return this.userService.updateProfile(user.id, {
      ...updateProfileDto,
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: user.status === UserStatus.ACTIVE
    });
  }

  @Put('password')
  @ApiOperation({ summary: '修改密码' })
  async updatePassword(
    @Request() req: RequestWithUser,
    @Body() updatePasswordDto: UpdatePasswordDto,
  ) {
    return this.userService.updatePassword(
      req.user.id,
      updatePasswordDto.oldPassword,
      updatePasswordDto.newPassword
    );
  }
} 