import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { UserCourse } from '../../course/entities/user-course.entity';
import { Course } from '../../course/entities/course.entity';
import { CourseLesson } from '../../course/entities/course-lesson.entity';

@Injectable()
export class StatsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserCourse)
    private readonly userCourseRepository: Repository<UserCourse>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(CourseLesson)
    private readonly lessonRepository: Repository<CourseLesson>,
  ) {}

  async getUserStats(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return {
      user: {
        id: user.id,
        email: user.email,
        createdAt: user.createdAt
      },
      stats: {
        lastLoginAt: user.lastLoginAt,
        loginCount: user.loginCount
      }
    };
  }

  async getSystemStats() {
    const totalUsers = await this.userRepository.count();
    const activeUsers = await this.userRepository.count({
      where: {
        lastLoginAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
      }
    });

    return {
      users: {
        total: totalUsers,
        active: activeUsers
      }
    };
  }

  async getDashboardStats(userId: string) {
    // 获取用户的所有课程记录
    const userCourses = await this.userCourseRepository.find({
      where: { userId },
      relations: ['course'],
    });

    // 计算已学课程数（progress > 0 的课程）
    const studiedCourses = userCourses.filter(uc => uc.progress > 0);

    // 获取最近更新的课程（按更新时间排序，取前5个）
    const recentCourses = [...userCourses]
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      .slice(0, 5);

    return {
      totalCourses: userCourses.length,
      studiedCourses: studiedCourses.length,
      totalProgress: this.calculateAverageProgress(userCourses),
      recentUpdateTime: userCourses.length > 0 ? 
        Math.max(...userCourses.map(uc => uc.updatedAt.getTime())) : 
        null,
    };
  }

  async getRecentLearning(userId: string) {
    // 获取用户最近学习的课程记录
    const recentCourses = await this.userCourseRepository.find({
      where: { userId },
      relations: ['course'],
      order: { updatedAt: 'DESC' },
      take: 5,
    });

    // 获取每个课程的最后访问章节信息
    const recentLearning = await Promise.all(
      recentCourses.map(async (userCourse) => {
        let lastLesson = null;
        if (userCourse.lastVisitedLessonId) {
          lastLesson = await this.lessonRepository.findOne({
            where: { id: userCourse.lastVisitedLessonId }
          });
        }

        // 确保进度值不超过100%
        const progress = Math.min(Math.max(userCourse.progress || 0, 0), 100);

        return {
          courseId: userCourse.courseId,
          courseName: userCourse.course.title,
          courseCode: userCourse.course.code,
          progress,
          lastVisitTime: userCourse.updatedAt,
          lastLesson: lastLesson ? {
            id: lastLesson.id,
            title: lastLesson.title,
            order: lastLesson.order,
          } : null,
        };
      })
    );

    return recentLearning;
  }

  private calculateAverageProgress(userCourses: UserCourse[]): number {
    if (userCourses.length === 0) return 0;
    
    // 确保每个课程的进度不超过100%
    const validProgress = userCourses.map(uc => Math.min(Math.max(uc.progress, 0), 100));
    const totalProgress = validProgress.reduce((sum, progress) => sum + progress, 0);
    
    // 确保平均进度也不超过100%
    return Math.min(Math.round(totalProgress / userCourses.length), 100);
  }
} 