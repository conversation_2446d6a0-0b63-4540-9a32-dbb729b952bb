import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StatsController } from './controllers/stats.controller';
import { StatsService } from './services/stats.service';
import { User } from '../user/entities/user.entity';
import { UserCourse } from '../course/entities/user-course.entity';
import { Course } from '../course/entities/course.entity';
import { CourseLesson } from '../course/entities/course-lesson.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserCourse,
      Course,
      CourseLesson,
    ]),
  ],
  controllers: [StatsController],
  providers: [StatsService],
  exports: [StatsService],
})
export class StatsModule {} 