import { Controller, Get, UseGuards } from '@nestjs/common';
import { Api<PERSON>earerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/user/entities/user.entity';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { StatsService } from '../services/stats.service';
import { User } from '../../user/entities/user.entity';

@ApiTags('统计')
@Controller('stats')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class StatsController {
  constructor(private readonly statsService: StatsService) {}

  @Get('system')
  @Roles(UserRole.ADMIN)
  async getSystemStats() {
    return this.statsService.getSystemStats();
  }

  @Get('user')
  async getUserStats(@CurrentUser('id') userId: string) {
    return this.statsService.getUserStats(userId);
  }

  @Get('dashboard')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取用户仪表盘数据' })
  async getDashboardStats(@CurrentUser() user: User) {
    return this.statsService.getDashboardStats(user.id);
  }

  @Get('recent-learning')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取最近学习记录' })
  async getRecentLearning(@CurrentUser() user: User) {
    return this.statsService.getRecentLearning(user.id);
  }
} 