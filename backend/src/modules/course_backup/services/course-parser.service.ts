import { Injectable } from '@nestjs/common';
import { CourseContent, CourseSection, CourseStep } from '../interfaces/course.interface';

@Injectable()
export class CourseParserService {
  parseCourseContent(markdown: string): CourseContent {
    const lines = markdown.split('\n');
    const content: CourseContent = {
      title: '',
      sections: [],
    };

    let currentSection: CourseSection | null = null;
    let currentStep: CourseStep | null = null;
    let collectingPracticeContent = false;
    let practiceContent = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 跳过空行
      if (!line) continue;

      // 解析标题
      if (line.startsWith('# ')) {
        content.title = line.substring(2).trim();
        continue;
      }

      // 解析章节
      if (line.startsWith('## ')) {
        if (currentSection) {
          content.sections.push(currentSection);
        }
        currentSection = {
          title: line.substring(3).trim(),
          steps: [],
        };
        continue;
      }

      // 解析AI实战开始
      if (line.startsWith('[practice:')) {
        if (currentStep) {
          currentSection?.steps.push(currentStep);
        }

        const practiceMatch = line.match(/\[practice:(\w+)(\s+[^\]]+)?\]/);
        if (practiceMatch) {
          const [_, practiceType, attributes] = practiceMatch;
          const practiceStep: CourseStep = {
            type: 'ai_practice',
            content: '',
            practice: {
              type: practiceType as 'chat' | 'image',
              mode: 'default'
            }
          };

          // 解析可选属性
          if (attributes) {
            const attrMatches = attributes.matchAll(/(\w+)="([^"]+)"/g);
            for (const match of attrMatches) {
              const [__, key, value] = match;
              switch (key) {
                case 'mode':
                  practiceStep.practice!.mode = value;
                  break;
                case 'character':
                  practiceStep.practice!.character = value;
                  break;
                case 'temperature':
                  practiceStep.practice!.temperature = parseFloat(value);
                  break;
                case 'max_tokens':
                  practiceStep.practice!.max_tokens = parseInt(value);
                  break;
                case 'system_prompt':
                  practiceStep.practice!.system_prompt = value;
                  break;
                case 'model':
                  practiceStep.practice!.model = value;
                  break;
                case 'size':
                  practiceStep.practice!.size = value;
                  break;
                case 'style':
                  practiceStep.practice!.style = value;
                  break;
                case 'quality':
                  practiceStep.practice!.quality = value;
                  break;
              }
            }
          }

          currentStep = practiceStep;
          collectingPracticeContent = true;
          practiceContent = '';
          continue;
        }
      }

      // 解析AI实战结束
      if (line === '[/practice]' && collectingPracticeContent) {
        if (currentStep) {
          currentStep.practice!.initial_prompt = practiceContent.trim();
          currentStep.content = practiceContent.trim();
          currentSection?.steps.push(currentStep);
          currentStep = null;
        }
        collectingPracticeContent = false;
        continue;
      }

      // 收集AI实战内容
      if (collectingPracticeContent) {
        practiceContent += line + '\n';
        continue;
      }

      // 解析多媒体内容
      if (line.startsWith('[media:')) {
        if (currentStep) {
          currentSection?.steps.push(currentStep);
        }

        const mediaMatch = line.match(/\[media:(\w+)(\s+[^\]]+)?\]\s*(.+)/);
        if (mediaMatch) {
          const [_, mediaType, attributes, url] = mediaMatch;
          const mediaStep: CourseStep = {
            type: 'media',
            content: '',
            media: {
              type: mediaType as 'video' | 'image' | 'audio',
              url: url.trim(),
            }
          };

          // 解析可选属性
          if (attributes) {
            const attrMatches = attributes.matchAll(/(\w+)="([^"]+)"/g);
            for (const match of attrMatches) {
              const [__, key, value] = match;
              switch (key) {
                case 'title':
                  mediaStep.media!.title = value;
                  break;
                case 'description':
                  mediaStep.media!.description = value;
                  break;
                case 'poster':
                  mediaStep.media!.poster = value;
                  break;
                case 'duration':
                  mediaStep.media!.duration = parseInt(value);
                  break;
                case 'width':
                  mediaStep.media!.width = parseInt(value);
                  break;
                case 'height':
                  mediaStep.media!.height = parseInt(value);
                  break;
              }
            }
          }

          currentStep = mediaStep;
          currentSection?.steps.push(currentStep);
          currentStep = null;
          continue;
        }
      }

      // 解析对话或等待
      if (line.startsWith('[') && line.includes(']:')) {
        if (currentStep) {
          currentSection?.steps.push(currentStep);
        }

        const [speaker, content] = line.split(']: ');
        currentStep = {
          type: 'dialogue',
          speaker: speaker.substring(1),
          content: content.trim().replace(/^"(.+)"$/, '$1'),
        };
        continue;
      }

      // 解析等待指令
      if (line.startsWith('[等待') && line.endsWith(']')) {
        if (line.includes('输入')) {
          if (currentStep) {
            currentStep.type = 'input';
            currentStep.waitForInput = true;
            currentSection?.steps.push(currentStep);
            currentStep = null;
          }
        } else if (line.includes('点击')) {
          if (currentStep) {
            currentStep.type = 'wait';
            currentSection?.steps.push(currentStep);
            currentStep = null;
          }
        }
        continue;
      }

      // 如果是普通文本且有当前步骤，则追加到内容中
      if (currentStep) {
        currentStep.content += '\n' + line;
      }
    }

    // 添加最后的步骤和章节
    if (currentStep) {
      currentSection?.steps.push(currentStep);
    }
    if (currentSection) {
      content.sections.push(currentSection);
    }

    return content;
  }
} 