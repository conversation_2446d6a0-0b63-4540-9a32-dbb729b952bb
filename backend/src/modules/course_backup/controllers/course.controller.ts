import { Controller, Get, Param } from '@nestjs/common';
import { CourseParserService } from '../services/course-parser.service';
import * as fs from 'fs/promises';
import * as path from 'path';

@Controller('courses')
export class CourseController {
  constructor(private readonly courseParserService: CourseParserService) {}

  @Get(':lessonId')
  async getCourseContent(@Param('lessonId') lessonId: string) {
    try {
      console.log('Current working directory:', process.cwd());
      const filePath = path.join(process.cwd(), '..', 'article', `lesson_${lessonId}_script.md`);
      console.log('Attempting to read file:', filePath);
      
      const content = await fs.readFile(filePath, 'utf-8');
      console.log('File content length:', content.length);
      console.log('First 100 characters:', content.substring(0, 100));
      
      const parsedContent = this.courseParserService.parseCourseContent(content);
      console.log('Parsed content sections:', parsedContent.sections.length);
      
      return parsedContent;
    } catch (error) {
      console.error('Error details:', error);
      throw new Error(`Failed to load lesson ${lessonId}: ${error.message}`);
    }
  }
} 