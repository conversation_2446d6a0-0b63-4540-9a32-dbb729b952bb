export interface CourseContent {
  title: string;
  sections: CourseSection[];
}

export interface CourseSection {
  title: string;
  steps: CourseStep[];
}

export interface CourseStep {
  type: 'dialogue' | 'input' | 'choice' | 'practice' | 'wait' | 'media' | 'ai_practice';
  speaker?: string;
  content: string;
  options?: string[];
  correctAnswer?: string;
  waitForInput?: boolean;
  media?: {
    type: 'video' | 'image' | 'audio';
    url: string;
    title?: string;
    description?: string;
    poster?: string;  // 视频封面图
    duration?: number;  // 音视频时长
    width?: number;   // 图片/视频宽度
    height?: number;  // 图片/视频高度
  };
  practice?: {
    type: 'chat' | 'image';
    mode: string;
    character?: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
    model?: string;
    size?: string;
    style?: string;
    quality?: string;
    initial_prompt?: string;
  };
}

export interface Course {
  id: string;
  title: string;
  description: string;
  content: CourseContent;
  createdAt: Date;
  updatedAt: Date;
} 