export const ORDER_EXPIRATION_TIME = 30 * 60 * 1000; // 30 分钟过期

export const WECHAT_PAY_TRADE_STATES = {
  SUCCESS: 'SUCCESS',      // 支付成功
  REFUND: 'REFUND',       // 转入退款
  NOTPAY: 'NOTPAY',       // 未支付
  CLOSED: 'CLOSED',       // 已关闭
  REVOKED: 'REVOKED',     // 已撤销
  USERPAYING: 'USERPAYING', // 用户支付中
  PAYERROR: 'PAYERROR',   // 支付失败
} as const;

export enum PaymentMethod {
  WECHAT = 'WECHAT',
} 