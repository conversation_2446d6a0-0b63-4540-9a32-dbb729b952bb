import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Entity<PERSON>anager, <PERSON>Than, Like, Between } from 'typeorm';
import { Order, OrderStatus, OrderType } from '../entities/order.entity';
import { ORDER_EXPIRATION_TIME } from '../constants/payment.constants';
import { CreateOrderDto } from '../dto/order.dto';
import { CourseService } from '../../course/services/course.service';
import { CourseAccessService } from '../../course/services/course-access.service';

interface FindAllOrdersOptions {
  page: number;
  pageSize: number;
  status?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

// 添加状态映射
const statusMap: Record<string, OrderStatus> = {
  '待支付': OrderStatus.PENDING,
  '已支付': OrderStatus.PAID,
  '已取消': OrderStatus.CANCELLED,
  '已退款': OrderStatus.REFUNDED,
  '已过期': OrderStatus.EXPIRED,
  'PENDING': OrderStatus.PENDING,
  'PAID': OrderStatus.PAID,
  'CANCELLED': OrderStatus.CANCELLED,
  'REFUNDED': OrderStatus.REFUNDED,
  'EXPIRED': OrderStatus.EXPIRED,
};

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly courseService: CourseService,
    private readonly courseAccessService: CourseAccessService,
  ) {}

  /**
   * 生成订单号
   */
  private generateOrderNo(userId: string): string {
    if (typeof userId !== 'string') {
      this.logger.error('Invalid userId type', { userId, type: typeof userId });
      throw new Error('Invalid userId: must be a string');
    }

    const timestamp = Date.now().toString().slice(-10);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    try {
      return `${userId.toString().slice(0, 8)}${timestamp}${random}`;
    } catch (error) {
      this.logger.error('Failed to generate order number', { userId, error });
      throw new Error('Failed to generate order number');
    }
  }

  /**
   * 创建订单
   */
  async createOrder(userId: string, dto: CreateOrderDto): Promise<Order> {
    if (!userId || typeof userId !== 'string') {
      this.logger.error('Invalid userId', { userId, type: typeof userId });
      throw new Error('Invalid userId provided');
    }

    const { courseId } = dto;

    // 检查用户是否已购买该课程
    const hasPurchased = await this.courseService.hasUserPurchased(userId, courseId);
    if (hasPurchased) {
      throw new Error('User has already purchased this course');
    }

    // 获取课程信息和价格
    const course = await this.courseService.findOne(courseId);
    const orderNo = this.generateOrderNo(userId);
    const expiresAt = new Date(Date.now() + ORDER_EXPIRATION_TIME);

    const order = this.orderRepository.create({
      orderNo,
      userId,
      orderType: OrderType.COURSE,
      productName: course.title,
      amount: course.price,
      status: OrderStatus.PENDING,
      expiresAt,
      courseId,
      metadata: {
        courseId,
      },
    });

    try {
      this.logger.debug('Creating order', { userId, courseId, orderNo });
      const savedOrder = await this.orderRepository.save(order);
      this.logger.debug('Order created', { orderId: savedOrder.id });
      return savedOrder;
    } catch (error) {
      this.logger.error('Failed to create order', {
        error: error instanceof Error ? error.message : error,
        userId,
        courseId,
      });
      throw error;
    }
  }

  /**
   * 处理支付成功
   */
  async handlePaymentSuccess(orderNo: string, transactionId: string, entityManager?: EntityManager): Promise<void> {
    this.logger.debug('Handling payment success', { orderNo, transactionId });
    
    // 如果没有传入 entityManager，创建一个新的事务
    if (!entityManager) {
      return this.orderRepository.manager.transaction(async (transactionalEntityManager) => {
        await this.handlePaymentSuccess(orderNo, transactionId, transactionalEntityManager);
      });
    }

    const order = await entityManager.findOne(Order, {
      where: { orderNo },
      lock: { mode: 'pessimistic_write' },
    });

    if (!order) {
      this.logger.error('Order not found', { orderNo });
      throw new NotFoundException(`Order not found: ${orderNo}`);
    }

    this.logger.debug('Found order', {
      orderNo,
      status: order.status,
      userId: order.userId,
      metadata: order.metadata
    });

    if (order.status === OrderStatus.PAID) {
      this.logger.warn('Order already paid', { orderNo });
      return;
    }

    try {
      // 更新订单状态
      order.status = OrderStatus.PAID;
      order.transactionId = transactionId;
      order.paidAt = new Date();
      await entityManager.save(order);
      this.logger.debug('Updated order status to PAID', { orderNo });

      // 检查metadata中是否有courseId
      if (!order.metadata || !order.metadata.courseId) {
        this.logger.error('Order metadata missing courseId', { orderNo, metadata: order.metadata });
        throw new Error('Order metadata missing courseId');
      }

      // 授予用户课程访问权限
      this.logger.debug('Granting course access', {
        userId: order.userId,
        courseId: order.metadata.courseId
      });

      await this.courseAccessService.grantCourseAccess(
        order.userId,
        order.metadata.courseId,
        'purchase'
      );

      this.logger.debug('Successfully granted course access', {
        userId: order.userId,
        courseId: order.metadata.courseId
      });
    } catch (error) {
      this.logger.error('Failed to handle payment success', {
        error: error instanceof Error ? error.message : error,
        orderNo,
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * 查询订单
   */
  async findOrderByNo(orderNo: string): Promise<Order | null> {
    return this.orderRepository.findOne({
      where: { orderNo },
      relations: ['user', 'course', 'payment', 'refund'],
    });
  }

  /**
   * 查询用户订单列表
   */
  async findUserOrders(userId: string, page = 1, limit = 10): Promise<{
    items: Order[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const [items, total] = await this.orderRepository.findAndCount({
        where: { userId },
        order: { createdAt: 'DESC' },
        skip: (page - 1) * limit,
        take: limit,
      });

      return {
        items,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Failed to find user orders', {
        error: error instanceof Error ? error.message : error,
        userId,
      });
      throw error;
    }
  }

  /**
   * 关闭过期订单
   */
  async closeExpiredOrders(): Promise<void> {
    try {
      await this.orderRepository.update(
        {
          status: OrderStatus.PENDING,
          expiresAt: MoreThan(new Date()),
        },
        {
          status: OrderStatus.EXPIRED,
        },
      );
    } catch (error) {
      this.logger.error('Failed to close expired orders', {
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async findOrdersByUserId(userId: string) {
    return this.orderRepository.find({
      where: { userId },
      relations: ['course'],
      order: { createdAt: 'DESC' },
    });
  }

  async findAllOrders(options: FindAllOrdersOptions) {
    const { page, pageSize, status, startDate, endDate, search } = options;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.user', 'user')
      .leftJoinAndSelect('order.course', 'course')
      .orderBy('order.createdAt', 'DESC');

    if (status) {
      // 将中文状态转换为英文状态
      const mappedStatus = statusMap[status];
      if (!mappedStatus) {
        throw new Error(`Invalid status: ${status}`);
      }
      queryBuilder.andWhere('order.status = :status', { status: mappedStatus });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (search) {
      queryBuilder.andWhere(
        '(order.orderNo LIKE :search OR user.username LIKE :search OR course.title LIKE :search)',
        { search: `%${search}%` }
      );
    }

    const [items, total] = await queryBuilder
      .skip(skip)
      .take(pageSize)
      .getManyAndCount();

    return {
      items,
      total,
      page,
      pageSize,
    };
  }

  async updateOrderStatus(orderNo: string, status: string, reason?: string) {
    const order = await this.orderRepository.findOne({
      where: { orderNo },
      relations: ['user', 'course'],
    });

    if (!order) {
      throw new NotFoundException(`Order not found: ${orderNo}`);
    }

    // 将状态值转换为 OrderStatus 枚举
    const mappedStatus = statusMap[status];
    if (!mappedStatus) {
      throw new Error(`Invalid order status: ${status}`);
    }

    // 更新订单状态
    order.status = mappedStatus;
    if (reason) {
      order.metadata = {
        ...order.metadata,
        statusChangeReason: reason,
      };
    }

    await this.orderRepository.save(order);

    return order;
  }
}