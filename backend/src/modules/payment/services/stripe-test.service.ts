import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

export interface StripeTestResult {
  success: boolean;
  error?: string;
  details?: any;
}

@Injectable()
export class StripeTestService {
  private readonly logger = new Logger(StripeTestService.name);

  constructor(private readonly configService: ConfigService) {}

  async testStripeConnection(): Promise<StripeTestResult> {
    const apiKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    
    // 1. 检查 API Key 是否存在
    if (!apiKey) {
      return {
        success: false,
        error: 'STRIPE_SECRET_KEY not found in environment variables',
      };
    }

    // 2. 检查 API Key 格式
    if (!apiKey.startsWith('sk_')) {
      return {
        success: false,
        error: 'Invalid Stripe API key format (should start with sk_)',
      };
    }

    try {
      // 3. 初始化 Stripe 客户端
      const stripe = new Stripe(apiKey, {
        apiVersion: '2025-06-30.basil' as any,
      });

      // 4. 测试基本连接 - 获取账户信息
      const account = await stripe.accounts.retrieve();
      
      // 5. 测试客户创建
      const testCustomer = await stripe.customers.create({
        email: '<EMAIL>',
        name: 'Test Customer',
        metadata: {
          test: 'true',
          timestamp: Date.now().toString(),
        },
      });

      // 6. 清理测试数据
      await stripe.customers.del(testCustomer.id);

      return {
        success: true,
        details: {
          accountId: account.id,
          accountType: account.type,
          country: account.country,
          currency: account.default_currency,
          testMode: apiKey.includes('test'),
          customerCreated: testCustomer.id,
        },
      };

    } catch (error) {
      this.logger.error('Stripe connection test failed', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          errorType: error.constructor.name,
          stack: error instanceof Error ? error.stack : undefined,
        },
      };
    }
  }

  async testCustomerOperations(email: string = '<EMAIL>'): Promise<StripeTestResult> {
    const apiKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    
    if (!apiKey) {
      return {
        success: false,
        error: 'STRIPE_SECRET_KEY not configured',
      };
    }

    try {
      const stripe = new Stripe(apiKey, {
        apiVersion: '2025-06-30.basil' as any,
      });

      // 测试客户操作流程
      const operations = [];

      // 1. 创建客户
      const customer = await stripe.customers.create({
        email,
        name: 'Test Customer',
        metadata: { test: 'true' },
      });
      operations.push(`Created customer: ${customer.id}`);

      // 2. 查询客户
      const retrievedCustomer = await stripe.customers.retrieve(customer.id);
      operations.push(`Retrieved customer: ${retrievedCustomer.id}`);

      // 3. 更新客户
      const updatedCustomer = await stripe.customers.update(customer.id, {
        name: 'Updated Test Customer',
      });
      operations.push(`Updated customer: ${updatedCustomer.id}`);

      // 4. 列出客户
      const customerList = await stripe.customers.list({
        email,
        limit: 1,
      });
      operations.push(`Found ${customerList.data.length} customers with email ${email}`);

      // 5. 删除客户
      await stripe.customers.del(customer.id);
      operations.push(`Deleted customer: ${customer.id}`);

      return {
        success: true,
        details: {
          operations,
          customerId: customer.id,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          errorType: error.constructor.name,
        },
      };
    }
  }
}
