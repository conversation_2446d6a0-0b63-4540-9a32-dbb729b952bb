import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { OrderService } from './order.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { SubscriptionStatus } from '../../subscription/entities/subscription.entity';

@Injectable()
export class StripeService {
  private stripe: Stripe | null = null;
  private readonly logger = new Logger(StripeService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly orderService: OrderService,
    private readonly subscriptionService: SubscriptionService,
  ) {
    const apiKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!apiKey) {
      this.logger.warn('STRIPE_SECRET_KEY is not defined in environment variables, Stripe payment will be mocked');
    } else {
      try {
        // @ts-ignore: Stripe SDK version mismatch workaround
        this.stripe = new Stripe(api<PERSON><PERSON>, {
          apiVersion: '2025-06-30.basil' as any,
        });
      } catch (error) {
        this.logger.error('Failed to initialize Stripe', {
          error: error instanceof Error ? error.message : error,
        });
      }
    }
  }

  /**
   * 创建支付意向
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'usd',
    metadata: Record<string, string> = {},
  ): Promise<Stripe.PaymentIntent | { id: string; client_secret: string }> {
    // 如果Stripe未初始化，抛出错误而不是返回模拟的支付意向
    if (!this.stripe) {
      this.logger.error('Stripe not initialized');
      throw new Error('Stripe payment service is not configured');
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount), // 直接使用美元金额，不进行额外转换
        currency,
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      this.logger.debug('Payment intent created', {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
      });

      return paymentIntent;
    } catch (error) {
      this.logger.error('Failed to create payment intent', {
        error: error instanceof Error ? error.message : error,
        amount,
        currency,
      });
      throw error;
    }
  }

  /**
   * 创建模拟的支付意向
   */
  private createMockPaymentIntent(amount: number, metadata: Record<string, string> = {}): { id: string; client_secret: string } {
    const id = `pi_mock_${Date.now()}`;
    const clientSecret = `pi_mock_secret_${Date.now()}_secret_${Math.random().toString(36).substring(2, 15)}`;

    this.logger.debug('Created mock payment intent', { id, amount, metadata });

    return {
      id,
      client_secret: clientSecret,
    };
  }

  /**
   * 查询支付意向
   */
  async retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent | { id: string; client_secret: string }> {
    // 如果Stripe未初始化，返回模拟的支付意向
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, returning mock payment intent');
      return this.createMockPaymentIntent(0, { id: paymentIntentId });
    }

    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      this.logger.error('Failed to retrieve payment intent', {
        error: error instanceof Error ? error.message : error,
        paymentIntentId,
      });
      // 如果发生错误，返回模拟的支付意向
      return this.createMockPaymentIntent(0, { id: paymentIntentId });
    }
  }

  /**
   * 处理Stripe Webhook事件
   */
  async handleWebhookEvent(
    signature: string,
    payload: Buffer | undefined,
  ): Promise<{ received: boolean }> {
    // 如果Stripe未初始化，返回模拟的响应
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, mocking webhook response');
      return { received: true };
    }

    const webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');

    if (!webhookSecret) {
      this.logger.error('STRIPE_WEBHOOK_SECRET is not defined in environment variables');
      return { received: false };
    }

    if (!payload) {
      this.logger.error('Webhook payload is undefined');
      return { received: false };
    }

    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );

      this.logger.debug('Webhook event received', {
        type: event.type,
        id: event.id,
      });

      // 处理不同类型的事件
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        case 'charge.updated':
          const charge = event.data.object as Stripe.Charge;
          if (charge.payment_intent) {
            const paymentIntent = await this.stripe.paymentIntents.retrieve(charge.payment_intent as string);
            if (paymentIntent.status === 'succeeded') {
              await this.handlePaymentIntentSucceeded(paymentIntent);
            }
          }
          break;
        // 订阅相关事件
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
        case 'invoice.payment_succeeded':
        case 'invoice.payment_failed':
          await this.handleSubscriptionWebhook(event);
          break;
        default:
          this.logger.debug(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      this.logger.error('Error handling webhook event', {
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  /**
   * 处理支付成功事件
   */
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    this.logger.debug('Payment intent succeeded', {
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      metadata: paymentIntent.metadata,
    });

    // 更新订单状态为已支付
    if (paymentIntent.metadata.orderNo) {
      try {
        await this.orderService.handlePaymentSuccess(
          paymentIntent.metadata.orderNo,
          paymentIntent.id
        );
        this.logger.debug('Order status updated to PAID', {
          orderNo: paymentIntent.metadata.orderNo,
          paymentIntentId: paymentIntent.id
        });
      } catch (error) {
        this.logger.error('Failed to update order status', {
          error: error instanceof Error ? error.message : error,
          orderNo: paymentIntent.metadata.orderNo,
          paymentIntentId: paymentIntent.id
        });
      }
    }
  }

  /**
   * 处理支付失败事件
   */
  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    this.logger.debug('Payment intent failed', {
      id: paymentIntent.id,
      amount: paymentIntent.amount,
      metadata: paymentIntent.metadata,
      error: paymentIntent.last_payment_error,
    });

    // 这里可以添加处理支付失败的逻辑
    // 例如：记录失败原因，通知用户等
  }

  /**
   * 创建或获取Stripe客户
   */
  async createOrGetCustomer(userId: string, email: string, name?: string): Promise<Stripe.Customer | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot create customer');
      return null;
    }

    try {
      // 查找现有客户
      const existingCustomers = await this.stripe.customers.list({
        email,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        const customer = existingCustomers.data[0];
        this.logger.debug('Found existing Stripe customer', {
          customerId: customer.id,
          email,
          userId,
        });
        return customer;
      }

      // 创建新客户
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata: {
          userId,
        },
      });

      this.logger.debug('Created new Stripe customer', {
        customerId: customer.id,
        email,
        userId,
      });

      return customer;
    } catch (error) {
      this.logger.error('Failed to create or get Stripe customer', {
        error: error instanceof Error ? error.message : error,
        userId,
        email,
      });
      return null;
    }
  }

  /**
   * 创建 Setup Intent 用于收集支付方式
   */
  async createSetupIntent(
    customerId: string,
    metadata?: Record<string, string>
  ): Promise<Stripe.SetupIntent | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot create setup intent');
      return null;
    }

    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
        usage: 'off_session',
        metadata,
      });

      this.logger.debug('Created Setup Intent', {
        setupIntentId: setupIntent.id,
        customerId,
        status: setupIntent.status,
      });

      return setupIntent;
    } catch (error) {
      this.logger.error('Failed to create Setup Intent', {
        error: error instanceof Error ? error.message : error,
        customerId,
      });
      return null;
    }
  }

  /**
   * 创建Stripe订阅
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    trialDays?: number,
    metadata?: Record<string, string>
  ): Promise<Stripe.Subscription | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot create subscription');
      return null;
    }

    try {
      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        items: [
          {
            price: priceId,
          },
        ],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
          payment_method_types: ['card'],
        },
        expand: ['latest_invoice.payment_intent'],
        metadata,
      };

      // 如果有试用期，添加试用期设置
      if (trialDays && trialDays > 0) {
        subscriptionData.trial_period_days = trialDays;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionData);

      this.logger.debug('Created Stripe subscription', {
        subscriptionId: subscription.id,
        customerId,
        priceId,
        trialDays,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      this.logger.error('Failed to create Stripe subscription', {
        error: error instanceof Error ? error.message : error,
        customerId,
        priceId,
        trialDays,
      });
      return null;
    }
  }

  /**
   * 取消Stripe订阅
   */
  async cancelSubscription(subscriptionId: string, atPeriodEnd: boolean = false): Promise<Stripe.Subscription | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot cancel subscription');
      return null;
    }

    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: atPeriodEnd,
      });

      // 如果需要立即取消
      if (!atPeriodEnd) {
        const canceledSubscription = await this.stripe.subscriptions.cancel(subscriptionId);
        
        this.logger.debug('Canceled Stripe subscription immediately', {
          subscriptionId,
          status: canceledSubscription.status,
        });
        
        return canceledSubscription;
      }

      this.logger.debug('Scheduled Stripe subscription cancellation', {
        subscriptionId,
        cancelAtPeriodEnd: atPeriodEnd,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      this.logger.error('Failed to cancel Stripe subscription', {
        error: error instanceof Error ? error.message : error,
        subscriptionId,
        atPeriodEnd,
      });
      return null;
    }
  }

  /**
   * 重新激活Stripe订阅
   */
  async reactivateSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot reactivate subscription');
      return null;
    }

    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
      });

      this.logger.debug('Reactivated Stripe subscription', {
        subscriptionId,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      this.logger.error('Failed to reactivate Stripe subscription', {
        error: error instanceof Error ? error.message : error,
        subscriptionId,
      });
      return null;
    }
  }

  /**
   * 获取Stripe订阅信息
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
    if (!this.stripe) {
      this.logger.warn('Stripe not initialized, cannot get subscription');
      return null;
    }

    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      
      this.logger.debug('Retrieved Stripe subscription', {
        subscriptionId,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      this.logger.error('Failed to get Stripe subscription', {
        error: error instanceof Error ? error.message : error,
        subscriptionId,
      });
      return null;
    }
  }

  /**
   * 处理订阅相关的webhook事件
   */
  private async handleSubscriptionWebhook(event: Stripe.Event): Promise<void> {
    const subscription = event.data.object as Stripe.Subscription;
    
    this.logger.debug('Handling subscription webhook', {
      type: event.type,
      subscriptionId: subscription.id,
      status: subscription.status,
    });

    // 根据订阅ID查找对应的本地订阅记录
    const localSubscription = await this.subscriptionService.getSubscriptionByStripeId(subscription.id);
    
    if (!localSubscription) {
      this.logger.warn('No local subscription found for Stripe subscription', {
        stripeSubscriptionId: subscription.id,
      });
      return;
    }

    // 处理不同的订阅事件
    switch (event.type) {
      case 'customer.subscription.created':
        await this.handleSubscriptionCreated(subscription, localSubscription);
        break;
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(subscription, localSubscription);
        break;
      case 'customer.subscription.deleted':
        await this.handleSubscriptionDeleted(subscription, localSubscription);
        break;
      case 'invoice.payment_succeeded':
        const invoice = event.data.object as any;
        if (invoice.subscription && typeof invoice.subscription === 'string') {
          const invoiceSubscription = await this.stripe!.subscriptions.retrieve(invoice.subscription);
          await this.handleSubscriptionPaymentSucceeded(invoiceSubscription, localSubscription);
        }
        break;
      case 'invoice.payment_failed':
        const failedInvoice = event.data.object as any;
        if (failedInvoice.subscription && typeof failedInvoice.subscription === 'string') {
          const failedSubscription = await this.stripe!.subscriptions.retrieve(failedInvoice.subscription);
          await this.handleSubscriptionPaymentFailed(failedSubscription, localSubscription);
        }
        break;
    }
  }

  /**
   * 处理订阅创建事件
   */
  private async handleSubscriptionCreated(stripeSubscription: Stripe.Subscription, localSubscription: any): Promise<void> {
    this.logger.debug('Subscription created', {
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
    });

    // 更新本地订阅状态
    await this.subscriptionService.updateSubscriptionStatus(
      localSubscription.id,
      stripeSubscription.status === 'active' ? SubscriptionStatus.ACTIVE : SubscriptionStatus.INACTIVE
    );
  }

  /**
   * 处理订阅更新事件
   */
  private async handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription, localSubscription: any): Promise<void> {
    this.logger.debug('Subscription updated', {
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
    });

    // 更新本地订阅状态
    await this.subscriptionService.updateSubscriptionStatus(
      localSubscription.id,
      stripeSubscription.status === 'active' ? SubscriptionStatus.ACTIVE : SubscriptionStatus.INACTIVE
    );
  }

  /**
   * 处理订阅删除事件
   */
  private async handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription, localSubscription: any): Promise<void> {
    this.logger.debug('Subscription deleted', {
      stripeSubscriptionId: stripeSubscription.id,
    });

    // 更新本地订阅状态为取消
    await this.subscriptionService.updateSubscriptionStatus(localSubscription.id, SubscriptionStatus.CANCELLED);
  }

  /**
   * 处理订阅支付成功事件
   */
  private async handleSubscriptionPaymentSucceeded(stripeSubscription: Stripe.Subscription, localSubscription: any): Promise<void> {
    this.logger.debug('Subscription payment succeeded', {
      stripeSubscriptionId: stripeSubscription.id,
    });

    // 更新本地订阅状态为激活
    await this.subscriptionService.updateSubscriptionStatus(localSubscription.id, SubscriptionStatus.ACTIVE);
  }

  /**
   * 处理订阅支付失败事件
   */
  private async handleSubscriptionPaymentFailed(stripeSubscription: Stripe.Subscription, localSubscription: any): Promise<void> {
    this.logger.debug('Subscription payment failed', {
      stripeSubscriptionId: stripeSubscription.id,
    });

    // 这里可以添加处理订阅支付失败的逻辑
    // 例如：发送通知、暂停服务等
  }
}
