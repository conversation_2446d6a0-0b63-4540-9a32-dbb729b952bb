import { Controller, Get, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { StripeTestService, StripeTestResult } from '../services/stripe-test.service';

@ApiTags('stripe-test')
@Controller('stripe-test')
export class StripeTestController {
  private readonly logger = new Logger(StripeTestController.name);

  constructor(private readonly stripeTestService: StripeTestService) {
    this.logger.log('StripeTestController initialized');
  }

  @Get('connection')
  @ApiOperation({ summary: '测试 Stripe 连接' })
  @ApiResponse({ status: 200, description: '连接测试结果' })
  async testConnection(): Promise<StripeTestResult> {
    this.logger.log('Testing Stripe connection...');
    return this.stripeTestService.testStripeConnection();
  }

  @Get('customer-operations')
  @ApiOperation({ summary: '测试 Stripe 客户操作' })
  @ApiResponse({ status: 200, description: '客户操作测试结果' })
  async testCustomerOperations(
    @Query('email') email?: string,
  ): Promise<StripeTestResult> {
    return this.stripeTestService.testCustomerOperations(email);
  }

  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  async health() {
    this.logger.log('Health check called');
    return { status: 'ok', controller: 'StripeTestController', timestamp: new Date().toISOString() };
  }
}
