import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Param,
  UseGuards,
  NotFoundException,
  Headers,
  Req,
  RawBodyRequest,
  Logger,
  ForbiddenException,
} from '@nestjs/common';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { OrderService } from '../services/order.service';
import { StripeService } from '../services/stripe.service';
import { CreateOrderDto, OrderQueryDto } from '../dto/order.dto';
import { User } from '@/modules/user/entities/user.entity';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { SubscriptionPlanService } from '../../subscription/services/subscription-plan.service';
import { CreateSubscriptionDto } from '../../subscription/dto/subscription.dto';

@ApiTags('支付')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly orderService: OrderService,
    private readonly stripeService: StripeService,
    private readonly subscriptionService: SubscriptionService,
    private readonly subscriptionPlanService: SubscriptionPlanService,
  ) {}

  @Post('orders')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '创建订单' })
  @ApiResponse({ status: 201, description: '订单创建成功' })
  async createOrder(
    @CurrentUser('id') userId: string,
    @Body() dto: CreateOrderDto,
  ) {
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid user ID');
    }

    const order = await this.orderService.createOrder(userId, dto);

    return {
      orderId: order.id,
      orderNo: order.orderNo,
    };
  }

  @Get('orders')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取用户订单列表' })
  async getOrders(@CurrentUser() user: User) {
    const orders = await this.orderService.findOrdersByUserId(user.id);
    return orders;
  }

  @Get('orders/:orderNo')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取订单详情' })
  @ApiResponse({ status: 200, description: '订单详情' })
  async getUserOrder(
    @CurrentUser() user: User,
    @Param('orderNo') orderNo: string,
  ) {
    const order = await this.orderService.findOrderByNo(orderNo);
    if (!order) {
      throw new NotFoundException(`Order not found: ${orderNo}`);
    }
    
    // 检查订单是否属于当前用户
    if (order.userId !== user.id) {
      throw new ForbiddenException('You do not have permission to view this order');
    }
    
    return order;
  }

  @Get('create-payment-intent/:orderNo')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '创建 Stripe 支付意向' })
  @ApiResponse({ status: 200, description: '返回客户端密钥' })
  async createPaymentIntent(@Param('orderNo') orderNo: string) {
    // 查询订单
    const order = await this.orderService.findOrderByNo(orderNo);
    if (!order) {
      throw new NotFoundException(`Order not found: ${orderNo}`);
    }

    // 创建 Stripe 支付意向
    const paymentIntent = await this.stripeService.createPaymentIntent(
      Math.round(Number(order.amount) * 100),
      'usd',
      { orderNo: order.orderNo }
    );

    return {
      clientSecret: paymentIntent.client_secret,
    };
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Stripe Webhook 回调' })
  @ApiResponse({ status: 200, description: '处理 Stripe 事件' })
  async handleStripeWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: RawBodyRequest<Request>,
  ) {
    try {
      this.logger.debug('Received Stripe webhook', { signature });
      if (!request.rawBody) {
        this.logger.error('Webhook payload is undefined');
        return { received: false, error: 'Webhook payload is undefined' };
      }
      return this.stripeService.handleWebhookEvent(signature, request.rawBody);
    } catch (error) {
      this.logger.error('Error handling Stripe webhook', {
        error: error instanceof Error ? error.message : error,
      });
      return { received: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  @Post('mock/success')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '模拟支付成功' })
  @ApiResponse({ status: 200, description: '支付成功' })
  async mockPaymentSuccess(@Body('outTradeNo') outTradeNo: string) {
    this.logger.debug('Received mock payment success', { outTradeNo });
    try {
      await this.orderService.handlePaymentSuccess(outTradeNo, 'MOCK_TRANSACTION');
      this.logger.debug('Successfully handled mock payment', { outTradeNo });
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to handle mock payment', {
        error: error instanceof Error ? error.message : error,
        outTradeNo,
      });
      throw error;
    }
  }

  @Get('admin/orders')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: '获取订单列表' })
  @ApiResponse({ status: 200, description: '获取订单列表成功' })
  async getAllOrders(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('search') search?: string,
  ) {
    const { items, total } = await this.orderService.findAllOrders({
      page,
      pageSize,
      status,
      startDate,
      endDate,
      search,
    });

    return {
      items,
      total,
      page,
      pageSize,
    };
  }

  @Get('admin/orders/:orderNo')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: '管理员获取订单详情' })
  @ApiResponse({ status: 200, description: '订单详情' })
  async getAdminOrder(@Param('orderNo') orderNo: string) {
    const order = await this.orderService.findOrderByNo(orderNo);
    if (!order) {
      throw new NotFoundException(`Order not found: ${orderNo}`);
    }
    return order;
  }

  @Post('admin/orders/:orderNo/status')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: '修改订单状态' })
  @ApiResponse({ status: 200, description: '订单状态修改成功' })
  async updateOrderStatus(
    @Param('orderNo') orderNo: string,
    @Body('status') status: string,
    @Body('reason') reason?: string,
  ) {
    const order = await this.orderService.updateOrderStatus(orderNo, status, reason);
    return order;
  }

  @Post('subscriptions/create')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '创建订阅支付' })
  @ApiResponse({ status: 201, description: '订阅支付创建成功' })
  async createSubscriptionPayment(
    @CurrentUser() user: User,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ) {
    try {
      const { planId } = createSubscriptionDto;

      // 获取订阅计划
      const plan = await this.subscriptionPlanService.getPlanById(planId);
      if (!plan.stripePriceId) {
        throw new Error('Subscription plan does not have Stripe price ID configured. Please contact administrator to configure the Price ID in Stripe Dashboard.');
      }

      // 验证 Price ID 格式
      if (!plan.stripePriceId.startsWith('price_')) {
        throw new Error(`Invalid Stripe Price ID format: ${plan.stripePriceId}. Price ID must start with "price_", not "prod_". Please update the configuration.`);
      }

      // 创建或获取Stripe客户
      const customer = await this.stripeService.createOrGetCustomer(
        user.id,
        user.email,
        user.name
      );

      if (!customer) {
        throw new Error('Failed to create Stripe customer');
      }

      // 创建本地订阅记录
      const subscription = await this.subscriptionService.createSubscriptionFromPlan(
        user.id,
        createSubscriptionDto
      );

      // 创建Stripe订阅
      const stripeSubscription = await this.stripeService.createSubscription(
        customer.id,
        plan.stripePriceId,
        plan.trialDays,
        {
          subscriptionId: subscription.id,
          userId: user.id,
        }
      );

      if (!stripeSubscription) {
        throw new Error('Failed to create Stripe subscription');
      }

      // 更新本地订阅记录的Stripe ID
      await this.subscriptionService.updateSubscription(subscription.id, {
        stripeSubscriptionId: stripeSubscription.id,
      });

      const invoice = stripeSubscription.latest_invoice as any;
      const paymentIntent = invoice?.payment_intent;

      // 检查是否为试用期订阅
      if (stripeSubscription.status === 'trialing') {
        // 试用期订阅需要创建 Setup Intent 来收集支付方式
        const setupIntent = await this.stripeService.createSetupIntent(
          customer.id,
          {
            subscriptionId: subscription.id,
            userId: user.id,
          }
        );

        if (!setupIntent?.client_secret) {
          throw new Error('Failed to create setup intent for trial subscription');
        }

        return {
          subscriptionId: subscription.id,
          stripeSubscriptionId: stripeSubscription.id,
          clientSecret: setupIntent.client_secret,
          status: stripeSubscription.status,
          requiresPayment: true,
          isTrialSubscription: true,
          isSetupIntent: true,
          message: 'Please set up your payment method for trial subscription',
        };
      }

      // 非试用期订阅需要支付确认
      if (!paymentIntent?.client_secret) {
        throw new Error('Payment intent client secret is required for non-trial subscriptions');
      }

      return {
        subscriptionId: subscription.id,
        stripeSubscriptionId: stripeSubscription.id,
        clientSecret: paymentIntent.client_secret,
        status: stripeSubscription.status,
        requiresPayment: true,
      };
    } catch (error) {
      this.logger.error('Failed to create subscription payment', {
        error: error instanceof Error ? error.message : error,
        userId: user.id,
        planId: createSubscriptionDto.planId,
      });
      throw error;
    }
  }

  @Post('subscriptions/:subscriptionId/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '取消订阅' })
  @ApiResponse({ status: 200, description: '订阅取消成功' })
  async cancelSubscription(
    @CurrentUser() user: User,
    @Param('subscriptionId') subscriptionId: string,
    @Body('immediate') immediate: boolean = false,
  ) {
    try {
      // 获取本地订阅记录
      const subscription = await this.subscriptionService.getCurrentSubscription(user.id);
      if (!subscription || subscription.id !== subscriptionId) {
        throw new ForbiddenException('Subscription not found or access denied');
      }

      // 取消Stripe订阅
      if (subscription.stripeSubscriptionId) {
        await this.stripeService.cancelSubscription(
          subscription.stripeSubscriptionId,
          !immediate
        );
      }

      // 更新本地订阅状态
      const updatedSubscription = await this.subscriptionService.cancelSubscription(
        user.id,
        { immediate }
      );

      return {
        success: true,
        subscription: updatedSubscription,
      };
    } catch (error) {
      this.logger.error('Failed to cancel subscription', {
        error: error instanceof Error ? error.message : error,
        userId: user.id,
        subscriptionId,
      });
      throw error;
    }
  }

  @Post('subscriptions/:subscriptionId/reactivate')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '重新激活订阅' })
  @ApiResponse({ status: 200, description: '订阅重新激活成功' })
  async reactivateSubscription(
    @CurrentUser() user: User,
    @Param('subscriptionId') subscriptionId: string,
  ) {
    try {
      // 获取本地订阅记录
      const subscription = await this.subscriptionService.getCurrentSubscription(user.id);
      if (!subscription || subscription.id !== subscriptionId) {
        throw new ForbiddenException('Subscription not found or access denied');
      }

      // 重新激活Stripe订阅
      if (subscription.stripeSubscriptionId) {
        await this.stripeService.reactivateSubscription(subscription.stripeSubscriptionId);
      }

      // 更新本地订阅状态
      const updatedSubscription = await this.subscriptionService.reactivateSubscription(user.id);

      return {
        success: true,
        subscription: updatedSubscription,
      };
    } catch (error) {
      this.logger.error('Failed to reactivate subscription', {
        error: error instanceof Error ? error.message : error,
        userId: user.id,
        subscriptionId,
      });
      throw error;
    }
  }
}