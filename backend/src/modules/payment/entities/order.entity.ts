import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToOne } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Course } from '../../course/entities/course.entity';
import { Payment } from './payment.entity';
import { Refund } from './refund.entity';
import { Subscription } from '../../subscription/entities/subscription.entity';

export enum OrderStatus {
  PENDING = 'pending',     // 待支付
  PAID = 'paid',          // 已支付
  CANCELLED = 'cancelled', // 已取消
  REFUNDED = 'refunded',  // 已退款
  EXPIRED = 'expired',    // 已过期
}

export enum OrderType {
  COURSE = 'course',        // 课程购买
  SUBSCRIPTION = 'subscription',  // 订阅购买
}

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'order_no', length: 32, unique: true })
  orderNo: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'order_type', type: 'enum', enum: OrderType })
  orderType: OrderType;

  @Column({ name: 'product_name' })
  productName: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ name: 'payment_method', nullable: true })
  paymentMethod: string;

  @Column({ name: 'transaction_id', nullable: true })
  transactionId: string;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING
  })
  status: OrderStatus;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ name: 'paid_at', type: 'timestamp', nullable: true })
  paidAt: Date;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ name: 'course_id', nullable: true })
  courseId: string;

  @ManyToOne(() => Course)
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @Column({ name: 'subscription_id', nullable: true })
  subscriptionId: string;

  @ManyToOne(() => Subscription, subscription => subscription.orders)
  @JoinColumn({ name: 'subscription_id' })
  subscription: Subscription;

  @OneToOne(() => Payment, payment => payment.order)
  payment: Payment;

  @OneToOne(() => Refund, refund => refund.order)
  refund: Refund;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 