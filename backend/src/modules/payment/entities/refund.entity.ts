import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn } from 'typeorm';
import { Order } from './order.entity';

export enum RefundStatus {
  PENDING = 'pending',     // 待处理
  SUCCESS = 'success',     // 退款成功
  FAILED = 'failed',       // 退款失败
}

@Entity('refunds')
export class Refund {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'order_id' })
  orderId: string;

  @OneToOne(() => Order, order => order.refund)
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @Column({ name: 'refund_no', length: 32, unique: true })
  refundNo: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: RefundStatus,
    default: RefundStatus.PENDING
  })
  status: RefundStatus;

  @Column({ name: 'reason', nullable: true })
  reason: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 