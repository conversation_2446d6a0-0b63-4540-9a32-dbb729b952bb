import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { Order } from './entities/order.entity';
import { OrderService } from './services/order.service';
import { StripeService } from './services/stripe.service';
import { StripeTestService } from './services/stripe-test.service';
import { PaymentController } from './controllers/payment.controller';
import { StripeTestController } from './controllers/stripe-test.controller';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order]),
    ConfigModule,
    SubscriptionModule,
  ],
  controllers: [PaymentController, StripeTestController],
  providers: [OrderService, StripeService, StripeTestService],
  exports: [OrderService, StripeService, StripeTestService],
})
export class PaymentModule {}
