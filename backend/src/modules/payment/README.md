# 支付模块

本模块实现了微信支付的原生接口，支持以下功能：

- Native 支付（二维码支付）
- 订单管理
- 支付结果通知处理
- 订单查询
- 订单关闭

## 配置说明

在 `.env` 文件中需要配置以下环境变量：

```env
# 微信支付配置
WECHAT_PAY_APP_ID=your_app_id
WECHAT_PAY_MCH_ID=your_merchant_id
WECHAT_PAY_API_V3_KEY=your_api_v3_key
WECHAT_PAY_SERIAL_NO=your_serial_number
WECHAT_PAY_PRIVATE_KEY_PATH=certs/apiclient_key.pem
WECHAT_PAY_CERT_PATH=certs/apiclient_cert.pem
WECHAT_NOTIFY_URL=https://your.domain/api/payment/notify
```

## 证书说明

需要在 `backend/certs` 目录下放置以下证书文件：

- `apiclient_key.pem`: 商户API私钥
- `apiclient_cert.pem`: 商户API证书

## 使用方法

### 1. 创建支付订单

```typescript
@Post('orders')
@UseGuards(JwtAuthGuard)
async createOrder(
  @CurrentUser('id') userId: string,
  @Body() dto: CreateOrderDto,
) {
  const order = await this.orderService.createOrder(userId, dto);
  const payment = await this.wechatPayService.createPayment({
    description: order.productName,
    outTradeNo: order.orderNo,
    amount: order.amount,
  });

  return {
    orderId: order.id,
    orderNo: order.orderNo,
    payment,
  };
}
```

### 2. 查询订单状态

```typescript
const result = await this.wechatPayService.queryOrder(orderNo);
if (result.trade_state === WECHAT_PAY_TRADE_STATES.SUCCESS) {
  // 处理支付成功逻辑
}
```

### 3. 关闭订单

```typescript
await this.wechatPayService.closeOrder(orderNo);
```

### 4. 处理支付通知

```typescript
@Post('notify')
async handleNotify(
  @Headers() headers: Record<string, string>,
  @Req() request: RawBodyRequest<Request>,
) {
  const body = request.rawBody?.toString() || '';
  return this.wechatPayService.handlePaymentNotification(headers, body);
}
```

## 支付流程

1. 前端调用创建订单接口
2. 后端创建订单记录并调用微信支付下单接口
3. 前端获取支付二维码并展示
4. 用户扫码支付
5. 微信支付通知支付结果
6. 后端处理支付结果，更新订单状态和会员信息

## 错误处理

所有接口都包含了错误处理和日志记录，主要的错误类型：

- 配置错误：缺少必要的配置信息
- 签名错误：请求签名验证失败
- 订单错误：订单不存在或状态异常
- 支付错误：支付失败或异常

## 安全说明

1. 所有敏感信息都通过环境变量配置
2. 使用 API v3 密钥加密敏感信息
3. 使用证书进行签名和验证
4. 所有支付相关的操作都有日志记录
5. 使用事务确保数据一致性

## 注意事项

1. 确保证书文件权限正确设置
2. 定期检查证书有效期
3. 正确配置回调通知地址
4. 处理重复通知的情况（使用 transaction_id 作为唯一标识）
5. 注意订单超时处理
6. 定期检查未完成订单的状态

## 常见问题

1. 证书加载失败
   - 检查证书文件路径是否正确
   - 检查证书文件权限
   - 确保证书格式正确

2. 签名验证失败
   - 检查私钥是否正确
   - 确认签名参数是否完整
   - 验证请求时间戳是否有效

3. 回调通知处理失败
   - 确保回调地址可以正常访问
   - 检查回调数据的解密是否正确
   - 验证签名是否正确

4. 订单状态不同步
   - 主动调用查询接口确认状态
   - 使用事务确保状态更新的原子性
   - 添加重试机制 