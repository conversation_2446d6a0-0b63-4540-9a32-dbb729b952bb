import { Controller, Post, Body, HttpCode, HttpStatus, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { VerificationService } from './verification.service';
import { SendVerificationCodeDto } from './dto/send-verification-code.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';

@ApiTags('验证')
@Controller('verification')
export class VerificationController {
  constructor(private readonly verificationService: VerificationService) {}

  @Post('send-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '发送邮箱验证码' })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器错误' })
  async sendVerificationCode(@Body() dto: SendVerificationCodeDto) {
    const result = await this.verificationService.sendVerificationCode(dto.email);
    
    if (!result) {
      throw new BadRequestException('验证码发送失败，请稍后重试');
    }
    
    return { success: true, message: '验证码发送成功' };
  }

  @Post('verify-code')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '验证邮箱验证码' })
  @ApiResponse({ status: 200, description: '验证码验证成功' })
  @ApiResponse({ status: 400, description: '验证码错误或已过期' })
  async verifyCode(@Body() dto: VerifyCodeDto) {
    const isValid = await this.verificationService.verifyCode(dto.email, dto.code);
    
    if (!isValid) {
      throw new BadRequestException('验证码错误或已过期');
    }
    
    return { success: true, message: '验证码验证成功' };
  }
}
