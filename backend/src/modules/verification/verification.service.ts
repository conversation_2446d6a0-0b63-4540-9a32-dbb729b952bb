import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { MailService } from '../mail/mail.service';

@Injectable()
export class VerificationService {
  private readonly logger = new Logger(VerificationService.name);
  private readonly VERIFICATION_CODE_PREFIX = 'verification:email:';
  private readonly VERIFICATION_CODE_EXPIRY = 600; // 10分钟过期

  constructor(
    private readonly redisService: RedisService,
    private readonly mailService: MailService,
  ) {}

  /**
   * 生成验证码
   * @returns 6位数字验证码
   */
  generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 保存验证码到Redis
   * @param email 邮箱
   * @param code 验证码
   */
  async saveCode(email: string, code: string): Promise<void> {
    const key = this.getRedisKey(email);
    await this.redisService.set(key, code, this.VERIFICATION_CODE_EXPIRY);
    this.logger.log(`验证码已保存: ${email}`);
  }

  /**
   * 获取验证码
   * @param email 邮箱
   * @returns 验证码
   */
  async getCode(email: string): Promise<string | null> {
    const key = this.getRedisKey(email);
    return this.redisService.get(key);
  }

  /**
   * 验证验证码
   * @param email 邮箱
   * @param code 验证码
   * @returns 是否验证成功
   */
  async verifyCode(email: string, code: string): Promise<boolean> {
    const savedCode = await this.getCode(email);
    
    if (!savedCode) {
      this.logger.warn(`验证码不存在或已过期: ${email}`);
      return false;
    }

    const isValid = savedCode === code;
    
    if (isValid) {
      // 验证成功后删除验证码，防止重复使用
      await this.removeCode(email);
      this.logger.log(`验证码验证成功: ${email}`);
    } else {
      this.logger.warn(`验证码验证失败: ${email}`);
    }

    return isValid;
  }

  /**
   * 删除验证码
   * @param email 邮箱
   */
  async removeCode(email: string): Promise<void> {
    const key = this.getRedisKey(email);
    await this.redisService.del(key);
    this.logger.log(`验证码已删除: ${email}`);
  }

  /**
   * 发送验证码
   * @param email 邮箱
   * @returns 是否发送成功
   */
  async sendVerificationCode(email: string): Promise<boolean> {
    try {
      const code = this.generateCode();
      
      // 保存验证码到Redis
      await this.saveCode(email, code);
      
      // 发送验证码邮件
      const result = await this.mailService.sendVerificationCode(email, code);
      
      if (!result) {
        this.logger.error(`验证码邮件发送失败: ${email}`);
        return false;
      }
      
      this.logger.log(`验证码邮件发送成功: ${email}`);
      return true;
    } catch (error) {
      this.logger.error(`发送验证码失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取Redis键名
   * @param email 邮箱
   * @returns Redis键名
   */
  private getRedisKey(email: string): string {
    return `${this.VERIFICATION_CODE_PREFIX}${email}`;
  }
}
