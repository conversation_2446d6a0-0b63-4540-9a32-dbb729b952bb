import { Injectable, Logger, HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { SubscriptionPlan, SubscriptionPlanStatus } from '../entities/subscription-plan.entity';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto, SubscriptionPlanResponseDto } from '../dto/subscription-plan.dto';

@Injectable()
export class SubscriptionPlanService {
  private readonly logger = new Logger(SubscriptionPlanService.name);

  constructor(
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
  ) {}

  /**
   * 创建订阅计划
   */
  async createPlan(createPlanDto: CreateSubscriptionPlanDto): Promise<SubscriptionPlan> {
    try {
      // 检查计划名称是否已存在
      const existingPlan = await this.subscriptionPlanRepository.findOne({
        where: { name: createPlanDto.name }
      });

      if (existingPlan) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: '订阅计划名称已存在',
            code: 'PLAN_NAME_EXISTS'
          },
          HttpStatus.BAD_REQUEST
        );
      }

      const plan = this.subscriptionPlanRepository.create({
        ...createPlanDto,
        status: createPlanDto.status || SubscriptionPlanStatus.ACTIVE,
        sortOrder: createPlanDto.sortOrder || 0,
        features: createPlanDto.features || [],
        trialDays: createPlanDto.trialDays || 0
      });

      const savedPlan = await this.subscriptionPlanRepository.save(plan);
      this.logger.debug('Subscription plan created', { planId: savedPlan.id, name: savedPlan.name });
      
      return savedPlan;
    } catch (error) {
      this.logger.error('Failed to create subscription plan', {
        error: error instanceof Error ? error.message : error,
        planName: createPlanDto.name
      });
      throw error;
    }
  }

  /**
   * 获取所有可用的订阅计划
   */
  async getAvailablePlans(): Promise<SubscriptionPlan[]> {
    return await this.subscriptionPlanRepository.find({
      where: { status: SubscriptionPlanStatus.ACTIVE },
      order: { sortOrder: 'ASC', createdAt: 'ASC' }
    });
  }

  /**
   * 获取所有订阅计划（包括非活跃的）
   */
  async getAllPlans(): Promise<SubscriptionPlan[]> {
    return await this.subscriptionPlanRepository.find({
      order: { sortOrder: 'ASC', createdAt: 'ASC' }
    });
  }

  /**
   * 根据ID获取订阅计划
   */
  async getPlanById(id: string): Promise<SubscriptionPlan> {
    const plan = await this.subscriptionPlanRepository.findOne({
      where: { id }
    });

    if (!plan) {
      throw new NotFoundException(`订阅计划不存在: ${id}`);
    }

    return plan;
  }

  /**
   * 更新订阅计划
   */
  async updatePlan(id: string, updatePlanDto: UpdateSubscriptionPlanDto): Promise<SubscriptionPlan> {
    const plan = await this.getPlanById(id);

    // 如果更新名称，检查是否与其他计划重名
    if (updatePlanDto.name && updatePlanDto.name !== plan.name) {
      const existingPlan = await this.subscriptionPlanRepository.findOne({
        where: { name: updatePlanDto.name }
      });

      if (existingPlan) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: '订阅计划名称已存在',
            code: 'PLAN_NAME_EXISTS'
          },
          HttpStatus.BAD_REQUEST
        );
      }
    }

    try {
      await this.subscriptionPlanRepository.update(id, updatePlanDto);
      const updatedPlan = await this.getPlanById(id);
      
      this.logger.debug('Subscription plan updated', { planId: id, name: updatedPlan.name });
      return updatedPlan;
    } catch (error) {
      this.logger.error('Failed to update subscription plan', {
        error: error instanceof Error ? error.message : error,
        planId: id
      });
      throw error;
    }
  }

  /**
   * 删除订阅计划（软删除 - 将状态设为 ARCHIVED）
   */
  async deletePlan(id: string): Promise<void> {
    const plan = await this.getPlanById(id);

    try {
      await this.subscriptionPlanRepository.update(id, {
        status: SubscriptionPlanStatus.ARCHIVED
      });
      
      this.logger.debug('Subscription plan archived', { planId: id, name: plan.name });
    } catch (error) {
      this.logger.error('Failed to archive subscription plan', {
        error: error instanceof Error ? error.message : error,
        planId: id
      });
      throw error;
    }
  }

  /**
   * 激活订阅计划
   */
  async activatePlan(id: string): Promise<SubscriptionPlan> {
    const plan = await this.getPlanById(id);

    try {
      await this.subscriptionPlanRepository.update(id, {
        status: SubscriptionPlanStatus.ACTIVE
      });
      
      const updatedPlan = await this.getPlanById(id);
      this.logger.debug('Subscription plan activated', { planId: id, name: plan.name });
      
      return updatedPlan;
    } catch (error) {
      this.logger.error('Failed to activate subscription plan', {
        error: error instanceof Error ? error.message : error,
        planId: id
      });
      throw error;
    }
  }

  /**
   * 停用订阅计划
   */
  async deactivatePlan(id: string): Promise<SubscriptionPlan> {
    const plan = await this.getPlanById(id);

    try {
      await this.subscriptionPlanRepository.update(id, {
        status: SubscriptionPlanStatus.INACTIVE
      });
      
      const updatedPlan = await this.getPlanById(id);
      this.logger.debug('Subscription plan deactivated', { planId: id, name: plan.name });
      
      return updatedPlan;
    } catch (error) {
      this.logger.error('Failed to deactivate subscription plan', {
        error: error instanceof Error ? error.message : error,
        planId: id
      });
      throw error;
    }
  }

  /**
   * 获取计划的统计信息
   */
  async getPlanStats(planId: string): Promise<{
    totalSubscriptions: number;
    activeSubscriptions: number;
    totalRevenue: number;
  }> {
    const plan = await this.getPlanById(planId);
    
    // 这里需要与订阅表关联查询，暂时返回占位数据
    // 实际实现需要根据订阅表的数据来计算
    return {
      totalSubscriptions: 0,
      activeSubscriptions: 0,
      totalRevenue: 0
    };
  }

  /**
   * 转换为响应DTO
   */
  toResponseDto(plan: SubscriptionPlan): SubscriptionPlanResponseDto {
    return {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      type: plan.type,
      durationDays: plan.durationDays,
      aiCallLimit: plan.aiCallLimit,
      maxCourses: plan.maxCourses,
      trialDays: plan.trialDays,
      status: plan.status,
      sortOrder: plan.sortOrder,
      features: plan.features || [],
      stripePriceId: plan.stripePriceId,
      createdAt: plan.createdAt,
      updatedAt: plan.updatedAt
    };
  }
}