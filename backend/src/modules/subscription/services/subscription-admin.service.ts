import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { SubscriptionPlan } from '../entities/subscription-plan.entity';
import { User } from '../../user/entities/user.entity';

export interface SubscriptionStats {
  totalSubscribers: number;
  monthlyRevenue: number;
  activeTrials: number;
  conversionRate: number;
}

export interface RecentActivity {
  id: string;
  type: 'new_subscription' | 'trial_ended' | 'cancelled' | 'upgraded';
  message: string;
  timestamp: string;
  userEmail?: string;
}

export interface UserSubscriptionListItem {
  id: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
  planName: string;
  status: SubscriptionStatus;
  price: number;
  isActive: boolean;
  startDate: Date;
  endDate: Date;
  trialEndDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSubscriptionDetail extends UserSubscriptionListItem {
  plan?: {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
  };
  paymentHistory: {
    id: string;
    amount: number;
    status: string;
    createdAt: Date;
  }[];
}

export interface SubscriptionListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: SubscriptionStatus;
  startDate?: Date;
  endDate?: Date;
}

@Injectable()
export class SubscriptionAdminService {
  private readonly logger = new Logger(SubscriptionAdminService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * 获取订阅统计数据
   */
  async getSubscriptionStats(): Promise<SubscriptionStats> {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // 获取当前活跃订阅数
    const totalSubscribers = await this.subscriptionRepository.count({
      where: {
        status: SubscriptionStatus.ACTIVE,
        isActive: true,
        endDate: MoreThanOrEqual(now)
      }
    });

    // 获取本月收入
    const monthlyRevenueQuery = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .select('SUM(subscription.price)', 'total')
      .where('subscription.createdAt >= :thisMonth', { thisMonth })
      .where('subscription.createdAt < :nextMonth', { nextMonth: new Date(now.getFullYear(), now.getMonth() + 1, 1) })
      .getRawOne();

    const monthlyRevenue = parseFloat(monthlyRevenueQuery?.total || '0');

    // 获取活跃试用用户数
    const activeTrials = await this.subscriptionRepository.count({
      where: {
        status: SubscriptionStatus.TRIAL,
        isActive: true,
        trialEndDate: MoreThanOrEqual(now)
      }
    });

    // 计算转化率 (过去30天内从试用转为付费的用户比例)
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const trialUsersCount = await this.subscriptionRepository.count({
      where: {
        status: SubscriptionStatus.TRIAL,
        createdAt: MoreThanOrEqual(thirtyDaysAgo)
      }
    });

    const convertedUsersCount = await this.subscriptionRepository.count({
      where: {
        status: SubscriptionStatus.ACTIVE,
        createdAt: MoreThanOrEqual(thirtyDaysAgo)
      }
    });

    const conversionRate = trialUsersCount > 0 ? (convertedUsersCount / trialUsersCount) * 100 : 0;

    return {
      totalSubscribers,
      monthlyRevenue,
      activeTrials,
      conversionRate
    };
  }

  /**
   * 获取最近的订阅活动
   */
  async getRecentActivities(limit: number = 10): Promise<RecentActivity[]> {
    const activities: RecentActivity[] = [];

    // 获取最近的订阅记录
    const recentSubscriptions = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.plan', 'plan')
      .orderBy('subscription.createdAt', 'DESC')
      .limit(limit)
      .getMany();

    for (const subscription of recentSubscriptions) {
      let activityType: RecentActivity['type'] = 'new_subscription';
      let message = '';

      switch (subscription.status) {
        case SubscriptionStatus.ACTIVE:
          activityType = 'new_subscription';
          message = `用户 ${subscription.user?.email || '未知'} 订阅了 ${subscription.planName}`;
          break;
        case SubscriptionStatus.TRIAL:
          activityType = 'new_subscription';
          message = `用户 ${subscription.user?.email || '未知'} 开始试用 ${subscription.planName}`;
          break;
        case SubscriptionStatus.CANCELLED:
          activityType = 'cancelled';
          message = `用户 ${subscription.user?.email || '未知'} 取消了 ${subscription.planName} 订阅`;
          break;
        case SubscriptionStatus.EXPIRED:
          activityType = 'trial_ended';
          message = `用户 ${subscription.user?.email || '未知'} 的 ${subscription.planName} 已过期`;
          break;
      }

      activities.push({
        id: subscription.id,
        type: activityType,
        message,
        timestamp: subscription.createdAt.toISOString(),
        userEmail: subscription.user?.email
      });
    }

    // 获取最近取消的订阅
    const recentCancellations = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .where('subscription.cancelledAt IS NOT NULL')
      .orderBy('subscription.cancelledAt', 'DESC')
      .limit(Math.floor(limit / 2))
      .getMany();

    for (const subscription of recentCancellations) {
      activities.push({
        id: subscription.id + '_cancelled',
        type: 'cancelled',
        message: `用户 ${subscription.user?.email || '未知'} 取消了订阅`,
        timestamp: subscription.cancelledAt!.toISOString(),
        userEmail: subscription.user?.email
      });
    }

    // 按时间排序并限制数量
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * 获取用户订阅列表
   */
  async getUserSubscriptions(query: SubscriptionListQuery): Promise<{
    data: UserSubscriptionListItem[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      startDate,
      endDate
    } = query;

    const queryBuilder = this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.plan', 'plan');

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(user.email ILIKE :search OR user.name ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // 状态筛选
    if (status) {
      queryBuilder.andWhere('subscription.status = :status', { status });
    }

    // 时间范围筛选
    if (startDate) {
      queryBuilder.andWhere('subscription.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      queryBuilder.andWhere('subscription.createdAt <= :endDate', { endDate });
    }

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.offset(offset).limit(limit);

    // 排序
    queryBuilder.orderBy('subscription.createdAt', 'DESC');

    const [subscriptions, total] = await queryBuilder.getManyAndCount();

    const data: UserSubscriptionListItem[] = subscriptions.map(subscription => ({
      id: subscription.id,
      user: {
        id: subscription.user.id,
        email: subscription.user.email,
        name: subscription.user.name || subscription.user.email
      },
      planName: subscription.planName,
      status: subscription.status,
      price: subscription.price,
      isActive: subscription.isActive,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      trialEndDate: subscription.trialEndDate,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt
    }));

    return {
      data,
      total,
      page,
      limit
    };
  }

  /**
   * 获取用户订阅详情
   */
  async getUserSubscriptionById(subscriptionId: string): Promise<UserSubscriptionDetail | null> {
    const subscription = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.plan', 'plan')
      .where('subscription.id = :subscriptionId', { subscriptionId })
      .getOne();

    if (!subscription) {
      return null;
    }

    // 获取支付历史 (如果有Order实体的话)
    const paymentHistory: UserSubscriptionDetail['paymentHistory'] = [];

    return {
      id: subscription.id,
      user: {
        id: subscription.user.id,
        email: subscription.user.email,
        name: subscription.user.name || subscription.user.email
      },
      planName: subscription.planName,
      status: subscription.status,
      price: subscription.price,
      isActive: subscription.isActive,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      trialEndDate: subscription.trialEndDate,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
      plan: subscription.plan ? {
        id: subscription.plan.id,
        name: subscription.plan.name,
        description: subscription.plan.description,
        price: subscription.plan.price,
        duration: subscription.plan.durationDays
      } : undefined,
      paymentHistory
    };
  }

  /**
   * 更新订阅状态
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    status: SubscriptionStatus,
    adminUserId: string
  ): Promise<boolean> {
    try {
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId }
      });

      if (!subscription) {
        this.logger.warn(`Subscription not found: ${subscriptionId}`);
        return false;
      }

      subscription.status = status;
      
      // 根据状态更新其他字段
      if (status === SubscriptionStatus.CANCELLED) {
        subscription.isActive = false;
        subscription.cancelledAt = new Date();
      } else if (status === SubscriptionStatus.ACTIVE) {
        subscription.isActive = true;
        subscription.cancelledAt = undefined;
      }

      await this.subscriptionRepository.save(subscription);

      this.logger.log(
        `Subscription ${subscriptionId} status updated to ${status} by admin ${adminUserId}`
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to update subscription status: ${error.message}`,
        error.stack
      );
      return false;
    }
  }

  /**
   * 延长订阅期限
   */
  async extendSubscription(
    subscriptionId: string,
    extensionDays: number,
    adminUserId: string
  ): Promise<boolean> {
    try {
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId }
      });

      if (!subscription) {
        this.logger.warn(`Subscription not found: ${subscriptionId}`);
        return false;
      }

      const currentEndDate = subscription.endDate;
      const newEndDate = new Date(currentEndDate.getTime() + extensionDays * 24 * 60 * 60 * 1000);
      
      subscription.endDate = newEndDate;
      await this.subscriptionRepository.save(subscription);

      this.logger.log(
        `Subscription ${subscriptionId} extended by ${extensionDays} days by admin ${adminUserId}`
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to extend subscription: ${error.message}`,
        error.stack
      );
      return false;
    }
  }
} 