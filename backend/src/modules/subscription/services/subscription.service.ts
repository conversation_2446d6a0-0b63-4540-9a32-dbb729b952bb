import { Injectable, Logger, Inject, forwardRef, HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, <PERSON><PERSON>han, LessThanOrEqual } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { SubscriptionPlan } from '../entities/subscription-plan.entity';
import { CreateSubscriptionDto, UpdateSubscriptionDto, SubscriptionResponseDto, SubscriptionUsageDto, CancelSubscriptionDto } from '../dto/subscription.dto';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
  ) {}

  // 添加用于测试的时间
  private testNow?: Date;

  /**
   * 设置测试时间（仅用于测试）
   */
  setTestTime(date: Date) {
    this.testNow = date;
    this.logger.debug('Set test time', { testTime: date });
  }

  /**
   * 清除测试时间
   */
  clearTestTime() {
    this.testNow = undefined;
    this.logger.debug('Cleared test time');
  }

  /**
   * 获取当前时间
   * 如果设置了测试时间，返回测试时间
   * 否则返回真实时间
   */
  private getNow(): Date {
    return this.testNow || new Date();
  }

  /**
   * 计算新订阅的起止时间
   * 如果有现有订阅，新订阅从现有订阅的结束时间开始计算
   * 如果没有现有订阅，从当前时间开始计算
   */
  private async calculateNewSubscriptionDates(
    userId: string,
    durationDays: number,
    entityManager?: EntityManager
  ): Promise<{ startDate: Date; endDate: Date }> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();
    
    // 查找用户当前有效的订阅（按结束时间降序排序）
    const currentSubscriptions = await repo.find({
      where: {
        userId,
        endDate: MoreThan(now),
        isActive: true
      },
      order: { endDate: 'DESC' }
    });

    this.logger.debug(`Found ${currentSubscriptions.length} active subscriptions for user ${userId}`);

    if (currentSubscriptions.length > 0) {
      // 如果有有效订阅，找出最晚的结束时间
      const latestEndDate = currentSubscriptions[0].endDate;
      const startDate = latestEndDate;
      const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
      
      this.logger.debug('Calculated subscription dates', {
        userId,
        existingEndDate: latestEndDate,
        newStartDate: startDate,
        newEndDate: endDate
      });
      
      return { startDate, endDate };
    } else {
      // 如果没有有效订阅，从当前时间开始计算
      const endDate = new Date(now.getTime() + durationDays * 24 * 60 * 60 * 1000);
      
      this.logger.debug('Calculated subscription dates (no existing subscription)', {
        userId,
        startDate: now,
        endDate
      });
      
      return { startDate: now, endDate };
    }
  }

  /**
   * 检查未来订阅
   * 如果已经存在未来订阅，则抛出异常
   */
  private async checkFutureSubscriptions(
    userId: string,
    entityManager?: EntityManager
  ): Promise<void> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();

    const futureSubscriptions = await repo.find({
      where: {
        userId,
        startDate: MoreThan(now),
        isActive: true,
      },
    });

    if (futureSubscriptions.length > 0) {
      this.logger.warn(`User ${userId} already has future subscription`, {
        existingSubscriptionId: futureSubscriptions[0].id,
        startDate: futureSubscriptions[0].startDate,
        endDate: futureSubscriptions[0].endDate,
      });
      
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          error: `您已有一个从 ${this.formatDate(futureSubscriptions[0].startDate)} 开始生效的订阅，暂时无法创建新订阅。如需帮助，请联系客服。`,
          code: 'FUTURE_SUBSCRIPTION_EXISTS'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  private formatDate(date: Date): string {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }

  /**
   * 创建订阅
   */
  async createSubscription(
    userId: string,
    params: {
      planName: string;
      price: number;
      duration: number;
      aiCallLimit: number;
    },
    entityManager?: EntityManager,
  ): Promise<Subscription> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;

    try {
      // 1. 计算新订阅的起止时间
      const { startDate, endDate } = await this.calculateNewSubscriptionDates(
        userId,
        params.duration,
        entityManager
      );

      // 2. 如果是未来订阅，检查是否已存在未来订阅
      const now = this.getNow();
      if (startDate > now) {
        await this.checkFutureSubscriptions(userId, entityManager);
      }

      // 3. 创建新订阅，初始状态为未激活，等待支付确认
      const subscription = repo.create({
        userId,
        planName: params.planName,
        price: params.price,
        startDate,
        endDate,
        aiCallLimit: params.aiCallLimit,
        aiCallUsed: 0,
        isActive: false // 初始状态为未激活，等待支付确认
      });

      this.logger.debug('Creating subscription', { 
        userId, 
        planName: params.planName,
        startDate,
        endDate,
        isActive: false
      });

      const savedSubscription = await repo.save(subscription);
      this.logger.debug('Subscription created', { 
        subscriptionId: savedSubscription.id,
        startDate: savedSubscription.startDate,
        endDate: savedSubscription.endDate,
        isActive: savedSubscription.isActive
      });

      return savedSubscription;
    } catch (error) {
      this.logger.error('Failed to create subscription', {
        error: error instanceof Error ? error.message : error,
        userId,
        planName: params.planName
      });
      throw error;
    }
  }

  /**
   * 获取用户当前订阅
   * 返回优先级：
   * 1. 当前生效的订阅
   * 2. 未来即将生效的订阅
   */
  async getCurrentSubscription(userId: string, entityManager?: EntityManager): Promise<Subscription | null> {
    const repo = entityManager ? entityManager.getRepository(Subscription) : this.subscriptionRepository;
    
    return await repo.findOne({
      where: {
        userId,
        isActive: true,
      },
      order: {
        endDate: 'DESC',
      },
    });
  }

  /**
   * 获取用户订阅历史
   */
  async getSubscriptionHistory(userId: string): Promise<{ items: Subscription[]; total: number }> {
    const subscriptions = await this.subscriptionRepository.find({
      where: {
        userId,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    return {
      items: subscriptions,
      total: subscriptions.length,
    };
  }

  /**
   * 更新 AI 调用次数
   */
  async updateAiCallUsed(subscriptionId: string, used: number): Promise<void> {
    try {
      await this.subscriptionRepository.update(
        { id: subscriptionId },
        { aiCallUsed: used },
      );
    } catch (error) {
      this.logger.error('Failed to update AI call used count', {
        error: error instanceof Error ? error.message : error,
        subscriptionId,
        used,
      });
      throw error;
    }
  }

  /**
   * 更新订阅
   */
  async updateSubscription(id: string, data: Partial<Subscription>) {
    await this.subscriptionRepository.update(id, data);
    return await this.subscriptionRepository.findOne({ where: { id } });
  }

  /**
   * 使当前有效的订阅失效
   * 注意：此方法仅在特殊情况下使用，如订阅被取消或管理员手动停用订阅时。
   * 在正常的订阅流程中（如创建新订阅、兑换礼品码等），不应该停用当前生效的订阅。
   */
  private async deactivateActiveSubscriptions(
    userId: string,
    entityManager?: EntityManager
  ): Promise<void> {
    const repo = entityManager?.getRepository(Subscription) || this.subscriptionRepository;
    const now = this.getNow();

    try {
      // 查找当前生效的订阅（开始时间小于等于现在，结束时间大于现在）
      const activeSubscriptions = await repo.find({
        where: {
          userId,
          startDate: LessThanOrEqual(now),
          endDate: MoreThan(now),
          isActive: true
        }
      });

      if (activeSubscriptions.length === 0) {
        this.logger.debug(`No active subscriptions found for user ${userId}`);
        return;
      }

      // 逐个更新并保存订阅
      for (const subscription of activeSubscriptions) {
        subscription.isActive = false;
        subscription.updatedAt = now;
        await repo.save(subscription);
        
        this.logger.debug(`Deactivated subscription`, {
          subscriptionId: subscription.id,
          userId,
          oldEndDate: subscription.endDate
        });
      }

      this.logger.debug(`Successfully deactivated all subscriptions`, {
        userId,
        count: activeSubscriptions.length,
        subscriptionIds: activeSubscriptions.map(s => s.id)
      });
    } catch (error) {
      this.logger.error('Failed to deactivate subscriptions', {
        error: error instanceof Error ? error.message : error,
        userId
      });
      throw error;
    }
  }

  async deactivateSubscription(id: string) {
    return await this.subscriptionRepository.update(id, {
      isActive: false,
    });
  }

  /**
   * 基于订阅计划创建订阅
   */
  async createSubscriptionFromPlan(
    userId: string,
    createSubscriptionDto: CreateSubscriptionDto
  ): Promise<SubscriptionResponseDto> {
    const { planId, autoRenew = true, stripeSubscriptionId } = createSubscriptionDto;

    // 获取订阅计划
    const plan = await this.subscriptionPlanRepository.findOne({
      where: { id: planId }
    });

    if (!plan) {
      throw new NotFoundException(`订阅计划不存在: ${planId}`);
    }

    // 创建订阅 - 初始状态为 PENDING，等待支付确认
    const subscription = await this.createSubscription(userId, {
      planName: plan.name,
      price: plan.price,
      duration: plan.durationDays,
      aiCallLimit: plan.aiCallLimit
    });

    // 更新额外的订阅信息 - 设置为 PENDING 状态
    await this.subscriptionRepository.update(subscription.id, {
      planId: plan.id,
      autoRenew,
      stripeSubscriptionId,
      status: SubscriptionStatus.PENDING, // 等待支付确认
      isActive: false, // 暂时未激活
      trialEndDate: plan.trialDays > 0 ? new Date(Date.now() + plan.trialDays * 24 * 60 * 60 * 1000) : undefined
    });

    const updatedSubscription = await this.subscriptionRepository.findOne({
      where: { id: subscription.id },
      relations: ['plan']
    });
    
    if (!updatedSubscription) {
      throw new NotFoundException('订阅更新失败');
    }
    
    return this.toResponseDto(updatedSubscription);
  }

  /**
   * 获取订阅使用情况
   */
  async getSubscriptionUsage(userId: string): Promise<SubscriptionUsageDto> {
    const subscription = await this.getCurrentSubscription(userId);
    const now = this.getNow();

    if (!subscription) {
      throw new NotFoundException('未找到有效订阅');
    }

    const daysRemaining = Math.max(0, Math.ceil((subscription.endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)));
    const aiCallsRemaining = Math.max(0, subscription.aiCallLimit - subscription.aiCallUsed);
    const usagePercentage = subscription.aiCallLimit > 0 ? (subscription.aiCallUsed / subscription.aiCallLimit) * 100 : 0;

    return {
      aiCallsUsed: subscription.aiCallUsed,
      aiCallsLimit: subscription.aiCallLimit,
      aiCallsRemaining,
      usagePercentage,
      daysRemaining,
      isActive: subscription.isActive,
      status: subscription.status,
      renewalDate: subscription.nextBillingDate || subscription.endDate,
      trialEndDate: subscription.trialEndDate
    };
  }

  /**
   * 取消订阅
   */
  async cancelSubscription(
    userId: string,
    cancelSubscriptionDto: CancelSubscriptionDto
  ): Promise<SubscriptionResponseDto> {
    const { immediate = false, reason } = cancelSubscriptionDto;
    const subscription = await this.getCurrentSubscription(userId);

    if (!subscription) {
      throw new NotFoundException('未找到有效订阅');
    }

    const now = this.getNow();

    if (immediate) {
      // 立即取消订阅
      await this.subscriptionRepository.update(subscription.id, {
        status: SubscriptionStatus.CANCELLED,
        isActive: false,
        autoRenew: false,
        cancelledAt: now,
        endDate: now // 立即结束
      });
    } else {
      // 到期后取消（不自动续费）
      await this.subscriptionRepository.update(subscription.id, {
        autoRenew: false,
        cancelledAt: now
      });
    }

    this.logger.debug('Subscription cancelled', {
      subscriptionId: subscription.id,
      userId,
      immediate,
      reason
    });

    const updatedSubscription = await this.subscriptionRepository.findOne({
      where: { id: subscription.id },
      relations: ['plan']
    });
    
    if (!updatedSubscription) {
      throw new NotFoundException('订阅更新失败');
    }
    
    return this.toResponseDto(updatedSubscription);
  }

  /**
   * 重新激活订阅
   */
  async reactivateSubscription(userId: string): Promise<SubscriptionResponseDto> {
    const subscription = await this.getCurrentSubscription(userId);

    if (!subscription) {
      throw new NotFoundException('未找到订阅');
    }

    if (subscription.status !== SubscriptionStatus.CANCELLED) {
      throw new HttpException('订阅状态不允许重新激活', HttpStatus.BAD_REQUEST);
    }

    const now = this.getNow();

    // 如果订阅已过期，不能重新激活
    if (subscription.endDate <= now) {
      throw new HttpException('订阅已过期，无法重新激活', HttpStatus.BAD_REQUEST);
    }

    await this.subscriptionRepository.update(subscription.id, {
      status: SubscriptionStatus.ACTIVE,
      isActive: true,
      autoRenew: true,
      cancelledAt: undefined
    });

    this.logger.debug('Subscription reactivated', {
      subscriptionId: subscription.id,
      userId
    });

    const updatedSubscription = await this.subscriptionRepository.findOne({
      where: { id: subscription.id },
      relations: ['plan']
    });
    
    if (!updatedSubscription) {
      throw new NotFoundException('订阅更新失败');
    }
    
    return this.toResponseDto(updatedSubscription);
  }

  /**
   * 增加AI调用使用次数
   */
  async incrementAiCallUsage(
    userId: string,
    count: number = 1
  ): Promise<{ success: boolean; remainingCalls: number }> {
    const subscription = await this.getCurrentSubscription(userId);

    if (!subscription) {
      throw new NotFoundException('未找到有效订阅');
    }

    const newUsedCount = subscription.aiCallUsed + count;
    
    if (newUsedCount > subscription.aiCallLimit) {
      throw new HttpException('AI调用次数已达上限', HttpStatus.BAD_REQUEST);
    }

    await this.subscriptionRepository.update(subscription.id, {
      aiCallUsed: newUsedCount
    });

    const remainingCalls = subscription.aiCallLimit - newUsedCount;

    this.logger.debug('AI call usage incremented', {
      subscriptionId: subscription.id,
      userId,
      count,
      newUsedCount,
      remainingCalls
    });

    return {
      success: true,
      remainingCalls
    };
  }

  /**
   * 根据ID查找订阅
   */
  async findById(subscriptionId: string): Promise<Subscription | null> {
    return this.subscriptionRepository.findOne({
      where: { id: subscriptionId },
      relations: ['plan']
    });
  }

  /**
   * 转换为响应DTO
   */
  private toResponseDto(subscription: Subscription): SubscriptionResponseDto {
    return {
      id: subscription.id,
      userId: subscription.userId,
      planId: subscription.planId,
      planName: subscription.planName,
      price: subscription.price,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      aiCallLimit: subscription.aiCallLimit,
      aiCallUsed: subscription.aiCallUsed,
      isActive: subscription.isActive,
      status: subscription.status,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      autoRenew: subscription.autoRenew,
      trialEndDate: subscription.trialEndDate,
      cancelledAt: subscription.cancelledAt,
      nextBillingDate: subscription.nextBillingDate,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
      plan: subscription.plan ? {
        id: subscription.plan.id,
        name: subscription.plan.name,
        description: subscription.plan.description,
        type: subscription.plan.type,
        features: subscription.plan.features || []
      } : undefined
    };
  }

  /**
   * 检查用户是否有有效订阅
   */
  async hasValidSubscription(userId: string): Promise<boolean> {
    const subscription = await this.getCurrentSubscription(userId);
    const now = this.getNow();
    
    if (!subscription) {
      return false;
    }
    
    // 检查订阅是否过期，如果过期则自动更新状态
    if (subscription.endDate <= now && subscription.status === SubscriptionStatus.ACTIVE) {
      this.logger.warn(`Subscription expired, updating status`, {
        subscriptionId: subscription.id,
        userId,
        endDate: subscription.endDate,
        now
      });
      
      // 自动更新过期订阅状态
      await this.updateSubscriptionStatus(subscription.id, SubscriptionStatus.EXPIRED);
      return false;
    }
    
    const isValid = subscription !== null && 
           subscription.isActive && 
           subscription.startDate <= now && 
           subscription.endDate > now &&
           subscription.status === SubscriptionStatus.ACTIVE;
    
    this.logger.debug('Subscription validity check', {
      userId,
      subscriptionId: subscription.id,
      isActive: subscription.isActive,
      status: subscription.status,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      now,
      isValid
    });
    
    return isValid;
  }

  /**
   * 检查用户是否可以使用AI功能
   */
  async canUseAi(userId: string): Promise<{ canUse: boolean; remainingCalls: number }> {
    const subscription = await this.getCurrentSubscription(userId);
    
    if (!subscription || !await this.hasValidSubscription(userId)) {
      return { canUse: false, remainingCalls: 0 };
    }

    const remainingCalls = subscription.aiCallLimit - subscription.aiCallUsed;
    return {
      canUse: remainingCalls > 0,
      remainingCalls
    };
  }

  /**
   * 根据Stripe订阅ID获取本地订阅记录
   */
  async getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null> {
    return await this.subscriptionRepository.findOne({
      where: { stripeSubscriptionId },
      relations: ['plan']
    });
  }

  /**
   * 更新订阅状态
   */
  async updateSubscriptionStatus(subscriptionId: string, status: SubscriptionStatus): Promise<void> {
    const updateData: Partial<Subscription> = { status };
    
    // 当状态变为 ACTIVE 时，设置 isActive 为 true
    if (status === SubscriptionStatus.ACTIVE) {
      updateData.isActive = true;
    }
    // 当状态变为非激活状态时，设置 isActive 为 false
    else if (status === SubscriptionStatus.CANCELLED || status === SubscriptionStatus.EXPIRED || status === SubscriptionStatus.INACTIVE) {
      updateData.isActive = false;
    }
    
    await this.subscriptionRepository.update(subscriptionId, updateData);
    
    this.logger.debug('Updated subscription status', {
      subscriptionId,
      status,
      isActive: updateData.isActive
    });
  }

  /**
   * 定时任务：每小时检查并更新过期的订阅状态
   */
  @Cron(CronExpression.EVERY_HOUR)
  async updateExpiredSubscriptions(): Promise<void> {
    const now = this.getNow();
    
    try {
      // 查找所有过期但仍标记为活跃的订阅
      const expiredSubscriptions = await this.subscriptionRepository.find({
        where: {
          endDate: LessThanOrEqual(now),
          status: SubscriptionStatus.ACTIVE,
          isActive: true
        }
      });

      if (expiredSubscriptions.length === 0) {
        this.logger.debug('No expired subscriptions found');
        return;
      }

      this.logger.warn(`Found ${expiredSubscriptions.length} expired subscriptions to update`);

      // 批量更新过期订阅
      for (const subscription of expiredSubscriptions) {
        await this.updateSubscriptionStatus(subscription.id, SubscriptionStatus.EXPIRED);
        
        this.logger.warn('Updated expired subscription', {
          subscriptionId: subscription.id,
          userId: subscription.userId,
          endDate: subscription.endDate,
          now
        });
      }

      this.logger.warn(`Successfully updated ${expiredSubscriptions.length} expired subscriptions`);
    } catch (error) {
      this.logger.error('Failed to update expired subscriptions', {
        error: error instanceof Error ? error.message : error
      });
    }
  }

  /**
   * 手动触发过期订阅更新（用于测试或手动维护）
   */
  async manualUpdateExpiredSubscriptions(): Promise<{ updated: number; subscriptions: any[] }> {
    const now = this.getNow();
    
    const expiredSubscriptions = await this.subscriptionRepository.find({
      where: {
        endDate: LessThanOrEqual(now),
        status: SubscriptionStatus.ACTIVE,
        isActive: true
      },
      relations: ['user']
    });

    const results = [];
    for (const subscription of expiredSubscriptions) {
      await this.updateSubscriptionStatus(subscription.id, SubscriptionStatus.EXPIRED);
      results.push({
        id: subscription.id,
        userId: subscription.userId,
        userEmail: subscription.user?.email,
        endDate: subscription.endDate
      });
    }

    return {
      updated: results.length,
      subscriptions: results
    };
  }
}