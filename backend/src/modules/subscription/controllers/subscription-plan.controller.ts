import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { SubscriptionPlanService } from '../services/subscription-plan.service';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto, SubscriptionPlanResponseDto } from '../dto/subscription-plan.dto';

@Controller('subscription/plans')
export class SubscriptionPlanController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService
  ) {}

  /**
   * 获取所有可用的订阅计划（公开接口）
   */
  @Get()
  async getAvailablePlans(): Promise<SubscriptionPlanResponseDto[]> {
    const plans = await this.subscriptionPlanService.getAvailablePlans();
    return plans.map(plan => this.subscriptionPlanService.toResponseDto(plan));
  }

  /**
   * 获取所有订阅计划（管理员接口）
   */
  @Get('admin/all')
  @UseGuards(JwtAuthGuard, AdminGuard)
  async getAllPlans(): Promise<SubscriptionPlanResponseDto[]> {
    const plans = await this.subscriptionPlanService.getAllPlans();
    return plans.map(plan => this.subscriptionPlanService.toResponseDto(plan));
  }

  /**
   * 根据ID获取订阅计划
   */
  @Get(':id')
  async getPlanById(@Param('id') id: string): Promise<SubscriptionPlanResponseDto> {
    const plan = await this.subscriptionPlanService.getPlanById(id);
    return this.subscriptionPlanService.toResponseDto(plan);
  }

  /**
   * 创建订阅计划（管理员接口）
   */
  @Post()
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(HttpStatus.CREATED)
  async createPlan(@Body() createPlanDto: CreateSubscriptionPlanDto): Promise<SubscriptionPlanResponseDto> {
    const plan = await this.subscriptionPlanService.createPlan(createPlanDto);
    return this.subscriptionPlanService.toResponseDto(plan);
  }

  /**
   * 更新订阅计划（管理员接口）
   */
  @Put(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  async updatePlan(
    @Param('id') id: string,
    @Body() updatePlanDto: UpdateSubscriptionPlanDto
  ): Promise<SubscriptionPlanResponseDto> {
    console.log('Updating subscription plan:', { id, updatePlanDto });
    const plan = await this.subscriptionPlanService.updatePlan(id, updatePlanDto);
    return this.subscriptionPlanService.toResponseDto(plan);
  }

  /**
   * 删除订阅计划（管理员接口）
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePlan(@Param('id') id: string): Promise<void> {
    await this.subscriptionPlanService.deletePlan(id);
  }

  /**
   * 激活订阅计划（管理员接口）
   */
  @Put(':id/activate')
  @UseGuards(JwtAuthGuard, AdminGuard)
  async activatePlan(@Param('id') id: string): Promise<SubscriptionPlanResponseDto> {
    const plan = await this.subscriptionPlanService.activatePlan(id);
    return this.subscriptionPlanService.toResponseDto(plan);
  }

  /**
   * 停用订阅计划（管理员接口）
   */
  @Put(':id/deactivate')
  @UseGuards(JwtAuthGuard, AdminGuard)
  async deactivatePlan(@Param('id') id: string): Promise<SubscriptionPlanResponseDto> {
    const plan = await this.subscriptionPlanService.deactivatePlan(id);
    return this.subscriptionPlanService.toResponseDto(plan);
  }

  /**
   * 获取计划统计信息（管理员接口）
   */
  @Get(':id/stats')
  @UseGuards(JwtAuthGuard, AdminGuard)
  async getPlanStats(@Param('id') id: string) {
    return await this.subscriptionPlanService.getPlanStats(id);
  }
}