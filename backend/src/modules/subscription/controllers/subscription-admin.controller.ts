import { Controller, Get, UseGuards, Query, Param, Put, Body, Req, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { SubscriptionAdminService, SubscriptionListQuery } from '../services/subscription-admin.service';
import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionStatus } from '../entities/subscription.entity';

@ApiTags('管理员-订阅')
@Controller('admin/subscription')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class SubscriptionAdminController {
  constructor(
    private readonly subscriptionAdminService: SubscriptionAdminService,
    private readonly subscriptionService: SubscriptionService
  ) {}

  @Get('stats')
  @ApiOperation({ summary: '获取订阅统计数据' })
  async getSubscriptionStats() {
    return this.subscriptionAdminService.getSubscriptionStats();
  }

  @Get('activities')
  @ApiOperation({ summary: '获取订阅活动记录' })
  async getSubscriptionActivities(@Query('limit') limit?: number) {
    return this.subscriptionAdminService.getRecentActivities(limit || 10);
  }

  @Get('users')
  @ApiOperation({ summary: '获取用户订阅列表' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: SubscriptionStatus })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  async getUserSubscriptions(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('status') status?: SubscriptionStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const query: SubscriptionListQuery = {
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search,
      status,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    };

    return this.subscriptionAdminService.getUserSubscriptions(query);
  }

  @Get('users/:subscriptionId')
  @ApiOperation({ summary: '获取用户订阅详情' })
  @ApiParam({ name: 'subscriptionId', type: String })
  async getUserSubscriptionById(@Param('subscriptionId') subscriptionId: string) {
    const subscription = await this.subscriptionAdminService.getUserSubscriptionById(subscriptionId);
    if (!subscription) {
      throw new Error('订阅不存在');
    }
    return subscription;
  }

  @Put('users/:subscriptionId/status')
  @ApiOperation({ summary: '更新订阅状态' })
  @ApiParam({ name: 'subscriptionId', type: String })
  async updateSubscriptionStatus(
    @Param('subscriptionId') subscriptionId: string,
    @Body('status') status: SubscriptionStatus,
    @Req() req: any
  ) {
    const adminUserId = req.user?.id;
    const success = await this.subscriptionAdminService.updateSubscriptionStatus(
      subscriptionId,
      status,
      adminUserId
    );

    if (!success) {
      throw new Error('更新订阅状态失败');
    }

    return { success: true, message: '订阅状态已更新' };
  }

  @Put('users/:subscriptionId/extend')
  @ApiOperation({ summary: '延长订阅期限' })
  @ApiParam({ name: 'subscriptionId', type: String })
  async extendSubscription(
    @Param('subscriptionId') subscriptionId: string,
    @Body('extensionDays') extensionDays: number,
    @Req() req: any
  ) {
    const adminUserId = req.user?.id;
    const success = await this.subscriptionAdminService.extendSubscription(
      subscriptionId,
      extensionDays,
      adminUserId
    );

    if (!success) {
      throw new Error('延长订阅期限失败');
    }

    return { 
      success: true, 
      message: `订阅期限已延长 ${extensionDays} 天` 
    };
  }

  @Post('maintenance/update-expired')
  @ApiOperation({ summary: '手动更新过期订阅状态' })
  async updateExpiredSubscriptions() {
    const result = await this.subscriptionService.manualUpdateExpiredSubscriptions();
    return {
      success: true,
      message: `已更新 ${result.updated} 个过期订阅`,
      data: result
    };
  }
} 