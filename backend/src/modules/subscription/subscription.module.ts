import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionController } from './controllers/subscription.controller';
import { SubscriptionPlanController } from './controllers/subscription-plan.controller';
import { SubscriptionAdminController } from './controllers/subscription-admin.controller';
import { SubscriptionService } from './services/subscription.service';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionAdminService } from './services/subscription-admin.service';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { User } from '../user/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Subscription, SubscriptionPlan, User]),
  ],
  controllers: [SubscriptionController, SubscriptionPlanController, SubscriptionAdminController],
  providers: [SubscriptionService, SubscriptionPlanService, SubscriptionAdminService],
  exports: [SubscriptionService, SubscriptionPlanService, SubscriptionAdminService],
})
export class SubscriptionModule {} 