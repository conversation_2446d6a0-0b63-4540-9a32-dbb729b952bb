import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { SubscriptionPlan } from './subscription-plan.entity';
import { Order } from '../../payment/entities/order.entity';

export enum SubscriptionStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  TRIAL = 'trial'
}

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'userid', type: 'uuid', nullable: false })
  userId: string;

  @ManyToOne(() => User, user => user.subscriptions)
  @JoinColumn({ name: 'userid' })
  user: User;

  @Column({ name: 'plan_id', type: 'uuid', nullable: false })
  planId: string;

  @ManyToOne(() => SubscriptionPlan, plan => plan.subscriptions)
  @JoinColumn({ name: 'plan_id' })
  plan: SubscriptionPlan;

  @Column({ name: 'planname', nullable: false })
  planName: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'startdate', type: 'timestamp' })
  startDate: Date;

  @Column({ name: 'enddate', type: 'timestamp' })
  endDate: Date;

  @Column({ name: 'aicalllimit', type: 'integer' })
  aiCallLimit: number;

  @Column({ name: 'aicallused', type: 'integer', default: 0 })
  aiCallUsed: number;

  @Column({ name: 'isactive', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'enum', enum: SubscriptionStatus, default: SubscriptionStatus.ACTIVE })
  status: SubscriptionStatus;

  @Column({ name: 'stripe_subscription_id', nullable: true })
  stripeSubscriptionId?: string;

  @Column({ name: 'auto_renew', type: 'boolean', default: true })
  autoRenew: boolean;

  @Column({ name: 'trial_end_date', type: 'timestamp', nullable: true })
  trialEndDate?: Date;

  @Column({ name: 'cancelled_at', type: 'timestamp', nullable: true })
  cancelledAt?: Date;

  @Column({ name: 'next_billing_date', type: 'timestamp', nullable: true })
  nextBillingDate?: Date;

  @OneToMany(() => Order, order => order.subscription)
  orders: Order[];

  @CreateDateColumn({ name: 'createdat', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updatedat', type: 'timestamp' })
  updatedAt: Date;
} 