import { Is<PERSON>tring, <PERSON><PERSON><PERSON>ber, IsEnum, IsOptional, IsBoolean, IsArray, IsUUID, Min, Max, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { SubscriptionPlanType, SubscriptionPlanStatus } from '../entities/subscription-plan.entity';

export class CreateSubscriptionPlanDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price: number;

  @IsEnum(SubscriptionPlanType)
  type: SubscriptionPlanType;

  @IsNumber()
  @Min(1)
  durationDays: number;

  @IsNumber()
  @Min(0)
  aiCallLimit: number;

  @IsOptional()
  @ValidateIf((obj, value) => value !== null)
  @IsNumber()
  @Min(0)
  maxCourses?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(365)
  trialDays?: number;

  @IsOptional()
  @IsEnum(SubscriptionPlanStatus)
  status?: SubscriptionPlanStatus;

  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @IsOptional()
  @ValidateIf((obj, value) => value !== null)
  @IsString()
  stripePriceId?: string;
}

export class UpdateSubscriptionPlanDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  price?: number;

  @IsOptional()
  @IsEnum(SubscriptionPlanType)
  type?: SubscriptionPlanType;

  @IsOptional()
  @IsNumber()
  @Min(1)
  durationDays?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  aiCallLimit?: number;

  @IsOptional()
  @ValidateIf((obj, value) => value !== null)
  @IsNumber()
  @Min(0)
  maxCourses?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(365)
  trialDays?: number;

  @IsOptional()
  @IsEnum(SubscriptionPlanStatus)
  status?: SubscriptionPlanStatus;

  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @IsOptional()
  @ValidateIf((obj, value) => value !== null)
  @IsString()
  stripePriceId?: string;
}

export class SubscriptionPlanResponseDto {
  id: string;
  name: string;
  description?: string;
  price: number;
  type: SubscriptionPlanType;
  durationDays: number;
  aiCallLimit: number;
  maxCourses?: number;
  trialDays: number;
  status: SubscriptionPlanStatus;
  sortOrder: number;
  features: string[];
  stripePriceId?: string;
  createdAt: Date;
  updatedAt: Date;
}