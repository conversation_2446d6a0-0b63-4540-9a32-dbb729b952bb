import { IsString, <PERSON><PERSON><PERSON>ber, IsEnum, IsOptional, IsBoolean, IsUUID, IsDateString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { SubscriptionStatus } from '../entities/subscription.entity';

export class CreateSubscriptionDto {
  @IsUUID()
  planId: string;

  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @IsOptional()
  @IsString()
  stripeSubscriptionId?: string;
}

export class UpdateSubscriptionDto {
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(0)
  aiCallUsed?: number;

  @IsOptional()
  @IsDateString()
  trialEndDate?: string;

  @IsOptional()
  @IsDateString()
  cancelledAt?: string;

  @IsOptional()
  @IsDateString()
  nextBillingDate?: string;
}

export class SubscriptionResponseDto {
  id: string;
  userId: string;
  planId: string;
  planName: string;
  price: number;
  startDate: Date;
  endDate: Date;
  aiCallLimit: number;
  aiCallUsed: number;
  isActive: boolean;
  status: SubscriptionStatus;
  stripeSubscriptionId?: string;
  autoRenew: boolean;
  trialEndDate?: Date;
  cancelledAt?: Date;
  nextBillingDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  plan?: {
    id: string;
    name: string;
    description?: string;
    type: string;
    features: string[];
  };
}

export class SubscriptionUsageDto {
  aiCallsUsed: number;
  aiCallsLimit: number;
  aiCallsRemaining: number;
  usagePercentage: number;
  daysRemaining: number;
  isActive: boolean;
  status: SubscriptionStatus;
  renewalDate?: Date;
  trialEndDate?: Date;
}

export class CancelSubscriptionDto {
  @IsOptional()
  @IsBoolean()
  immediate?: boolean; // true: 立即取消, false: 到期后取消

  @IsOptional()
  @IsString()
  reason?: string;
}