{"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint/eslint-plugin"], "extends": ["plugin:@typescript-eslint/recommended"], "root": true, "env": {"node": true, "jest": true}, "rules": {"@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/explicit-module-boundary-types": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}