# SSL证书配置指南

## 1. 证书文件准备

请将以下SSL证书文件放置在此目录下：
- 证书文件（通常是 .pem 或 .crt 格式）
- 私钥文件（通常是 .key 格式）
- 证书链文件（如果有）

## 2. Nginx配置步骤

1. 将证书文件放置在此目录
2. 修改Nginx配置文件，添加以下配置：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;  # 替换为您的域名

    ssl_certificate /var/www/ai-learning/backend/certs/your-cert.pem;  # 替换为您的证书文件路径
    ssl_certificate_key /var/www/ai-learning/backend/certs/your-key.key;  # 替换为您的私钥文件路径

    # SSL配置优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HSTS配置（可选）
    add_header Strict-Transport-Security "max-age=63072000" always;

    # 其他配置保持不变...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    return 301 https://$server_name$request_uri;
}
```

3. 测试Nginx配置：
```bash
sudo nginx -t
```

4. 如果测试通过，重启Nginx：
```bash
sudo systemctl restart nginx
```

## 3. 证书更新

当证书需要更新时：
1. 将新的证书文件放置在此目录
2. 确保文件权限正确：`sudo chmod 644 *.pem *.crt`
3. 重启Nginx服务

## 4. 安全建议

1. 确保证书文件权限设置正确
2. 定期检查证书有效期
3. 配置自动更新机制（如使用certbot）
4. 保持备份文件