# 企业AI课堂后端项目

## 项目结构

```
backend/
├── src/                    # 源代码目录
│   ├── modules/           # 业务模块
│   │   ├── auth/         # 认证模块
│   │   ├── course/       # 课程模块
│   │   ├── user/         # 用户模块
│   │   └── ai/           # AI 服务模块
│   ├── common/           # 公共代码
│   ├── config/           # 配置文件
│   └── main.ts           # 应用入口
├── uploads/              # 上传文件目录
│   ├── images/          # 图片文件
│   ├── videos/          # 视频文件
│   └── audios/          # 音频文件
├── certs/               # 证书和密钥
├── .env.example         # 环境变量示例
├── .env.production      # 生产环境配置
├── nest-cli.json        # NestJS CLI 配置
├── tsconfig.json        # TypeScript 配置
└── package.json         # 项目依赖和脚本
```

## 开发指南

### 环境准备
1. Node.js >= 18.0.0
2. PNPM >= 8.0.0
3. PostgreSQL >= 14
4. Redis >= 6

### 安装依赖
```bash
pnpm install
```

### 开发服务器
```bash
pnpm dev
```

### 构建
```bash
pnpm build
```

### 生产环境运行
```bash
pnpm start:prod
```

## 技术栈
- NestJS
- TypeScript
- PostgreSQL
- Redis
- TypeORM
- JWT 认证
- WebSocket

## 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 遵循 SOLID 设计原则

## 模块说明

### 认证模块 (`/src/modules/auth`)
- JWT 认证
- 用户登录注册
- 权限控制

### 课程模块 (`/src/modules/course`)
- 课程管理
- 课程内容
- 学习进度

### 用户模块 (`/src/modules/user`)
- 用户管理
- 角色管理
- 权限管理

### AI 服务模块 (`/src/modules/ai`)
- 阿里云 DashScope 集成
- AI 对话服务
- AI 图像生成

## API 文档
项目使用 Swagger 自动生成 API 文档，开发环境下访问：
```
http://localhost:3001/api/docs
```

## 环境变量
请参考 `.env.example` 文件设置必要的环境变量：
- 数据库配置
- Redis 配置
- JWT 配置
- 阿里云配置
- 微信支付配置

## Docker 部署
项目包含 Dockerfile，可以直接构建 Docker 镜像：
```bash
docker build -t ai-classroom-backend .
```

## 数据库迁移
```bash
# 生成迁移
pnpm typeorm:migration:generate

# 运行迁移
pnpm typeorm:migration:run

# 回滚迁移
pnpm typeorm:migration:revert
```

## 测试
```bash
# 单元测试
pnpm test

# e2e 测试
pnpm test:e2e

# 测试覆盖率
pnpm test:cov
``` 