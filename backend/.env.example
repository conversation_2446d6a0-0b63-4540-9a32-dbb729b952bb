# 基础配置
NODE_ENV=production
PORT=3001

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=SoulTalkAI_clare
DB_PASSWORD=SoulTalkAI0428
DB_DATABASE=SoulTalkAI
DB_SYNCHRONIZE=false
DB_LOGGING=false

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# OSS配置
ALIYUN_ACCESS_KEY_ID=your_key_id
ALIYUN_ACCESS_KEY_SECRET=your_key_secret
OSS_ROLE_ARN=your_role_arn
OSS_BUCKET=your_bucket_name
OSS_REGION=oss-cn-shanghai
OSS_ENDPOINT=oss-cn-shanghai.aliyuncs.com

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_jwt_refresh_secret
JWT_REFRESH_EXPIRES_IN=30d

# 微信支付配置
WECHAT_PAY_APP_ID=your_app_id
WECHAT_PAY_MCH_ID=your_mch_id
WECHAT_PAY_API_V3_KEY=your_api_v3_key
WECHAT_PAY_SERIAL_NO=your_serial_no
WECHAT_NOTIFY_URL=your_notify_url

# Stripe支付配置
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_test_51RHh9RQLbVN3sXcIeUf8WOZuB78xVtEpHhXkSAjI7Qby8bAf3LcEwcAkme3yMozHQt9zPcRmSGdw8ME6KHyIlrin00laxvkibw
STRIPE_WEBHOOK_SECRET=whsec_e69ed774e1e9419f30ed6092bd26d061f7ac0c178b56836d6e9bc5d1174fc6ef

# OSS Configuration
OSS_POLICY={"Version":"1","Statement":[{"Effect":"Allow","Action":["oss:PutObject","oss:GetObject","oss:DeleteObject"],"Resource":["acs:oss:*:*:aimakebooks/*"]}]}

# 邮件服务配置
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your_password
MAIL_SECURE=false
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=SoulTalkAI